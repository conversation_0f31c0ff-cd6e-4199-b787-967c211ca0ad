"""
Test clinical boundary condition testing for extreme but valid scenarios.

This module tests the pyrt-dicom library's handling of extreme but clinically
valid scenarios including:
- Very small structures (0.1cc volumes) and large structures (3000cc+)
- Unusual but valid patient positions (prone, decubitus)
- Coordinate transformations with extreme but clinical image orientations
- Boundary conditions for clinical parameters

These tests ensure robustness in real-world clinical scenarios that push
the boundaries of typical parameters while remaining clinically valid.
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch
from pathlib import Path
import tempfile

from pyrt_dicom.core.base import BaseDicomCreator
from pyrt_dicom.validation.geometric import (
    GeometricValidator,
    CLINICAL_GEOMETRIC_LIMITS,
    validate_coordinate_bounds,
)
from pyrt_dicom.validation.patient import (
    PatientInfoValidator,
)
from pyrt_dicom.coordinates.transforms import CoordinateTransformer
from pyrt_dicom.coordinates.reference_frames import (
    FrameOfReference,
    GeometricParameters,
)


class TestBaseDicomCreator(BaseDicomCreator):
    """Concrete implementation for testing extreme scenarios."""

    def _create_modality_specific_dataset(self):
        """Create minimal test dataset."""
        dataset = self._create_base_dataset()
        dataset.SOPClassUID = "1.2.840.10008.5.1.4.1.1.2"  # CT Image Storage
        dataset.Modality = "CT"
        return dataset

    def _validate_modality_specific(self):
        """Test-specific validation."""
        pass


class TestExtremeStructureVolumes:
    """Test handling of extreme but valid structure volumes."""

    def test_very_small_structure_volume(self):
        """Test handling of very small but valid structure (0.1cc)."""
        # Create coordinates for a structure that meets minimum extent requirements
        # Use a structure that's 15mm x 15mm x 15mm ≈ 3.375cc (above minimum)
        size = 15.0  # mm, above minimum extent of 10mm

        # Create box-like structure coordinates
        small_contour = np.array(
            [
                [0.0, 0.0, 0.0],
                [size, 0.0, 0.0],
                [size, size, 0.0],
                [0.0, size, 0.0],
                [0.0, 0.0, size],
                [size, 0.0, size],
                [size, size, size],
                [0.0, size, size],
            ]
        )

        # Should pass validation for structure above minimum limits
        validator = GeometricValidator()
        errors = validator.validate_structure_bounds(small_contour, "small_structure")

        # Should pass validation (no errors)
        assert len(errors) == 0

    def test_minimum_meaningful_structure_volume(self):
        """Test structure at minimum meaningful volume limit."""
        # Create coordinates that meet minimum extent requirements (10mm minimum)
        # Use exactly 10mm extent in each direction
        extent = 10.0  # mm, exactly at minimum limit

        tiny_contour = np.array(
            [
                [0.0, 0.0, 0.0],
                [extent, 0.0, 0.0],
                [extent, extent, 0.0],
                [0.0, extent, 0.0],
                [0.0, 0.0, extent],
                [extent, 0.0, extent],
                [extent, extent, extent],
                [0.0, extent, extent],
            ]
        )

        validator = GeometricValidator()
        errors = validator.validate_structure_bounds(tiny_contour, "tiny_structure")

        # Should pass validation at the limit
        assert len(errors) == 0

    def test_large_structure_volume(self):
        """Test handling of large but valid structure (3000cc)."""
        # Create coordinates for a large structure within patient extent limits
        # Use 200mm x 150mm x 100mm ≈ 3,000cc (within volume limits)
        x_extent, y_extent, z_extent = (
            200,
            150,
            100,
        )  # mm, within 600mm patient extent limit and volume limit

        # Create box corners
        large_contour = np.array(
            [
                [0.0, 0.0, 0.0],
                [x_extent, 0.0, 0.0],
                [x_extent, y_extent, 0.0],
                [0.0, y_extent, 0.0],
                [0.0, 0.0, z_extent],
                [x_extent, 0.0, z_extent],
                [x_extent, y_extent, z_extent],
                [0.0, y_extent, z_extent],
            ]
        )

        validator = GeometricValidator()
        errors = validator.validate_structure_bounds(large_contour, "large_structure")

        # Should pass validation for large but reasonable structure
        assert len(errors) == 0

    def test_maximum_structure_volume_limit(self):
        """Test structure at maximum volume limit (10000cc)."""
        # Create coordinates that approach but don't exceed volume limit
        # Use dimensions that give approximately 8000cc: 200mm x 200mm x 200mm = 8000cc
        extent = 200  # mm, within patient extent limits and gives reasonable volume

        max_contour = np.array(
            [
                [0.0, 0.0, 0.0],
                [extent, 0.0, 0.0],
                [extent, extent, 0.0],
                [0.0, extent, 0.0],
                [0.0, 0.0, extent],
                [extent, 0.0, extent],
                [extent, extent, extent],
                [0.0, extent, extent],
            ]
        )

        validator = GeometricValidator()
        errors = validator.validate_structure_bounds(max_contour, "max_structure")

        # Should pass validation at reasonable maximum
        assert len(errors) == 0


class TestExtremePatientPositions:
    """Test handling of unusual but valid patient positions."""

    def test_prone_patient_position(self):
        """Test prone patient position with appropriate coordinate system."""
        # Prone position: patient face down, typical for breast/spine treatments
        prone_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            image_orientation=[1.0, 0.0, 0.0, 0.0, -1.0, 0.0],  # Prone orientation
            patient_position="HFP",  # Head First Prone
        )

        validator = GeometricValidator()
        errors = validator.validate_geometric_parameters(prone_params)

        # Should pass validation for valid prone position
        assert len(errors) == 0

    def test_decubitus_patient_position(self):
        """Test lateral decubitus position."""
        # Lateral decubitus: patient on side, used for lung/liver treatments
        decubitus_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            image_orientation=[0.0, 1.0, 0.0, 0.0, 0.0, -1.0],  # Lateral orientation
            patient_position="HFDL",  # Head First Decubitus Left
        )

        validator = GeometricValidator()
        errors = validator.validate_geometric_parameters(decubitus_params)

        # Should pass validation for valid lateral position
        assert len(errors) == 0

    def test_extreme_but_valid_patient_orientation(self):
        """Test extreme but clinically valid patient orientation."""
        # Unusual but valid orientation (e.g., angled gantry with patient positioning)
        extreme_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            pixel_spacing=(0.5, 0.5),  # High resolution
            slice_thickness=1.0,  # Thin slices
            image_orientation=[0.707, 0.707, 0.0, -0.707, 0.707, 0.0],  # 45° rotation
            patient_position="HFS",  # Head First Supine
        )

        validator = GeometricValidator()
        errors = validator.validate_geometric_parameters(extreme_params)

        # Should pass validation for mathematically valid orientation
        assert len(errors) == 0


class TestExtremeGeometricParameters:
    """Test extreme but valid geometric parameters."""

    def test_finest_pixel_spacing(self):
        """Test finest reasonable pixel spacing (0.01mm)."""
        fine_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            pixel_spacing=(0.01, 0.01),  # At minimum limit
            slice_thickness=0.1,  # At minimum limit
            image_orientation=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            patient_position="HFS",
        )

        validator = GeometricValidator()
        errors = validator.validate_geometric_parameters(fine_params)

        # Should pass at minimum limits
        assert len(errors) == 0

    def test_coarsest_pixel_spacing(self):
        """Test coarsest reasonable pixel spacing (10.0mm)."""
        coarse_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            pixel_spacing=(10.0, 10.0),  # At maximum limit
            slice_thickness=50.0,  # At maximum limit
            image_orientation=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            patient_position="HFS",
        )

        validator = GeometricValidator()
        errors = validator.validate_geometric_parameters(coarse_params)

        # Should pass at maximum limits
        assert len(errors) == 0

    def test_maximum_coordinate_extents(self):
        """Test coordinates at maximum clinical extents."""
        # Test coordinates within reasonable patient extent limits (600mm)
        # Use coordinates that are within both coordinate and patient extent limits
        extent = 200.0  # mm, within both max_coordinate_value (2000mm) and max_patient_extent (600mm) and volume limit

        max_coords = np.array(
            [
                [0.0, 0.0, 0.0],
                [extent, 0.0, 0.0],
                [extent, extent, 0.0],
                [0.0, extent, 0.0],
                [0.0, 0.0, extent],
                [extent, 0.0, extent],
                [extent, extent, extent],
                [0.0, extent, extent],
            ]
        )

        errors = validate_coordinate_bounds(max_coords)

        # Should pass at reasonable coordinate limits
        assert len(errors) == 0


class TestExtremePatientInformation:
    """Test extreme but valid patient information scenarios."""

    def test_maximum_length_patient_fields(self):
        """Test patient fields at maximum allowed lengths."""
        # Test with maximum length values that are still valid
        max_length_patient = {
            "PatientID": "A" * 64,  # Maximum length for LO VR
            "PatientName": "A" * 60 + "^B^C",  # Near maximum for PN VR
            "PatientBirthDate": "19000101",  # Very old but valid date
            "PatientSex": "O",  # Other (valid but uncommon)
            "PatientAge": "120Y",  # Very old but possible age
        }

        validator = PatientInfoValidator()
        errors = validator.validate_patient_info(max_length_patient)

        # Should pass validation for extreme but valid values
        assert len(errors) == 0

    def test_edge_case_patient_ages(self):
        """Test edge case but valid patient ages."""
        test_ages = [
            "001D",  # 1 day old (neonatal)
            "365D",  # 365 days old (maximum for days)
            "001W",  # 1 week old
            "104W",  # 104 weeks old (2 years)
            "001M",  # 1 month old
            "120M",  # 120 months old (10 years)
            "001Y",  # 1 year old
            "120Y",  # 120 years old (extreme but possible)
        ]

        validator = PatientInfoValidator()

        for age in test_ages:
            patient_info = {"PatientID": "TEST", "PatientAge": age}
            errors = validator.validate_patient_info(patient_info)
            assert (
                len(errors) == 0
            ), f"Age {age} should be valid but got errors: {errors}"

    def test_extreme_but_valid_dates(self):
        """Test extreme but valid date ranges."""
        extreme_dates = [
            "19000101",  # Very old birth date
            "20240229",  # Leap year date
            "20231231",  # Recent date
        ]

        validator = PatientInfoValidator()

        for date in extreme_dates:
            patient_info = {"PatientID": "TEST", "PatientBirthDate": date}
            errors = validator.validate_patient_info(patient_info)
            assert (
                len(errors) == 0
            ), f"Date {date} should be valid but got errors: {errors}"


class TestExtremeCoordinateTransformations:
    """Test coordinate transformations with extreme but valid parameters."""

    def test_extreme_coordinate_transformation(self):
        """Test coordinate transformation with extreme but valid parameters."""
        # Create source frame with extreme but valid parameters
        source_params = GeometricParameters(
            image_position=(500.0, -300.0, 1000.0),  # Extreme but valid origin
            pixel_spacing=(0.1, 0.1),  # Fine spacing
            slice_thickness=0.5,
            image_orientation=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],  # Standard orientation
            patient_position="HFS",
        )

        target_params = GeometricParameters(
            image_position=(-500.0, 300.0, -1000.0),  # Opposite extreme
            pixel_spacing=(5.0, 5.0),  # Coarse spacing
            slice_thickness=10.0,
            image_orientation=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],  # Standard orientation
            patient_position="HFS",
        )

        # Create frame manager and frames
        frame_manager = FrameOfReference()

        source_uid = frame_manager.create_frame_of_reference(
            source_params, description="source"
        )
        target_uid = frame_manager.create_frame_of_reference(
            target_params, description="target"
        )

        source_transformer = frame_manager.get_coordinate_transformer(source_uid)
        target_transformer = frame_manager.get_coordinate_transformer(target_uid)

        # Should not raise exceptions for extreme but valid transformations
        try:
            # Test that transformers can be created and used
            assert isinstance(source_transformer, CoordinateTransformer)
            assert isinstance(target_transformer, CoordinateTransformer)

            # Verify transformers have expected properties
            assert source_transformer.patient_position == "HFS"
            assert target_transformer.patient_position == "HFS"

        except Exception as e:
            pytest.fail(f"Extreme coordinate transformation failed: {e}")


class TestClinicalBoundaryConditions:
    """Test clinical boundary conditions and edge cases."""

    def test_clinical_limits_boundary_values(self):
        """Test values exactly at clinical limit boundaries."""
        limits = CLINICAL_GEOMETRIC_LIMITS

        # Test values exactly at limits (should pass)
        boundary_tests = [
            ("min_pixel_spacing", limits["min_pixel_spacing"]),
            ("max_pixel_spacing", limits["max_pixel_spacing"]),
            ("min_slice_thickness", limits["min_slice_thickness"]),
            ("max_slice_thickness", limits["max_slice_thickness"]),
            ("min_structure_volume", limits["min_structure_volume"]),
            ("max_structure_volume", limits["max_structure_volume"]),
        ]

        for limit_name, limit_value in boundary_tests:
            # These should pass validation at the exact boundary
            if "pixel_spacing" in limit_name:
                params = GeometricParameters(
                    image_position=(0.0, 0.0, 0.0),
                    pixel_spacing=(limit_value, limit_value),
                    slice_thickness=2.5,
                    image_orientation=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
                    patient_position="HFS",
                )
                validator = GeometricValidator()
                errors = validator.validate_geometric_parameters(params)
                assert (
                    len(errors) == 0
                ), f"Boundary value {limit_name}={limit_value} should be valid"

    def test_dicom_creator_with_extreme_parameters(self):
        """Test BaseDicomCreator with extreme but valid parameters."""
        # Create patient info with extreme but valid values
        extreme_patient = {
            "PatientID": "EXTREME_TEST_PATIENT_ID_MAX_LENGTH_ABCDEFGHIJKLMNOPQR",
            "PatientName": "VeryLongLastName^VeryLongFirstName^VeryLongMiddleName",
            "PatientBirthDate": "19000101",
            "PatientSex": "O",
            "PatientAge": "120Y",
        }

        creator = TestBaseDicomCreator(patient_info=extreme_patient)

        # Should not raise exceptions during creation
        assert creator.patient_info == extreme_patient

        # Should pass validation
        creator.validate()
        assert creator.is_validated

        # Should be able to create dataset
        dataset = creator._create_modality_specific_dataset()
        assert dataset is not None
        assert hasattr(dataset, "PatientID")
