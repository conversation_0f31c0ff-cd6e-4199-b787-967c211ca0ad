# Copyright (C) 2024 Pirate DICOM Contributors

"""
Tests for frame of reference management.

Tests Frame of Reference UID management, geometric consistency validation,
and coordinate system tracking for RT DICOM objects.
"""

import pytest
import numpy as np

from pyrt_dicom.coordinates.reference_frames import (
    FrameOfReference,
    FrameOfReferenceInfo,
    GeometricParameters,
)
from pyrt_dicom.coordinates.transforms import CoordinateTransformer
from pyrt_dicom.uid_generation.generators import DefaultUIDGenerator
from pyrt_dicom.utils.exceptions import CoordinateSystemError


class TestGeometricParameters:
    """Test geometric parameters validation."""
    
    def test_valid_parameters(self):
        """Test creation with valid parameters."""
        params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            patient_position="HFS",
        )
        
        assert params.image_position == (0.0, 0.0, 0.0)
        assert params.image_orientation == [1, 0, 0, 0, 1, 0]
        assert params.pixel_spacing == (1.0, 1.0)
        assert params.slice_thickness == 2.0
        assert params.patient_position == "HFS"
    
    def test_invalid_image_orientation_length(self):
        """Test validation of image orientation length."""
        with pytest.raises(CoordinateSystemError, match="6 elements"):
            GeometricParameters(
                image_position=(0.0, 0.0, 0.0),
                image_orientation=[1, 0, 0, 0, 1],  # Only 5 elements
                pixel_spacing=(1.0, 1.0),
                slice_thickness=2.0,
            )
    
    def test_invalid_patient_position(self):
        """Test validation of patient position."""
        with pytest.raises(CoordinateSystemError, match="Invalid patient position"):
            GeometricParameters(
                image_position=(0.0, 0.0, 0.0),
                image_orientation=[1, 0, 0, 0, 1, 0],
                pixel_spacing=(1.0, 1.0),
                slice_thickness=2.0,
                patient_position="INVALID",
            )
    
    def test_invalid_pixel_spacing_length(self):
        """Test validation of pixel spacing length."""
        with pytest.raises(CoordinateSystemError, match="2 elements"):
            GeometricParameters(
                image_position=(0.0, 0.0, 0.0),
                image_orientation=[1, 0, 0, 0, 1, 0],
                pixel_spacing=(1.0,),  # Only 1 element
                slice_thickness=2.0,
            )
    
    def test_invalid_slice_thickness(self):
        """Test validation of slice thickness."""
        with pytest.raises(CoordinateSystemError, match="must be positive"):
            GeometricParameters(
                image_position=(0.0, 0.0, 0.0),
                image_orientation=[1, 0, 0, 0, 1, 0],
                pixel_spacing=(1.0, 1.0),
                slice_thickness=-1.0,  # Negative
            )
    
    def test_invalid_pixel_spacing_values(self):
        """Test validation of pixel spacing values."""
        with pytest.raises(CoordinateSystemError, match="must be positive"):
            GeometricParameters(
                image_position=(0.0, 0.0, 0.0),
                image_orientation=[1, 0, 0, 0, 1, 0],
                pixel_spacing=(0.0, 1.0),  # Zero value
                slice_thickness=2.0,
            )


class TestFrameOfReferenceInfo:
    """Test FrameOfReferenceInfo functionality."""
    
    def test_initialization(self):
        """Test basic initialization."""
        params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        frame_info = FrameOfReferenceInfo(
            uid="*******.5",
            geometric_parameters=params,
            description="Test frame",
        )
        
        assert frame_info.uid == "*******.5"
        assert frame_info.geometric_parameters == params
        assert frame_info.description == "Test frame"
        assert len(frame_info.associated_objects) == 0
    
    def test_add_associated_object(self):
        """Test adding associated objects."""
        params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        frame_info = FrameOfReferenceInfo("*******.5", params)
        
        frame_info.add_associated_object("series1", "CT")
        frame_info.add_associated_object("series2", "RTSTRUCT")
        
        assert "CT:series1" in frame_info.associated_objects
        assert "RTSTRUCT:series2" in frame_info.associated_objects
        assert len(frame_info.associated_objects) == 2
    
    def test_get_associated_objects_by_type(self):
        """Test retrieving associated objects by type."""
        params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        frame_info = FrameOfReferenceInfo("*******.5", params)
        
        frame_info.add_associated_object("series1", "CT")
        frame_info.add_associated_object("series2", "CT")
        frame_info.add_associated_object("series3", "RTSTRUCT")
        
        ct_objects = frame_info.get_associated_objects_by_type("CT")
        struct_objects = frame_info.get_associated_objects_by_type("RTSTRUCT")
        dose_objects = frame_info.get_associated_objects_by_type("RTDOSE")
        
        assert set(ct_objects) == {"series1", "series2"}
        assert struct_objects == ["series3"]
        assert dose_objects == []


class TestFrameOfReference:
    """Test FrameOfReference management functionality."""
    
    def test_initialization(self):
        """Test basic initialization."""
        frame_manager = FrameOfReference()
        
        assert len(frame_manager.list_frames()) == 0
        assert frame_manager.get_primary_frame_uid() is None
    
    def test_create_frame_of_reference(self):
        """Test creating a frame of reference."""
        frame_manager = FrameOfReference()
        
        params = GeometricParameters(
            image_position=(100.0, 200.0, 300.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.5, 1.5),
            slice_thickness=3.0,
            patient_position="HFS",
        )
        
        frame_uid = frame_manager.create_frame_of_reference(
            geometric_parameters=params,
            description="Test CT frame",
        )
        
        assert frame_uid is not None
        assert len(frame_uid) > 0
        assert frame_uid in frame_manager.list_frames()
        assert frame_manager.get_primary_frame_uid() == frame_uid
    
    def test_create_frame_with_specific_uid(self):
        """Test creating frame with specific UID."""
        frame_manager = FrameOfReference()
        
        params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        specific_uid = "*******.*******.9.10"
        frame_uid = frame_manager.create_frame_of_reference(
            geometric_parameters=params,
            uid=specific_uid,
        )
        
        assert frame_uid == specific_uid
        assert specific_uid in frame_manager.list_frames()
    
    def test_create_frame_duplicate_uid_error(self):
        """Test error when creating frame with duplicate UID."""
        frame_manager = FrameOfReference()
        
        params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        uid = "duplicate.uid"
        frame_manager.create_frame_of_reference(params, uid=uid)
        
        with pytest.raises(CoordinateSystemError, match="already exists"):
            frame_manager.create_frame_of_reference(params, uid=uid)
    
    def test_get_frame_of_reference(self):
        """Test retrieving frame of reference."""
        frame_manager = FrameOfReference()
        
        params = GeometricParameters(
            image_position=(10.0, 20.0, 30.0),
            image_orientation=[-1, 0, 0, 0, -1, 0],
            pixel_spacing=(2.0, 2.5),
            slice_thickness=4.0,
            patient_position="HFP",
        )
        
        frame_uid = frame_manager.create_frame_of_reference(
            geometric_parameters=params,
            description="HFP CT frame",
        )
        
        retrieved_frame = frame_manager.get_frame_of_reference(frame_uid)
        
        assert retrieved_frame.uid == frame_uid
        assert retrieved_frame.description == "HFP CT frame"
        assert retrieved_frame.geometric_parameters.patient_position == "HFP"
        assert retrieved_frame.geometric_parameters.image_position == (10.0, 20.0, 30.0)
    
    def test_get_frame_not_found_error(self):
        """Test error when retrieving non-existent frame."""
        frame_manager = FrameOfReference()
        
        with pytest.raises(CoordinateSystemError, match="not found"):
            frame_manager.get_frame_of_reference("nonexistent.uid")
    
    def test_associate_object(self):
        """Test associating objects with frame."""
        frame_manager = FrameOfReference()
        
        params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        frame_uid = frame_manager.create_frame_of_reference(params)
        
        frame_manager.associate_object(frame_uid, "ct.series.uid", "CT")
        frame_manager.associate_object(frame_uid, "struct.series.uid", "RTSTRUCT")
        
        frame_info = frame_manager.get_frame_of_reference(frame_uid)
        
        assert "CT:ct.series.uid" in frame_info.associated_objects
        assert "RTSTRUCT:struct.series.uid" in frame_info.associated_objects
        assert len(frame_info.associated_objects) == 2
    
    def test_associate_object_invalid_frame(self):
        """Test error when associating object with invalid frame."""
        frame_manager = FrameOfReference()
        
        with pytest.raises(CoordinateSystemError, match="not found"):
            frame_manager.associate_object("invalid.uid", "object.uid", "CT")
    
    def test_validate_geometric_consistency_success(self):
        """Test successful geometric consistency validation."""
        frame_manager = FrameOfReference()
        
        # Create original parameters
        original_params = GeometricParameters(
            image_position=(100.0, 200.0, 300.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.5, 1.5),
            slice_thickness=3.0,
            patient_position="HFS",
        )
        
        frame_uid = frame_manager.create_frame_of_reference(original_params)
        
        # Create consistent parameters (slight variation within tolerance)
        consistent_params = GeometricParameters(
            image_position=(100.0, 200.0, 300.0),
            image_orientation=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            pixel_spacing=(1.5, 1.5),
            slice_thickness=3.0,
            patient_position="HFS",
        )
        
        # Should not raise exception
        result = frame_manager.validate_geometric_consistency(
            frame_uid, consistent_params
        )
        assert result is True
    
    def test_validate_geometric_consistency_patient_position_mismatch(self):
        """Test geometric consistency validation with patient position mismatch."""
        frame_manager = FrameOfReference()
        
        original_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            patient_position="HFS",
        )
        
        frame_uid = frame_manager.create_frame_of_reference(original_params)
        
        inconsistent_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            patient_position="HFP",  # Different patient position
        )
        
        with pytest.raises(CoordinateSystemError, match="Patient position mismatch"):
            frame_manager.validate_geometric_consistency(
                frame_uid, inconsistent_params
            )
    
    def test_validate_geometric_consistency_orientation_mismatch(self):
        """Test geometric consistency validation with orientation mismatch."""
        frame_manager = FrameOfReference()
        
        original_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        frame_uid = frame_manager.create_frame_of_reference(original_params)
        
        inconsistent_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[-1, 0, 0, 0, -1, 0],  # Different orientation
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        with pytest.raises(CoordinateSystemError, match="Image orientation mismatch"):
            frame_manager.validate_geometric_consistency(
                frame_uid, inconsistent_params
            )
    
    def test_validate_geometric_consistency_spacing_mismatch(self):
        """Test geometric consistency validation with pixel spacing mismatch."""
        frame_manager = FrameOfReference()
        
        original_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        frame_uid = frame_manager.create_frame_of_reference(original_params)
        
        inconsistent_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(2.0, 2.0),  # Different pixel spacing
            slice_thickness=2.0,
        )
        
        with pytest.raises(CoordinateSystemError, match="Pixel spacing mismatch"):
            frame_manager.validate_geometric_consistency(
                frame_uid, inconsistent_params
            )
    
    def test_get_coordinate_transformer(self):
        """Test getting coordinate transformer for frame."""
        frame_manager = FrameOfReference()
        
        params = GeometricParameters(
            image_position=(50.0, 100.0, 150.0),
            image_orientation=[-1, 0, 0, 0, -1, 0],
            pixel_spacing=(1.25, 1.25),
            slice_thickness=2.5,
            patient_position="HFP",
        )
        
        frame_uid = frame_manager.create_frame_of_reference(params)
        
        transformer = frame_manager.get_coordinate_transformer(frame_uid)
        
        assert isinstance(transformer, CoordinateTransformer)
        assert transformer.patient_position == "HFP"
        assert transformer.image_orientation == [-1, 0, 0, 0, -1, 0]
        assert transformer.pixel_spacing == (1.25, 1.25)
        assert transformer.slice_thickness == 2.5
        assert transformer.image_position == (50.0, 100.0, 150.0)
    
    def test_primary_frame_management(self):
        """Test primary frame UID management."""
        frame_manager = FrameOfReference()
        
        params1 = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        params2 = GeometricParameters(
            image_position=(10.0, 20.0, 30.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.5, 1.5),
            slice_thickness=3.0,
        )
        
        # First frame becomes primary automatically
        frame_uid1 = frame_manager.create_frame_of_reference(params1)
        assert frame_manager.get_primary_frame_uid() == frame_uid1
        
        # Second frame doesn't change primary
        frame_uid2 = frame_manager.create_frame_of_reference(params2)
        assert frame_manager.get_primary_frame_uid() == frame_uid1
        
        # Manually set primary
        frame_manager.set_primary_frame_uid(frame_uid2)
        assert frame_manager.get_primary_frame_uid() == frame_uid2
    
    def test_set_primary_frame_invalid_uid(self):
        """Test error when setting primary frame with invalid UID."""
        frame_manager = FrameOfReference()
        
        with pytest.raises(CoordinateSystemError, match="not found"):
            frame_manager.set_primary_frame_uid("invalid.uid")
    
    def test_get_frame_summary(self):
        """Test getting frame summary information."""
        frame_manager = FrameOfReference()
        
        params = GeometricParameters(
            image_position=(25.0, 50.0, 75.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.2, 1.4),
            slice_thickness=2.8,
            patient_position="HFS",
        )
        
        frame_uid = frame_manager.create_frame_of_reference(
            params, description="Test frame for summary"
        )
        
        # Associate some objects
        frame_manager.associate_object(frame_uid, "ct1", "CT")
        frame_manager.associate_object(frame_uid, "ct2", "CT") 
        frame_manager.associate_object(frame_uid, "struct1", "RTSTRUCT")
        
        summary = frame_manager.get_frame_summary(frame_uid)
        
        assert summary["uid"] == frame_uid
        assert summary["description"] == "Test frame for summary"
        assert summary["patient_position"] == "HFS"
        assert summary["image_position"] == (25.0, 50.0, 75.0)
        assert summary["pixel_spacing"] == (1.2, 1.4)
        assert summary["slice_thickness"] == 2.8
        assert summary["associated_objects"] == 3
        assert summary["object_types"]["CT"] == 2
        assert summary["object_types"]["RTSTRUCT"] == 1
        assert summary["object_types"]["RTDOSE"] == 0
        assert summary["object_types"]["RTPLAN"] == 0
        assert summary["is_primary"] is True
    
    def test_validate_frame_consistency(self):
        """Test frame consistency validation."""
        frame_manager = FrameOfReference()
        
        # Test empty frame manager
        warnings = frame_manager.validate_frame_consistency()
        assert "No frames of reference defined" in warnings
        
        # Create frame without associating objects
        params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        frame_uid = frame_manager.create_frame_of_reference(params)
        
        warnings = frame_manager.validate_frame_consistency()
        assert any("no associated objects" in warning for warning in warnings)
        
        # Associate an object
        frame_manager.associate_object(frame_uid, "test.object", "CT")
        
        warnings = frame_manager.validate_frame_consistency()
        assert not any("no associated objects" in warning for warning in warnings)