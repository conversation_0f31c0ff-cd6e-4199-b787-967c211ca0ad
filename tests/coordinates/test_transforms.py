# Copyright (C) 2024 Pirate DICOM Contributors

"""
Tests for coordinate transformation utilities.

Tests coordinate transformations, patient position validation,
and geometric accuracy for clinical applications.
"""

import pytest
import numpy as np

from pyrt_dicom.coordinates.transforms import (
    CoordinateTransformer,
    DICOM_PATIENT_ORIENTATIONS,
    IMAGE_ORIENTATION_MAP,
    dicom_to_patient_coordinates,
    patient_to_dicom_coordinates,
    transform_image_orientation,
    validate_patient_position,
)
from pyrt_dicom.utils.exceptions import CoordinateSystemError


class TestCoordinateTransformer:
    """Test coordinate transformer functionality."""
    
    def test_initialization_default(self):
        """Test default initialization."""
        transformer = CoordinateTransformer()
        
        assert transformer.patient_position == "HFS"
        assert transformer.image_orientation == IMAGE_ORIENTATION_MAP["HFS"]
        assert transformer.pixel_spacing is None
        assert transformer.slice_thickness is None
        assert transformer.image_position == (0.0, 0.0, 0.0)
    
    def test_initialization_custom_parameters(self):
        """Test initialization with custom parameters."""
        transformer = CoordinateTransformer(
            patient_position="HFP",
            pixel_spacing=(1.5, 1.5),
            slice_thickness=3.0,
            image_position=(100.0, 200.0, 300.0),
        )
        
        assert transformer.patient_position == "HFP"
        assert transformer.image_orientation == IMAGE_ORIENTATION_MAP["HFP"]
        assert transformer.pixel_spacing == (1.5, 1.5)
        assert transformer.slice_thickness == 3.0
        assert transformer.image_position == (100.0, 200.0, 300.0)
    
    def test_invalid_patient_position(self):
        """Test initialization with invalid patient position."""
        with pytest.raises(CoordinateSystemError, match="Invalid patient position"):
            CoordinateTransformer(patient_position="INVALID")
    
    def test_invalid_image_orientation_length(self):
        """Test initialization with wrong image orientation length."""
        with pytest.raises(CoordinateSystemError, match="6 elements"):
            CoordinateTransformer(image_orientation=[1, 0, 0, 0, 1])  # Only 5 elements
    
    def test_invalid_image_orientation_not_normalized(self):
        """Test initialization with non-normalized orientation vectors."""
        with pytest.raises(CoordinateSystemError, match="normalized"):
            CoordinateTransformer(image_orientation=[2, 0, 0, 0, 1, 0])  # Row not normalized
    
    def test_invalid_image_orientation_not_orthogonal(self):
        """Test initialization with non-orthogonal orientation vectors."""
        with pytest.raises(CoordinateSystemError, match="orthogonal"):
            CoordinateTransformer(image_orientation=[1, 0, 0, 1, 0, 0])  # Not orthogonal
    
    def test_dicom_to_patient_transformation(self):
        """Test DICOM to patient coordinate transformation."""
        transformer = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(100.0, 200.0, 300.0),
        )
        
        # Test single point transformation
        dicom_coords = [10, 20, 5]  # (row, column, slice)
        patient_coords = transformer.dicom_to_patient(dicom_coords)
        
        expected = np.array([110.0, 220.0, 310.0])  # HFS: [1,0,0,0,1,0]
        np.testing.assert_allclose(patient_coords, expected, atol=1e-6)
    
    def test_dicom_to_patient_multiple_points(self):
        """Test DICOM to patient transformation with multiple points."""
        transformer = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(2.0, 3.0),
            slice_thickness=4.0,
            image_position=(0.0, 0.0, 0.0),
        )
        
        dicom_coords = [[0, 0, 0], [1, 1, 1], [10, 20, 5]]
        patient_coords = transformer.dicom_to_patient(dicom_coords)
        
        expected = np.array([
            [0.0, 0.0, 0.0],        # Origin
            [2.0, 3.0, 4.0],        # Single step in each direction  
            [20.0, 60.0, 20.0]      # Multiple steps
        ])
        np.testing.assert_allclose(patient_coords, expected, atol=1e-6)
    
    def test_patient_to_dicom_transformation(self):
        """Test patient to DICOM coordinate transformation."""
        transformer = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(2.0, 3.0),
            slice_thickness=4.0,
            image_position=(10.0, 20.0, 30.0),
        )
        
        # Test round-trip transformation
        original_dicom = [5, 10, 2]
        patient_coords = transformer.dicom_to_patient(original_dicom)
        recovered_dicom = transformer.patient_to_dicom(patient_coords)
        
        np.testing.assert_allclose(recovered_dicom, original_dicom, atol=1e-10)
    
    def test_transformation_without_geometric_params(self):
        """Test transformation fails without required geometric parameters."""
        transformer = CoordinateTransformer()  # No pixel spacing or slice thickness
        
        with pytest.raises(CoordinateSystemError, match="Pixel spacing and slice thickness"):
            transformer.dicom_to_patient([0, 0, 0])
    
    def test_transformation_matrix(self):
        """Test 4x4 transformation matrix generation."""
        transformer = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(1.0, 2.0),
            slice_thickness=3.0,
            image_position=(10.0, 20.0, 30.0),
        )
        
        matrix = transformer.get_transformation_matrix()
        
        assert matrix.shape == (4, 4)
        
        # Check translation part
        np.testing.assert_allclose(matrix[:3, 3], [10.0, 20.0, 30.0])
        
        # Check rotation/scaling part for HFS
        expected_rotation = np.array([
            [1.0, 0.0, 0.0],  # Row direction * pixel_spacing[0]
            [0.0, 2.0, 0.0],  # Column direction * pixel_spacing[1]
            [0.0, 0.0, 3.0],  # Slice direction * slice_thickness
        ])
        np.testing.assert_allclose(matrix[:3, :3], expected_rotation)
    
    def test_different_patient_positions(self):
        """Test transformations for different patient positions."""
        positions_to_test = ["HFS", "HFP", "FFS", "FFP"]
        
        for position in positions_to_test:
            transformer = CoordinateTransformer(
                patient_position=position,
                pixel_spacing=(1.0, 1.0),
                slice_thickness=1.0,
                image_position=(0.0, 0.0, 0.0),
            )
            
            # Test round-trip transformation
            original_dicom = [10, 20, 5]
            patient_coords = transformer.dicom_to_patient(original_dicom)
            recovered_dicom = transformer.patient_to_dicom(patient_coords)
            
            np.testing.assert_allclose(
                recovered_dicom, original_dicom, atol=1e-10,
                err_msg=f"Round-trip failed for position {position}"
            )


class TestPatientPositionValidation:
    """Test patient position validation functions."""
    
    def test_validate_patient_position_valid(self):
        """Test validation with valid patient position and orientation."""
        # Should not raise exception
        validate_patient_position("HFS", IMAGE_ORIENTATION_MAP["HFS"])
        validate_patient_position("HFP", IMAGE_ORIENTATION_MAP["HFP"])
    
    def test_validate_patient_position_invalid_position(self):
        """Test validation with invalid patient position."""
        with pytest.raises(CoordinateSystemError, match="Invalid patient position"):
            validate_patient_position("INVALID", [1, 0, 0, 0, 1, 0])
    
    def test_validate_patient_position_mismatch(self):
        """Test validation with mismatched position and orientation."""
        with pytest.raises(CoordinateSystemError, match="does not match"):
            validate_patient_position("HFS", IMAGE_ORIENTATION_MAP["HFP"])


class TestImageOrientationTransform:
    """Test image orientation transformation functions."""
    
    def test_transform_image_orientation_valid(self):
        """Test transformation between valid patient positions."""
        result = transform_image_orientation("HFS", "HFP")
        expected = IMAGE_ORIENTATION_MAP["HFP"]
        assert result == expected
    
    def test_transform_image_orientation_invalid_from(self):
        """Test transformation with invalid source position."""
        with pytest.raises(CoordinateSystemError, match="No image orientation mapping"):
            transform_image_orientation("INVALID", "HFS")
    
    def test_transform_image_orientation_invalid_to(self):
        """Test transformation with invalid target position."""
        with pytest.raises(CoordinateSystemError, match="No image orientation mapping"):
            transform_image_orientation("HFS", "INVALID")


class TestConvenienceFunctions:
    """Test convenience functions for coordinate transformation."""
    
    def test_dicom_to_patient_coordinates_function(self):
        """Test convenience function for DICOM to patient transformation."""
        result = dicom_to_patient_coordinates(
            dicom_coords=[10, 20, 5],
            patient_position="HFS",
            pixel_spacing=(2.0, 3.0),
            slice_thickness=4.0,
            image_position=(100.0, 200.0, 300.0),
        )
        
        expected = np.array([120.0, 260.0, 320.0])  # HFS transformation
        np.testing.assert_allclose(result, expected, atol=1e-6)
    
    def test_patient_to_dicom_coordinates_function(self):
        """Test convenience function for patient to DICOM transformation."""
        # First convert DICOM to patient
        dicom_coords = [5, 10, 2]
        patient_coords = dicom_to_patient_coordinates(
            dicom_coords=dicom_coords,
            patient_position="HFS",
            pixel_spacing=(1.5, 2.0),
            slice_thickness=2.5,
            image_position=(50.0, 100.0, 150.0),
        )
        
        # Then convert back to DICOM
        recovered_dicom = patient_to_dicom_coordinates(
            patient_coords=patient_coords,
            patient_position="HFS",
            pixel_spacing=(1.5, 2.0),
            slice_thickness=2.5,
            image_position=(50.0, 100.0, 150.0),
        )
        
        np.testing.assert_allclose(recovered_dicom, dicom_coords, atol=1e-10)


class TestGeometricAccuracy:
    """Test geometric accuracy requirements for clinical applications."""
    
    def test_submillimeter_accuracy(self):
        """Test that transformations maintain sub-millimeter accuracy."""
        transformer = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(0.5, 0.5),
            slice_thickness=1.0,
            image_position=(0.0, 0.0, 0.0),
        )
        
        # Test with clinical-scale coordinates
        test_points = [
            [0, 0, 0],          # Origin
            [256, 256, 100],    # Typical CT dimensions
            [512, 512, 200],    # Large CT dimensions
        ]
        
        for point in test_points:
            # Round-trip transformation
            patient_coords = transformer.dicom_to_patient(point)
            recovered_dicom = transformer.patient_to_dicom(patient_coords)
            
            # Check sub-millimeter accuracy (< 0.1mm)
            difference = np.linalg.norm(np.array(recovered_dicom) - np.array(point))
            assert difference < 0.0001, f"Accuracy error {difference:.6f} for point {point}"
    
    def test_clinical_coordinate_ranges(self):
        """Test transformations with clinical coordinate ranges."""
        transformer = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(1.25, 1.25),  # Common CT pixel spacing
            slice_thickness=2.5,         # Common CT slice thickness
            image_position=(-200.0, -300.0, -500.0),  # Typical CT positioning
        )
        
        # Test with typical clinical ranges
        clinical_ranges = [
            [0, 0, 0],              # Image origin
            [128, 128, 50],         # Half-way through typical volume
            [256, 256, 100],        # Full typical CT volume
            [400, 400, 150],        # Large patient/high resolution
        ]
        
        for dicom_point in clinical_ranges:
            # Transform to patient coordinates
            patient_coords = transformer.dicom_to_patient(dicom_point)
            
            # Verify patient coordinates are in reasonable clinical range
            for coord in patient_coords:
                assert -1000 <= coord <= 1000, f"Patient coordinate {coord} outside clinical range"
            
            # Verify round-trip accuracy
            recovered = transformer.patient_to_dicom(patient_coords)
            np.testing.assert_allclose(
                recovered, dicom_point, atol=1e-10,
                err_msg=f"Round-trip failed for {dicom_point}"
            )


class TestDicomPatientOrientations:
    """Test DICOM patient orientation constants."""
    
    def test_orientation_definitions_complete(self):
        """Test that all patient orientations have definitions."""
        assert len(DICOM_PATIENT_ORIENTATIONS) == 17  # All DICOM positions
        
        # Check specific required orientations
        required_orientations = ["HFS", "HFP", "FFS", "FFP", "SITTING"]
        for orientation in required_orientations:
            assert orientation in DICOM_PATIENT_ORIENTATIONS
            assert isinstance(DICOM_PATIENT_ORIENTATIONS[orientation], str)
            assert len(DICOM_PATIENT_ORIENTATIONS[orientation]) > 0
    
    def test_image_orientation_map_coverage(self):
        """Test that common patient positions have image orientation mappings."""
        common_orientations = ["HFS", "HFP", "FFS", "FFP", "HFDL", "HFDR", "FFDL", "FFDR"]
        
        for orientation in common_orientations:
            assert orientation in IMAGE_ORIENTATION_MAP
            assert len(IMAGE_ORIENTATION_MAP[orientation]) == 6
            
            # Verify orientation vectors are normalized and orthogonal
            row_cosines = np.array(IMAGE_ORIENTATION_MAP[orientation][:3])
            col_cosines = np.array(IMAGE_ORIENTATION_MAP[orientation][3:])
            
            assert abs(np.linalg.norm(row_cosines) - 1.0) < 1e-6
            assert abs(np.linalg.norm(col_cosines) - 1.0) < 1e-6
            assert abs(np.dot(row_cosines, col_cosines)) < 1e-6