"""
Test geometric parameter bounds validation functionality.

Tests enhanced geometric validation including:
- Coordinate transformation input range validation
- Clinical limits for pixel spacing and slice thickness
- Image orientation and position parameter validation
- Edge cases and clinical scenarios
"""

import pytest
import numpy as np

from pyrt_dicom.validation.geometric import (
    validate_coordinate_transformation_inputs,
    validate_image_orientation_and_position,
    CLINICAL_GEOMETRIC_LIMITS,
)


class TestCoordinateTransformationValidation:
    """Test coordinate transformation input validation."""

    def test_valid_coordinate_inputs(self):
        """Test valid coordinate transformation inputs."""
        # Valid 3D coordinates within clinical range
        coords = np.array(
            [
                [100.0, 200.0, 300.0],
                [-100.0, -200.0, -300.0],
                [0.0, 0.0, 0.0],
            ]
        )

        errors = validate_coordinate_transformation_inputs(coords)
        assert len(errors) == 0

    def test_empty_coordinates(self):
        """Test empty coordinates array."""
        coords = np.array([])

        errors = validate_coordinate_transformation_inputs(coords)
        assert len(errors) == 1
        assert "empty" in errors[0]

    def test_invalid_coordinate_shape(self):
        """Test invalid coordinate array shapes."""
        # Wrong number of dimensions
        coords_1d = np.array([1, 2, 3])
        errors = validate_coordinate_transformation_inputs(coords_1d)
        assert len(errors) == 1
        assert "N x 3 array" in errors[0]

        # Wrong number of columns
        coords_2col = np.array([[1, 2], [3, 4]])
        errors = validate_coordinate_transformation_inputs(coords_2col)
        assert len(errors) == 1
        assert "N x 3 array" in errors[0]

    def test_coordinates_exceed_range(self):
        """Test coordinates exceeding clinical range."""
        max_coord = CLINICAL_GEOMETRIC_LIMITS["max_coordinate_value"]

        # Coordinates exceeding maximum range
        coords = np.array(
            [
                [max_coord + 100, 0.0, 0.0],
                [0.0, max_coord + 100, 0.0],
                [0.0, 0.0, max_coord + 100],
            ]
        )

        errors = validate_coordinate_transformation_inputs(coords)
        assert len(errors) == 1
        assert "exceed maximum range" in errors[0]
        assert f"{max_coord}mm" in errors[0]

    def test_valid_transformation_matrix(self):
        """Test valid transformation matrix."""
        coords = np.array([[100.0, 200.0, 300.0]])

        # Valid 4x4 transformation matrix (identity)
        transform = np.eye(4)

        errors = validate_coordinate_transformation_inputs(coords, transform)
        assert len(errors) == 0

    def test_invalid_transformation_matrix_shape(self):
        """Test invalid transformation matrix shape."""
        coords = np.array([[100.0, 200.0, 300.0]])

        # Wrong shape
        transform = np.eye(3)  # Should be 4x4

        errors = validate_coordinate_transformation_inputs(coords, transform)
        assert len(errors) == 1
        assert "must be 4x4" in errors[0]

    def test_singular_transformation_matrix(self):
        """Test singular transformation matrix."""
        coords = np.array([[100.0, 200.0, 300.0]])

        # Singular matrix (determinant = 0)
        transform = np.zeros((4, 4))

        errors = validate_coordinate_transformation_inputs(coords, transform)
        assert len(errors) >= 1  # May have multiple errors (singular + scaling)
        assert any(
            "singular" in error and "determinant near zero" in error for error in errors
        )

    def test_unusual_scaling_transformation(self):
        """Test transformation matrix with unusual scaling."""
        coords = np.array([[100.0, 200.0, 300.0]])

        # Matrix with large scaling factor
        transform = np.eye(4)
        transform[:3, :3] *= 10.0  # 10x scaling

        errors = validate_coordinate_transformation_inputs(coords, transform)
        assert len(errors) == 1
        assert "unusual scaling" in errors[0]
        assert "determinant" in errors[0]


class TestImageOrientationAndPositionValidation:
    """Test image orientation and position validation."""

    def test_valid_axial_orientation(self):
        """Test valid axial image orientation."""
        # Standard axial orientation (HFS)
        orientation = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 0

    def test_valid_sagittal_orientation(self):
        """Test valid sagittal image orientation."""
        # Standard sagittal orientation
        orientation = [0.0, 1.0, 0.0, 0.0, 0.0, -1.0]
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 0

    def test_valid_coronal_orientation(self):
        """Test valid coronal image orientation."""
        # Standard coronal orientation
        orientation = [1.0, 0.0, 0.0, 0.0, 0.0, -1.0]
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 0

    def test_invalid_orientation_length(self):
        """Test invalid image orientation length."""
        # Wrong number of elements
        orientation = [1.0, 0.0, 0.0, 0.0, 1.0]  # Missing one element
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 1
        assert "6 elements" in errors[0]

    def test_non_unit_orientation_vectors(self):
        """Test non-unit orientation vectors."""
        # Vectors not normalized
        orientation = [2.0, 0.0, 0.0, 0.0, 2.0, 0.0]
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 2  # Both vectors are not unit length
        assert any("not unit length" in error for error in errors)

    def test_non_orthogonal_orientation_vectors(self):
        """Test non-orthogonal orientation vectors."""
        # Vectors not orthogonal
        orientation = [1.0, 0.0, 0.0, 0.5, 0.866, 0.0]  # 60 degree angle
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert (
            len(errors) >= 1
        )  # May have multiple errors (unit length + orthogonality)
        assert any(
            "not orthogonal" in error and "dot product" in error for error in errors
        )

    def test_invalid_position_length(self):
        """Test invalid image position length."""
        orientation = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        position = [100.0, 200.0]  # Missing Z coordinate
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 1
        assert "3 elements" in errors[0]

    def test_position_coordinates_exceed_range(self):
        """Test image position coordinates exceeding range."""
        orientation = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        max_coord = CLINICAL_GEOMETRIC_LIMITS["max_coordinate_value"]
        position = [max_coord + 100, 0.0, 0.0]  # Exceeds maximum
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 1
        assert "too large" in errors[0]
        assert f"{max_coord}mm" in errors[0]

    def test_invalid_pixel_spacing_length(self):
        """Test invalid pixel spacing length."""
        orientation = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0]  # Missing second element
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 1
        assert "2 elements" in errors[0]

    def test_pixel_spacing_out_of_range(self):
        """Test pixel spacing values outside clinical range."""
        orientation = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        position = [100.0, 200.0, 300.0]
        slice_thickness = 2.5

        limits = CLINICAL_GEOMETRIC_LIMITS

        # Too small
        pixel_spacing = [limits["min_pixel_spacing"] / 2, 1.0]
        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 1
        assert "too small" in errors[0]

        # Too large
        pixel_spacing = [limits["max_pixel_spacing"] * 2, 1.0]
        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 1
        assert "too large" in errors[0]

    def test_slice_thickness_out_of_range(self):
        """Test slice thickness values outside clinical range."""
        orientation = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0, 1.0]

        limits = CLINICAL_GEOMETRIC_LIMITS

        # Too small
        slice_thickness = limits["min_slice_thickness"] / 2
        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 1
        assert "too small" in errors[0]

        # Too large
        slice_thickness = limits["max_slice_thickness"] * 2
        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 1
        assert "too large" in errors[0]


class TestClinicalScenarios:
    """Test clinical scenarios and edge cases."""

    def test_high_resolution_ct(self):
        """Test high-resolution CT parameters."""
        orientation = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        position = [0.0, 0.0, 0.0]
        pixel_spacing = [0.5, 0.5]  # High resolution
        slice_thickness = 0.5  # Thin slices

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 0

    def test_low_resolution_planning_ct(self):
        """Test low-resolution planning CT parameters."""
        orientation = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        position = [0.0, 0.0, 0.0]
        pixel_spacing = [3.0, 3.0]  # Lower resolution
        slice_thickness = 5.0  # Thick slices

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 0

    def test_oblique_orientation(self):
        """Test oblique image orientation."""
        # 45-degree rotation around Z-axis
        cos45 = np.cos(np.pi / 4)
        sin45 = np.sin(np.pi / 4)
        orientation = [cos45, sin45, 0.0, -sin45, cos45, 0.0]
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 0

    def test_extreme_but_valid_coordinates(self):
        """Test extreme but valid coordinate values."""
        max_coord = CLINICAL_GEOMETRIC_LIMITS["max_coordinate_value"]

        coords = np.array(
            [
                [max_coord - 1, max_coord - 1, max_coord - 1],
                [-max_coord + 1, -max_coord + 1, -max_coord + 1],
            ]
        )

        errors = validate_coordinate_transformation_inputs(coords)
        assert len(errors) == 0

    def test_minimal_structure_coordinates(self):
        """Test coordinates for minimal structures."""
        # Very small structure coordinates
        coords = np.array(
            [
                [0.1, 0.1, 0.1],
                [0.2, 0.2, 0.2],
            ]
        )

        errors = validate_coordinate_transformation_inputs(coords)
        assert len(errors) == 0

    def test_large_patient_coordinates(self):
        """Test coordinates for large patient extent."""
        # Large but reasonable patient coordinates
        coords = np.array(
            [
                [300.0, 400.0, 500.0],
                [-300.0, -400.0, -500.0],
            ]
        )

        errors = validate_coordinate_transformation_inputs(coords)
        assert len(errors) == 0


class TestToleranceHandling:
    """Test geometric tolerance handling."""

    def test_orientation_vectors_within_tolerance(self):
        """Test orientation vectors within geometric tolerance."""
        tolerance = CLINICAL_GEOMETRIC_LIMITS["geometric_tolerance"]

        # Vectors slightly off from unit length but within tolerance
        orientation = [
            1.0 + tolerance / 2,
            0.0,
            0.0,  # Slightly longer than unit
            0.0,
            1.0 - tolerance / 2,
            0.0,  # Slightly shorter than unit
        ]
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 0

    def test_orientation_vectors_outside_tolerance(self):
        """Test orientation vectors outside geometric tolerance."""
        tolerance = CLINICAL_GEOMETRIC_LIMITS["geometric_tolerance"]

        # Vectors outside tolerance
        orientation = [
            1.0 + tolerance * 2,
            0.0,
            0.0,  # Too far from unit length
            0.0,
            1.0,
            0.0,
        ]
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 1
        assert "not unit length" in errors[0]

    def test_orthogonality_within_tolerance(self):
        """Test orthogonality check within tolerance."""
        tolerance = CLINICAL_GEOMETRIC_LIMITS["geometric_tolerance"]

        # Vectors with small dot product within tolerance
        orientation = [1.0, 0.0, 0.0, tolerance / 2, 1.0, 0.0]
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 0

    def test_orthogonality_outside_tolerance(self):
        """Test orthogonality check outside tolerance."""
        tolerance = CLINICAL_GEOMETRIC_LIMITS["geometric_tolerance"]

        # Vectors with dot product outside tolerance
        orientation = [1.0, 0.0, 0.0, tolerance * 2, 1.0, 0.0]
        position = [100.0, 200.0, 300.0]
        pixel_spacing = [1.0, 1.0]
        slice_thickness = 2.5

        errors = validate_image_orientation_and_position(
            orientation, position, pixel_spacing, slice_thickness
        )
        assert len(errors) == 1
        assert "not orthogonal" in errors[0]
