"""
Test DICOM tag value validation functionality.

Tests comprehensive DICOM tag validation including:
- DICOM tag values against VR (Value Representation) constraints
- Length limits and character set validation for text fields
- Numeric range validation for clinical parameters
- RT-specific clinical parameter validation
"""

import pytest

from pyrt_dicom.validation.dicom_tags import (
    DicomTagValidator,
    validate_dicom_tag_value,
    validate_vr_constraints,
    get_clinical_range,
    DICOM_VR_CONSTRAINTS,
    RT_CLINICAL_RANGES,
)


class TestDicomTagValidator:
    """Test DicomTagValidator class functionality."""

    def test_init_default(self):
        """Test validator initialization with defaults."""
        validator = DicomTagValidator()
        assert validator.strict_mode is True
        assert validator.vr_constraints == DICOM_VR_CONSTRAINTS
        assert validator.clinical_ranges == RT_CLINICAL_RANGES

    def test_init_custom(self):
        """Test validator initialization with custom settings."""
        validator = DicomTagValidator(strict_mode=False)
        assert validator.strict_mode is False


class TestVRConstraintValidation:
    """Test DICOM VR constraint validation."""

    def test_valid_lo_values(self):
        """Test valid Long String (LO) values."""
        validator = DicomTagValidator()
        valid_values = [
            "PatientID_001",
            "Study Description",
            "A" * 64,  # Maximum length
            "",  # Empty string
        ]

        for value in valid_values:
            errors = validator.validate_tag_value("LO", "TestTag", value)
            assert len(errors) == 0, f"LO value '{value}' should be valid"

    def test_invalid_lo_length(self):
        """Test LO values exceeding maximum length."""
        validator = DicomTagValidator()
        long_value = "A" * 65  # Exceeds 64 character limit

        errors = validator.validate_tag_value("LO", "TestTag", long_value)
        assert len(errors) == 1
        assert "exceeds maximum length" in errors[0]
        assert "VR LO" in errors[0]

    def test_valid_ds_values(self):
        """Test valid Decimal String (DS) values."""
        validator = DicomTagValidator()
        valid_values = [
            "1.5",
            "-2.5",
            "123.456",
            "1.23e-4",
            "1.23E+5",
            "0",
            ".5",
            "5.",
        ]

        for value in valid_values:
            errors = validator.validate_tag_value("DS", "TestTag", value)
            assert len(errors) == 0, f"DS value '{value}' should be valid"

    def test_invalid_ds_format(self):
        """Test invalid DS format."""
        validator = DicomTagValidator()
        invalid_values = [
            "abc",
            "1.2.3",
            "1,5",  # Comma instead of period
            "1 2",  # Space
        ]

        for value in invalid_values:
            errors = validator.validate_tag_value("DS", "TestTag", value)
            assert len(errors) == 1
            assert "format invalid" in errors[0]

    def test_valid_is_values(self):
        """Test valid Integer String (IS) values."""
        validator = DicomTagValidator()
        valid_values = [
            "123",
            "-456",
            "0",
            "+789",
        ]

        for value in valid_values:
            errors = validator.validate_tag_value("IS", "TestTag", value)
            assert len(errors) == 0, f"IS value '{value}' should be valid"

    def test_invalid_is_format(self):
        """Test invalid IS format."""
        validator = DicomTagValidator()
        invalid_values = [
            "1.5",
            "abc",
            "1 2",
            "1e5",
        ]

        for value in invalid_values:
            errors = validator.validate_tag_value("IS", "TestTag", value)
            assert len(errors) == 1
            assert "format invalid" in errors[0]

    def test_valid_da_values(self):
        """Test valid Date (DA) values."""
        validator = DicomTagValidator()
        valid_values = [
            "20231201",
            "19800101",
            "20991231",
        ]

        for value in valid_values:
            errors = validator.validate_tag_value("DA", "TestTag", value)
            assert len(errors) == 0, f"DA value '{value}' should be valid"

    def test_invalid_da_format(self):
        """Test invalid DA format."""
        validator = DicomTagValidator()
        invalid_values = [
            "2023-12-01",  # Wrong format (also too long)
            "23/12/01",  # Wrong format
            "2023120",  # Too short
            "202312011",  # Too long
            "abcd1201",  # Non-numeric
        ]

        for value in invalid_values:
            errors = validator.validate_tag_value("DA", "TestTag", value)
            assert len(errors) >= 1  # May have multiple errors (length + format)
            assert any(
                "format invalid" in error or "exceeds maximum length" in error
                for error in errors
            )

    def test_valid_tm_values(self):
        """Test valid Time (TM) values."""
        validator = DicomTagValidator()
        valid_values = [
            "12",
            "1234",
            "123456",
            "123456.123",
            "123456.123456",
        ]

        for value in valid_values:
            errors = validator.validate_tag_value("TM", "TestTag", value)
            assert len(errors) == 0, f"TM value '{value}' should be valid"

    def test_valid_pn_values(self):
        """Test valid Person Name (PN) values."""
        validator = DicomTagValidator()
        valid_values = [
            "Doe^John",
            "Doe^John^M",
            "Doe^John^Middle^Dr^Jr",
            "Smith",
            "O'Brien^Patrick",
        ]

        for value in valid_values:
            errors = validator.validate_tag_value("PN", "TestTag", value)
            assert len(errors) == 0, f"PN value '{value}' should be valid"

    def test_valid_ui_values(self):
        """Test valid Unique Identifier (UI) values."""
        validator = DicomTagValidator()
        valid_values = [
            "1.2.826.0.1.3680043.8.498.12345",
            "1.2.3",
            "1.2.3.4.5.6.7.8.9.10.11.12.13.14.15",
        ]

        for value in valid_values:
            errors = validator.validate_tag_value("UI", "TestTag", value)
            assert len(errors) == 0, f"UI value '{value}' should be valid"

    def test_invalid_ui_format(self):
        """Test invalid UI format."""
        validator = DicomTagValidator()
        invalid_values = [
            "1.2.3.abc",  # Non-numeric component
            "1.2.3.",  # Trailing period
            ".1.2.3",  # Leading period
            "1..2.3",  # Double period
            "1.2.3-4",  # Invalid character
        ]

        for value in invalid_values:
            errors = validator.validate_tag_value("UI", "TestTag", value)
            assert len(errors) == 1
            assert "format invalid" in errors[0]

    def test_valid_us_values(self):
        """Test valid Unsigned Short (US) values."""
        validator = DicomTagValidator()
        valid_values = [0, 1, 32767, 65535]

        for value in valid_values:
            errors = validator.validate_tag_value("US", "TestTag", value)
            assert len(errors) == 0, f"US value '{value}' should be valid"

    def test_invalid_us_range(self):
        """Test US values outside valid range."""
        validator = DicomTagValidator()
        invalid_values = [-1, 65536, 100000]

        for value in invalid_values:
            errors = validator.validate_tag_value("US", "TestTag", value)
            assert len(errors) == 1
            if value < 0:
                assert "below minimum" in errors[0]
            else:
                assert "above maximum" in errors[0]

    def test_valid_cs_values(self):
        """Test valid Code String (CS) values."""
        validator = DicomTagValidator()
        valid_values = [
            "CT",
            "RTSTRUCT",
            "RTDOSE",
            "RTPLAN",
            "AXIAL",
            "SAGITTAL",
        ]

        for value in valid_values:
            errors = validator.validate_tag_value("CS", "TestTag", value)
            assert len(errors) == 0, f"CS value '{value}' should be valid"

    def test_invalid_cs_characters(self):
        """Test CS values with invalid characters."""
        validator = DicomTagValidator()
        invalid_values = [
            "ct",  # Lowercase not allowed
            "RT-STRUCT",  # Hyphen not allowed in CS
            "RT@DOSE",  # @ not allowed
        ]

        for value in invalid_values:
            errors = validator.validate_tag_value("CS", "TestTag", value)
            assert len(errors) == 1
            assert "invalid characters" in errors[0]


class TestClinicalRangeValidation:
    """Test clinical parameter range validation."""

    def test_valid_slice_thickness(self):
        """Test valid slice thickness values."""
        validator = DicomTagValidator()
        valid_values = ["0.5", "1.0", "2.5", "5.0", "10.0"]

        for value in valid_values:
            errors = validator.validate_tag_value("DS", "SliceThickness", value)
            assert len(errors) == 0, f"SliceThickness '{value}' should be valid"

    def test_invalid_slice_thickness_range(self):
        """Test slice thickness values outside clinical range."""
        validator = DicomTagValidator()

        # Too small
        errors = validator.validate_tag_value("DS", "SliceThickness", "0.05")
        assert len(errors) == 1
        assert "below clinical minimum" in errors[0]
        assert "0.1mm" in errors[0]

        # Too large
        errors = validator.validate_tag_value("DS", "SliceThickness", "100.0")
        assert len(errors) == 1
        assert "above clinical maximum" in errors[0]
        assert "50.0mm" in errors[0]

    def test_valid_pixel_spacing(self):
        """Test valid pixel spacing values."""
        validator = DicomTagValidator()
        valid_values = ["0.1", "0.5", "1.0", "2.0", "5.0"]

        for value in valid_values:
            errors = validator.validate_tag_value("DS", "PixelSpacing", value)
            assert len(errors) == 0, f"PixelSpacing '{value}' should be valid"

    def test_valid_beam_energy(self):
        """Test valid nominal beam energy values."""
        validator = DicomTagValidator()
        valid_values = ["6.0", "10.0", "15.0", "18.0", "23.0"]

        for value in valid_values:
            errors = validator.validate_tag_value("DS", "NominalBeamEnergy", value)
            assert len(errors) == 0, f"NominalBeamEnergy '{value}' should be valid"

    def test_invalid_beam_energy_range(self):
        """Test beam energy values outside clinical range."""
        validator = DicomTagValidator()

        # Too small
        errors = validator.validate_tag_value("DS", "NominalBeamEnergy", "0.5")
        assert len(errors) == 1
        assert "below clinical minimum" in errors[0]
        assert "1.0MV" in errors[0]

        # Too large
        errors = validator.validate_tag_value("DS", "NominalBeamEnergy", "50.0")
        assert len(errors) == 1
        assert "above clinical maximum" in errors[0]
        assert "25.0MV" in errors[0]

    def test_valid_angles(self):
        """Test valid angle values."""
        validator = DicomTagValidator()
        angle_tags = ["GantryAngle", "CollimatorAngle", "CouchAngle"]
        valid_values = ["0.0", "90.0", "180.0", "270.0", "359.9"]

        for tag in angle_tags:
            for value in valid_values:
                errors = validator.validate_tag_value("DS", tag, value)
                assert len(errors) == 0, f"{tag} '{value}' should be valid"

    def test_invalid_angle_range(self):
        """Test angle values outside valid range."""
        validator = DicomTagValidator()

        # Test gantry angle
        errors = validator.validate_tag_value("DS", "GantryAngle", "360.0")
        assert len(errors) == 1
        assert "above clinical maximum" in errors[0]
        assert "359.9degrees" in errors[0]

        errors = validator.validate_tag_value("DS", "GantryAngle", "-1.0")
        assert len(errors) == 1
        assert "below clinical minimum" in errors[0]

    def test_clinical_validation_disabled(self):
        """Test disabling clinical validation."""
        validator = DicomTagValidator()

        # This would normally fail clinical validation
        errors = validator.validate_tag_value(
            "DS", "SliceThickness", "100.0", clinical_validation=False
        )
        assert len(errors) == 0  # Only VR validation, no clinical validation


class TestConvenienceFunctions:
    """Test convenience functions."""

    def test_validate_dicom_tag_value_function(self):
        """Test validate_dicom_tag_value convenience function."""
        errors = validate_dicom_tag_value("DS", "SliceThickness", "2.5")
        assert len(errors) == 0

        errors = validate_dicom_tag_value("DS", "SliceThickness", "invalid")
        assert (
            len(errors) >= 1
        )  # May have multiple errors (VR format + clinical validation)

    def test_validate_vr_constraints_function(self):
        """Test validate_vr_constraints convenience function."""
        errors = validate_vr_constraints("LO", "ValidString", "TestTag")
        assert len(errors) == 0

        errors = validate_vr_constraints("LO", "A" * 65, "TestTag")
        assert len(errors) == 1

    def test_get_clinical_range_function(self):
        """Test get_clinical_range convenience function."""
        range_info = get_clinical_range("SliceThickness")
        assert range_info is not None
        assert range_info["min_value"] == 0.1
        assert range_info["max_value"] == 50.0
        assert range_info["units"] == "mm"

        range_info = get_clinical_range("NonExistentTag")
        assert range_info is None


class TestEdgeCases:
    """Test edge cases and special scenarios."""

    def test_empty_values(self):
        """Test handling of empty values."""
        validator = DicomTagValidator()

        # Empty values should not trigger validation errors
        errors = validator.validate_tag_value("LO", "TestTag", "")
        assert len(errors) == 0

        errors = validator.validate_tag_value("DS", "SliceThickness", "")
        assert len(errors) == 0

    def test_none_values(self):
        """Test handling of None values."""
        validator = DicomTagValidator()

        errors = validator.validate_tag_value("LO", "TestTag", None)
        assert len(errors) == 0

    def test_whitespace_handling(self):
        """Test handling of whitespace."""
        validator = DicomTagValidator()

        # Whitespace should be stripped
        errors = validator.validate_tag_value("DS", "SliceThickness", "  2.5  ")
        assert len(errors) == 0

    def test_unknown_vr_strict_mode(self):
        """Test unknown VR handling in strict mode."""
        validator = DicomTagValidator(strict_mode=True)

        errors = validator.validate_tag_value("XX", "TestTag", "value")
        assert len(errors) == 1
        assert "Unknown VR" in errors[0]

    def test_unknown_vr_non_strict_mode(self):
        """Test unknown VR handling in non-strict mode."""
        validator = DicomTagValidator(strict_mode=False)

        errors = validator.validate_tag_value("XX", "TestTag", "value")
        assert len(errors) == 0  # Should not fail in non-strict mode

    def test_numeric_conversion_errors(self):
        """Test handling of numeric conversion errors."""
        validator = DicomTagValidator()

        # Non-numeric value for clinical validation
        errors = validator.validate_tag_value("DS", "SliceThickness", "abc")
        assert len(errors) == 2  # VR format error + clinical validation error
        assert any("format invalid" in error for error in errors)
        assert any("must be valid number" in error for error in errors)
