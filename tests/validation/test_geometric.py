# Copyright (C) 2024 Pirate DICOM Contributors

"""
Tests for geometric validation utilities.

Tests coordinate system validation, geometric bounds checking,
and clinical safety validation for RT DICOM objects.
"""

import pytest
import numpy as np

from pyrt_dicom.validation.geometric import (
    GeometricValidator,
    CLINICAL_GEOMETRIC_LIMITS,
    validate_coordinate_bounds,
    validate_geometric_consistency,
    validate_structure_geometry,
    check_contour_closure,
)
from pyrt_dicom.coordinates.transforms import CoordinateTransformer
from pyrt_dicom.coordinates.reference_frames import (
    FrameOfReference,
    GeometricParameters,
)
from pyrt_dicom.utils.exceptions import CoordinateSystemError, ValidationError


class TestGeometricValidator:
    """Test geometric validator functionality."""
    
    def test_initialization_default(self):
        """Test default initialization."""
        validator = GeometricValidator()
        
        assert validator.limits == CLINICAL_GEOMETRIC_LIMITS
        assert validator.tolerance == 1e-6
    
    def test_initialization_custom_limits(self):
        """Test initialization with custom limits."""
        custom_limits = {"max_pixel_spacing": 5.0, "min_slice_thickness": 0.5}
        validator = GeometricValidator(
            clinical_limits=custom_limits,
            tolerance=1e-3,
        )
        
        assert validator.tolerance == 1e-3
        assert validator.limits["max_pixel_spacing"] == 5.0
        assert validator.limits["min_slice_thickness"] == 0.5
        # Check that other defaults are preserved
        assert validator.limits["max_patient_extent"] == CLINICAL_GEOMETRIC_LIMITS["max_patient_extent"]
    
    def test_validate_geometric_parameters_valid(self):
        """Test validation with valid geometric parameters."""
        params = GeometricParameters(
            image_position=(100.0, 200.0, 300.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.25, 1.25),
            slice_thickness=2.5,
            patient_position="HFS",
        )
        
        validator = GeometricValidator()
        errors = validator.validate_geometric_parameters(params)
        
        assert len(errors) == 0
    
    def test_validate_geometric_parameters_pixel_spacing_too_small(self):
        """Test validation with pixel spacing too small."""
        params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(0.005, 1.0),  # Too small
            slice_thickness=2.0,
        )
        
        validator = GeometricValidator()
        errors = validator.validate_geometric_parameters(params)
        
        assert len(errors) == 1
        assert "too small" in errors[0]
        assert "0.005mm" in errors[0]
    
    def test_validate_geometric_parameters_pixel_spacing_too_large(self):
        """Test validation with pixel spacing too large."""
        params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 15.0),  # Too large
            slice_thickness=2.0,
        )
        
        validator = GeometricValidator()
        errors = validator.validate_geometric_parameters(params)
        
        assert len(errors) == 1
        assert "too large" in errors[0]
        assert "15.0mm" in errors[0]
    
    def test_validate_geometric_parameters_slice_thickness_invalid(self):
        """Test validation with invalid slice thickness."""
        # Too small
        params_small = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=0.05,  # Too small
        )
        
        validator = GeometricValidator()
        errors = validator.validate_geometric_parameters(params_small)
        assert len(errors) == 1
        assert "too small" in errors[0]
        
        # Too large
        params_large = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=60.0,  # Too large
        )
        
        errors = validator.validate_geometric_parameters(params_large)
        assert len(errors) == 1
        assert "too large" in errors[0]
    
    def test_validate_geometric_parameters_image_position_too_large(self):
        """Test validation with image position coordinates too large."""
        params = GeometricParameters(
            image_position=(2500.0, 100.0, -2500.0),  # Too large coordinates
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        validator = GeometricValidator()
        errors = validator.validate_geometric_parameters(params)
        
        assert len(errors) == 2  # Two coordinates exceed limit
        assert all("too large" in error for error in errors)
    
    def test_validate_coordinate_consistency_matching_transformers(self):
        """Test coordinate consistency validation with matching transformers."""
        transformer1 = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(0.0, 0.0, 0.0),
        )
        
        transformer2 = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(0.0, 0.0, 0.0),
        )
        
        validator = GeometricValidator()
        errors = validator.validate_coordinate_consistency(transformer1, transformer2)
        
        assert len(errors) == 0
    
    def test_validate_coordinate_consistency_patient_position_mismatch(self):
        """Test coordinate consistency validation with patient position mismatch."""
        transformer1 = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        transformer2 = CoordinateTransformer(
            patient_position="HFP",  # Different position
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        validator = GeometricValidator()
        errors = validator.validate_coordinate_consistency(transformer1, transformer2)
        
        assert len(errors) >= 1
        assert any("Patient position mismatch" in error for error in errors)
    
    def test_validate_coordinate_consistency_orientation_mismatch(self):
        """Test coordinate consistency validation with orientation mismatch."""
        transformer1 = CoordinateTransformer(
            patient_position="HFS",
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        transformer2 = CoordinateTransformer(
            patient_position="HFS",  # Same position
            image_orientation=[-1, 0, 0, 0, -1, 0],  # Different orientation
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        validator = GeometricValidator()
        errors = validator.validate_coordinate_consistency(transformer1, transformer2)
        
        assert len(errors) >= 1
        assert any("Image orientation mismatch" in error for error in errors)
    
    def test_validate_coordinate_consistency_custom_test_points(self):
        """Test coordinate consistency validation with custom test points."""
        transformer1 = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(10.0, 20.0, 30.0),
        )
        
        transformer2 = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            image_position=(10.0, 20.0, 30.0),
        )
        
        custom_test_points = np.array([
            [50, 100, 25],
            [200, 300, 75],
        ], dtype=np.float64)
        
        validator = GeometricValidator()
        errors = validator.validate_coordinate_consistency(
            transformer1, transformer2, custom_test_points
        )
        
        assert len(errors) == 0
    
    def test_validate_structure_bounds_valid(self):
        """Test structure bounds validation with valid coordinates."""
        coordinates = np.array([
            [100.0, 200.0, 300.0],
            [110.0, 210.0, 310.0],
            [120.0, 220.0, 320.0],
        ])
        
        validator = GeometricValidator()
        errors = validator.validate_structure_bounds(coordinates, "TestStructure")
        
        assert len(errors) == 0
    
    def test_validate_structure_bounds_empty_coordinates(self):
        """Test structure bounds validation with empty coordinates."""
        coordinates = np.array([])
        
        validator = GeometricValidator()
        errors = validator.validate_structure_bounds(coordinates, "EmptyStructure")
        
        assert len(errors) == 1
        assert "no coordinates" in errors[0]
    
    def test_validate_structure_bounds_wrong_shape(self):
        """Test structure bounds validation with wrong array shape."""
        coordinates = np.array([
            [100.0, 200.0],  # Missing z coordinate
            [110.0, 210.0],
        ])
        
        validator = GeometricValidator()
        errors = validator.validate_structure_bounds(coordinates, "WrongShape")
        
        assert len(errors) == 1
        assert "N x 3 array" in errors[0]
    
    def test_validate_structure_bounds_coordinate_too_large(self):
        """Test structure bounds validation with coordinates too large."""
        coordinates = np.array([
            [100.0, 200.0, 300.0],
            [2500.0, 200.0, 300.0],  # x-coordinate too large
            [100.0, -2500.0, 300.0],  # y-coordinate too large (negative)
        ])
        
        validator = GeometricValidator()
        errors = validator.validate_structure_bounds(coordinates, "LargeCoords")
        
        assert len(errors) >= 2  # At least two coordinate limit errors
        assert any("x-coordinate" in error and "exceeds limit" in error for error in errors)
        assert any("y-coordinate" in error and "exceeds limit" in error for error in errors)
    
    def test_validate_structure_bounds_extent_too_large(self):
        """Test structure bounds validation with extent too large."""
        coordinates = np.array([
            [-400.0, -400.0, -400.0],  # Large extent structure
            [400.0, 400.0, 400.0],
        ])
        
        validator = GeometricValidator()
        errors = validator.validate_structure_bounds(coordinates, "LargeExtent")
        
        # Should have extent errors for all three axes
        extent_errors = [error for error in errors if "extent" in error and "maximum patient extent" in error]
        assert len(extent_errors) >= 1
    
    def test_validate_structure_bounds_extent_too_small(self):
        """Test structure bounds validation with extent too small."""
        coordinates = np.array([
            [100.0, 200.0, 300.0],
            [100.001, 200.001, 300.001],  # Very small extent
        ])
        
        validator = GeometricValidator()
        errors = validator.validate_structure_bounds(coordinates, "SmallExtent")
        
        # Should have extent errors for all three axes
        extent_errors = [error for error in errors if "extent" in error and "minimum meaningful extent" in error]
        assert len(extent_errors) >= 1
    
    def test_validate_structure_bounds_volume_estimation(self):
        """Test structure bounds validation with volume estimation."""
        # Large structure (should exceed maximum volume)
        large_coordinates = np.array([
            [-200.0, -200.0, -200.0],
            [200.0, 200.0, 200.0],
        ])
        
        validator = GeometricValidator()
        errors = validator.validate_structure_bounds(large_coordinates, "LargeVolume")
        
        volume_errors = [error for error in errors if "volume" in error and "maximum reasonable volume" in error]
        assert len(volume_errors) >= 1
        
        # Tiny structure (should be below minimum volume)
        tiny_coordinates = np.array([
            [100.0, 200.0, 300.0],
            [100.01, 200.01, 300.01],
        ])
        
        errors = validator.validate_structure_bounds(tiny_coordinates, "TinyVolume")
        volume_errors = [error for error in errors if "volume" in error and "minimum meaningful volume" in error]
        assert len(volume_errors) >= 1


class TestConvenienceFunctions:
    """Test convenience functions for geometric validation."""
    
    def test_validate_coordinate_bounds(self):
        """Test validate_coordinate_bounds function."""
        # Valid coordinates
        valid_coords = np.array([
            [100.0, 200.0, 300.0],
            [150.0, 250.0, 350.0],
        ])
        
        errors = validate_coordinate_bounds(valid_coords)
        assert len(errors) == 0
        
        # Invalid coordinates (too large)
        invalid_coords = np.array([
            [2500.0, 200.0, 300.0],
        ])
        
        errors = validate_coordinate_bounds(invalid_coords)
        assert len(errors) > 0
        assert any("exceeds limit" in error for error in errors)
    
    def test_validate_coordinate_bounds_custom_limits(self):
        """Test validate_coordinate_bounds with custom limits."""
        coordinates = np.array([[100.0, 200.0, 300.0]])
        custom_limits = {"max_coordinate_value": 50.0}  # Very restrictive
        
        errors = validate_coordinate_bounds(coordinates, limits=custom_limits)
        
        assert len(errors) >= 1
        assert any("50.0mm" in error for error in errors)
    
    def test_validate_geometric_consistency_function(self):
        """Test validate_geometric_consistency function."""
        frame_manager = FrameOfReference()
        
        # Create frame with specific parameters
        original_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            patient_position="HFS",
        )
        
        frame_uid = frame_manager.create_frame_of_reference(original_params)
        
        # Test consistent parameters
        consistent_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
            patient_position="HFS",
        )
        
        errors = validate_geometric_consistency(
            frame_manager, consistent_params, frame_uid
        )
        assert len(errors) == 0
        
        # Test inconsistent parameters
        inconsistent_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(2.0, 2.0),  # Different spacing
            slice_thickness=2.0,
            patient_position="HFS",
        )
        
        errors = validate_geometric_consistency(
            frame_manager, inconsistent_params, frame_uid
        )
        assert len(errors) > 0
        assert any("Pixel spacing mismatch" in error for error in errors)
    
    def test_validate_structure_geometry(self):
        """Test validate_structure_geometry function."""
        # Valid 3D binary mask
        valid_mask = np.ones((50, 50, 20), dtype=np.uint8)
        valid_mask[10:40, 10:40, 5:15] = 1  # Small structure inside
        
        geometric_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        errors = validate_structure_geometry(
            valid_mask, "TestStructure", geometric_params
        )
        # May have warnings about volume, but should not have fatal errors
        assert not any("must be 3D" in error for error in errors)
        assert not any("must be binary" in error for error in errors)
    
    def test_validate_structure_geometry_empty_mask(self):
        """Test validate_structure_geometry with empty mask."""
        empty_mask = np.array([])
        
        geometric_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        errors = validate_structure_geometry(
            empty_mask, "EmptyMask", geometric_params
        )
        
        assert len(errors) == 1
        assert "mask is empty" in errors[0]
    
    def test_validate_structure_geometry_wrong_dimensions(self):
        """Test validate_structure_geometry with wrong mask dimensions."""
        wrong_dim_mask = np.ones((50, 50), dtype=np.uint8)  # 2D instead of 3D
        
        geometric_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        errors = validate_structure_geometry(
            wrong_dim_mask, "WrongDim", geometric_params
        )
        
        assert len(errors) >= 1
        assert any("must be 3D" in error for error in errors)
    
    def test_validate_structure_geometry_non_binary(self):
        """Test validate_structure_geometry with non-binary mask."""
        non_binary_mask = np.full((20, 20, 10), 0.5, dtype=np.float32)  # Not binary
        
        geometric_params = GeometricParameters(
            image_position=(0.0, 0.0, 0.0),
            image_orientation=[1, 0, 0, 0, 1, 0],
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.0,
        )
        
        errors = validate_structure_geometry(
            non_binary_mask, "NonBinary", geometric_params
        )
        
        assert len(errors) >= 1
        assert any("must be binary" in error for error in errors)
    
    def test_check_contour_closure(self):
        """Test check_contour_closure function."""
        # Closed contour
        closed_contour = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [1.0, 1.0, 0.0],
            [0.0, 1.0, 0.0],
            [0.0, 0.0, 0.0],  # Returns to start
        ])
        
        assert check_contour_closure(closed_contour) is True
        
        # Open contour
        open_contour = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [1.0, 1.0, 0.0],
            [0.0, 1.0, 0.0],
            [0.5, 0.5, 0.0],  # Does not return to start
        ])
        
        assert check_contour_closure(open_contour) is False
        
        # Nearly closed contour (within tolerance)
        nearly_closed_contour = np.array([
            [0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0],
            [1.0, 1.0, 0.0],
            [0.005, 0.0, 0.0],  # Very close to start
        ])
        
        assert check_contour_closure(nearly_closed_contour, tolerance=0.01) is True
        assert check_contour_closure(nearly_closed_contour, tolerance=0.001) is False
    
    def test_check_contour_closure_insufficient_points(self):
        """Test check_contour_closure with insufficient points."""
        too_few_points = np.array([
            [0.0, 0.0],
            [1.0, 0.0],
        ])
        
        assert check_contour_closure(too_few_points) is False


class TestClinicalGeometricLimits:
    """Test clinical geometric limits constants."""
    
    def test_limits_completeness(self):
        """Test that all required limits are defined."""
        required_limits = [
            "max_patient_extent",
            "min_patient_extent", 
            "max_pixel_spacing",
            "min_pixel_spacing",
            "max_slice_thickness",
            "min_slice_thickness",
            "max_structure_volume",
            "min_structure_volume",
            "max_coordinate_value",
            "contour_point_tolerance",
            "geometric_tolerance",
        ]
        
        for limit in required_limits:
            assert limit in CLINICAL_GEOMETRIC_LIMITS
            assert isinstance(CLINICAL_GEOMETRIC_LIMITS[limit], (int, float))
            assert CLINICAL_GEOMETRIC_LIMITS[limit] > 0
    
    def test_limits_reasonableness(self):
        """Test that limits are clinically reasonable."""
        limits = CLINICAL_GEOMETRIC_LIMITS
        
        # Patient extents should be reasonable for human body
        assert 10 <= limits["min_patient_extent"] <= 50  # mm
        assert 300 <= limits["max_patient_extent"] <= 1000  # mm
        
        # Pixel spacing should cover clinical range
        assert 0.001 <= limits["min_pixel_spacing"] <= 0.1  # mm
        assert 5 <= limits["max_pixel_spacing"] <= 20  # mm
        
        # Slice thickness should cover clinical range
        assert 0.1 <= limits["min_slice_thickness"] <= 1.0  # mm
        assert 10 <= limits["max_slice_thickness"] <= 100  # mm
        
        # Structure volumes should be reasonable
        assert 0.0001 <= limits["min_structure_volume"] <= 0.01  # cc
        assert 1000 <= limits["max_structure_volume"] <= 50000  # cc
        
        # Coordinate values should allow whole-body coverage
        assert 1000 <= limits["max_coordinate_value"] <= 5000  # mm
        
        # Tolerances should be sub-millimeter
        assert limits["contour_point_tolerance"] <= 1.0  # mm
        assert limits["geometric_tolerance"] <= 1e-3