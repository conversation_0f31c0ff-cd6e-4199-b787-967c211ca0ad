"""
Integration tests for UID generation with pydicom.

Tests real DICOM file creation with generated UIDs and verifies 
compatibility with pydicom dataset operations.
"""

import pytest
import tempfile
import os
from pathlib import Path

import pydicom
from pydicom.dataset import Dataset, FileDataset
from pydicom.uid import generate_uid

from pyrt_dicom.uid_generation.generators import (
    HashBasedUIDGenerator, RandomUIDGenerator, DefaultUIDGenerator
)
from pyrt_dicom.uid_generation.registry import UIDRegistry
from pyrt_dicom.utils.exceptions import UIDGenerationError


class TestPydicomIntegration:
    """Test UID generator integration with pydicom."""
    
    def test_generated_uids_valid_in_pydicom(self):
        """Test that generated UIDs are valid for pydicom datasets."""
        generator = RandomUIDGenerator()
        
        # Generate various UID types
        study_uid = generator.generate_study_instance_uid()
        series_uid = generator.generate_series_instance_uid()
        instance_uid = generator.generate_sop_instance_uid()
        frame_ref_uid = generator.generate_frame_of_reference_uid()
        
        # Create pydicom dataset with generated UIDs
        ds = Dataset()
        ds.StudyInstanceUID = study_uid
        ds.SeriesInstanceUID = series_uid
        ds.SOPInstanceUID = instance_uid
        ds.FrameOfReferenceUID = frame_ref_uid
        
        # Verify UIDs are accepted by pydicom
        assert ds.StudyInstanceUID == study_uid
        assert ds.SeriesInstanceUID == series_uid
        assert ds.SOPInstanceUID == instance_uid
        assert ds.FrameOfReferenceUID == frame_ref_uid
    
    def test_hash_generator_consistency_across_sessions(self):
        """Test hash generator produces consistent UIDs across sessions."""
        seed_data = "patient_123_study_ct_20231201"
        
        # Generate UID in first "session"
        generator1 = HashBasedUIDGenerator()
        uid1 = generator1.generate_study_instance_uid(seed_data)
        
        # Generate UID in second "session"
        generator2 = HashBasedUIDGenerator()
        uid2 = generator2.generate_study_instance_uid(seed_data)
        
        # Should be identical
        assert uid1 == uid2
        
        # Both should work in pydicom
        ds1 = Dataset()
        ds1.StudyInstanceUID = uid1
        
        ds2 = Dataset()
        ds2.StudyInstanceUID = uid2
        
        assert ds1.StudyInstanceUID == ds2.StudyInstanceUID


class TestDicomFileCreation:
    """Test creating actual DICOM files with generated UIDs."""
    
    def create_basic_dicom_dataset(self, generator):
        """Create a basic DICOM dataset for testing."""
        study_uid = generator.generate_study_instance_uid()
        series_uid = generator.generate_series_instance_uid()
        instance_uid = generator.generate_sop_instance_uid()
        
        # Create basic dataset
        ds = Dataset()
        
        # Required DICOM elements
        ds.StudyInstanceUID = study_uid
        ds.SeriesInstanceUID = series_uid
        ds.SOPInstanceUID = instance_uid
        ds.SOPClassUID = "1.2.840.10008.*******.1.2"  # CT Image Storage
        
        # Patient information
        ds.PatientName = "Test^Patient"
        ds.PatientID = "TEST123"
        ds.PatientBirthDate = "19800101"
        ds.PatientSex = "M"
        
        # Study information
        ds.StudyDate = "20231201"
        ds.StudyTime = "120000"
        ds.StudyID = "1"
        ds.AccessionNumber = "ACC123"
        
        # Series information
        ds.SeriesDate = "20231201"
        ds.SeriesTime = "120000"
        ds.Modality = "CT"
        ds.SeriesNumber = "1"
        
        # Instance information
        ds.InstanceNumber = "1"
        ds.ImageType = ["ORIGINAL", "PRIMARY", "AXIAL"]
        
        return ds
    
    def test_create_dicom_file_with_random_uids(self):
        """Test creating DICOM file with random UIDs."""
        generator = RandomUIDGenerator()
        ds = self.create_basic_dicom_dataset(generator)
        
        with tempfile.NamedTemporaryFile(suffix='.dcm', delete=False) as tmp:
            try:
                # Create FileDataset
                file_meta = Dataset()
                file_meta.TransferSyntaxUID = pydicom.uid.ImplicitVRLittleEndian
                file_meta.MediaStorageSOPClassUID = ds.SOPClassUID
                file_meta.MediaStorageSOPInstanceUID = ds.SOPInstanceUID
                
                file_ds = FileDataset(tmp.name, ds, file_meta=file_meta, preamble=b"\0" * 128)
                file_ds.is_little_endian = True
                file_ds.is_implicit_VR = True
                
                # Save file
                file_ds.save_as(tmp.name)
                
                # Read back and verify
                read_ds = pydicom.dcmread(tmp.name)
                assert read_ds.StudyInstanceUID == ds.StudyInstanceUID
                assert read_ds.SeriesInstanceUID == ds.SeriesInstanceUID
                assert read_ds.SOPInstanceUID == ds.SOPInstanceUID
                
            finally:
                if os.path.exists(tmp.name):
                    os.unlink(tmp.name)
    
    def test_create_dicom_file_with_hash_uids(self):
        """Test creating DICOM file with hash-based UIDs."""
        generator = HashBasedUIDGenerator()
        seed_data = "reproducible_test_data"
        
        # Create first dataset
        ds1 = self.create_basic_dicom_dataset(generator)
        ds1.StudyInstanceUID = generator.generate_study_instance_uid(seed_data)
        ds1.SeriesInstanceUID = generator.generate_series_instance_uid(seed_data + "_series")
        ds1.SOPInstanceUID = generator.generate_sop_instance_uid(seed_data + "_instance1")
        
        # Create second dataset with same seed data
        ds2 = self.create_basic_dicom_dataset(generator)
        ds2.StudyInstanceUID = generator.generate_study_instance_uid(seed_data)
        ds2.SeriesInstanceUID = generator.generate_series_instance_uid(seed_data + "_series")
        ds2.SOPInstanceUID = generator.generate_sop_instance_uid(seed_data + "_instance1")
        
        # Study and Series UIDs should be identical (same seed)
        assert ds1.StudyInstanceUID == ds2.StudyInstanceUID
        assert ds1.SeriesInstanceUID == ds2.SeriesInstanceUID
        assert ds1.SOPInstanceUID == ds2.SOPInstanceUID
        
        # Verify they can be saved and read
        with tempfile.NamedTemporaryFile(suffix='.dcm', delete=False) as tmp:
            try:
                file_meta = Dataset()
                file_meta.TransferSyntaxUID = pydicom.uid.ImplicitVRLittleEndian
                file_meta.MediaStorageSOPClassUID = ds1.SOPClassUID
                file_meta.MediaStorageSOPInstanceUID = ds1.SOPInstanceUID
                
                file_ds = FileDataset(tmp.name, ds1, file_meta=file_meta, preamble=b"\0" * 128)
                file_ds.is_little_endian = True
                file_ds.is_implicit_VR = True
                
                file_ds.save_as(tmp.name)
                
                read_ds = pydicom.dcmread(tmp.name)
                assert read_ds.StudyInstanceUID == ds1.StudyInstanceUID
                
            finally:
                if os.path.exists(tmp.name):
                    os.unlink(tmp.name)
    
    def test_create_rt_structure_with_registry(self):
        """Test creating RT Structure-like DICOM with UID registry."""
        registry = UIDRegistry()
        
        # Create study and series hierarchy
        study_uid = registry.create_and_register_study_uid({'study_description': 'RT Planning'})
        series_uid = registry.create_and_register_series_uid(study_uid, {'modality': 'RTSTRUCT'})
        instance_uid = registry.create_and_register_instance_uid(series_uid)
        frame_ref_uid = registry.create_and_register_frame_reference_uid(study_uid)
        
        # Create RT Structure dataset
        ds = Dataset()
        ds.StudyInstanceUID = study_uid
        ds.SeriesInstanceUID = series_uid
        ds.SOPInstanceUID = instance_uid
        ds.SOPClassUID = "1.2.840.10008.*******.1.481.3"  # RT Structure Set Storage
        ds.FrameOfReferenceUID = frame_ref_uid
        
        # RT Structure specific elements
        ds.Modality = "RTSTRUCT"
        ds.StructureSetLabel = "Test Structures"
        ds.StructureSetName = "Test"
        
        # Verify relationships are maintained
        assert registry.get_series_study(series_uid) == study_uid
        assert registry.get_instance_series(instance_uid) == series_uid
        
        # Verify dataset is valid
        assert ds.StudyInstanceUID == study_uid
        assert ds.SeriesInstanceUID == series_uid
        assert ds.SOPInstanceUID == instance_uid
        assert ds.FrameOfReferenceUID == frame_ref_uid


class TestUIDRegistryWithPydicom:
    """Test UID registry integration with pydicom datasets."""
    
    def test_registry_tracks_multiple_rt_objects(self):
        """Test registry tracking multiple RT DICOM objects."""
        registry = UIDRegistry()
        
        # Create study
        study_uid = registry.create_and_register_study_uid()
        frame_ref_uid = registry.create_and_register_frame_reference_uid(study_uid)
        
        # Create CT series
        ct_series_uid = registry.create_and_register_series_uid(
            study_uid, {'modality': 'CT', 'description': 'Planning CT'}
        )
        
        # Create RT Structure series
        struct_series_uid = registry.create_and_register_series_uid(
            study_uid, {'modality': 'RTSTRUCT', 'description': 'RT Structures'}
        )
        
        # Create RT Dose series
        dose_series_uid = registry.create_and_register_series_uid(
            study_uid, {'modality': 'RTDOSE', 'description': 'RT Dose Distribution'}
        )
        
        # Create instances
        ct_instance_uid = registry.create_and_register_instance_uid(ct_series_uid)
        struct_instance_uid = registry.create_and_register_instance_uid(struct_series_uid)
        dose_instance_uid = registry.create_and_register_instance_uid(dose_series_uid)
        
        # Create datasets for each
        datasets = []
        
        # CT dataset
        ct_ds = Dataset()
        ct_ds.StudyInstanceUID = study_uid
        ct_ds.SeriesInstanceUID = ct_series_uid
        ct_ds.SOPInstanceUID = ct_instance_uid
        ct_ds.FrameOfReferenceUID = frame_ref_uid
        ct_ds.Modality = "CT"
        datasets.append(ct_ds)
        
        # RT Structure dataset
        struct_ds = Dataset()
        struct_ds.StudyInstanceUID = study_uid
        struct_ds.SeriesInstanceUID = struct_series_uid
        struct_ds.SOPInstanceUID = struct_instance_uid
        struct_ds.FrameOfReferenceUID = frame_ref_uid
        struct_ds.Modality = "RTSTRUCT"
        datasets.append(struct_ds)
        
        # RT Dose dataset
        dose_ds = Dataset()
        dose_ds.StudyInstanceUID = study_uid
        dose_ds.SeriesInstanceUID = dose_series_uid
        dose_ds.SOPInstanceUID = dose_instance_uid
        dose_ds.FrameOfReferenceUID = frame_ref_uid
        dose_ds.Modality = "RTDOSE"
        datasets.append(dose_ds)
        
        # Verify all use same study and frame of reference
        for ds in datasets:
            assert ds.StudyInstanceUID == study_uid
            assert ds.FrameOfReferenceUID == frame_ref_uid
        
        # Verify registry maintains relationships
        study_series = registry.get_study_series(study_uid)
        assert len(study_series) == 3
        assert ct_series_uid in study_series
        assert struct_series_uid in study_series
        assert dose_series_uid in study_series
        
        # Validate registry hierarchy
        errors = registry.validate_hierarchy()
        assert len(errors) == 0


class TestUIDComplianceWithPydicom:
    """Test UID format compliance with pydicom requirements."""
    
    def test_uid_length_compliance(self):
        """Test that generated UIDs comply with DICOM length limits."""
        generator = RandomUIDGenerator()
        
        # Generate many UIDs to test length consistency
        for _ in range(1000):
            uid = generator.generate_uid()
            assert len(uid) <= 64  # DICOM maximum
            
            # Should be accepted by pydicom
            ds = Dataset()
            ds.StudyInstanceUID = uid
            assert ds.StudyInstanceUID == uid
    
    def test_uid_character_compliance(self):
        """Test that generated UIDs use only valid characters."""
        generator = RandomUIDGenerator()
        
        for _ in range(100):
            uid = generator.generate_uid()
            
            # Should contain only digits and dots
            assert all(c.isdigit() or c == '.' for c in uid)
            
            # Should not start or end with dot
            assert not uid.startswith('.')
            assert not uid.endswith('.')
            
            # Should not have consecutive dots
            assert '..' not in uid
            
            # Should work in pydicom
            ds = Dataset()
            ds.StudyInstanceUID = uid
            assert ds.StudyInstanceUID == uid
    
    def test_pydicom_uid_validation(self):
        """Test that pydicom accepts our UIDs without validation errors."""
        generator = RandomUIDGenerator()
        
        study_uid = generator.generate_study_instance_uid()
        series_uid = generator.generate_series_instance_uid()
        instance_uid = generator.generate_sop_instance_uid()
        
        ds = Dataset()
        ds.StudyInstanceUID = study_uid
        ds.SeriesInstanceUID = series_uid
        ds.SOPInstanceUID = instance_uid
        
        # Verify pydicom doesn't raise validation errors
        # (pydicom typically validates UID format when accessing)
        assert str(ds.StudyInstanceUID) == study_uid
        assert str(ds.SeriesInstanceUID) == series_uid
        assert str(ds.SOPInstanceUID) == instance_uid


class TestRealWorldScenarios:
    """Test real-world clinical scenarios."""
    
    def test_multi_slice_ct_series(self):
        """Test creating multi-slice CT series with consistent UIDs."""
        registry = UIDRegistry()
        generator = registry.uid_generator
        
        # Create study and series
        study_uid = registry.create_and_register_study_uid()
        ct_series_uid = registry.create_and_register_series_uid(study_uid, {'modality': 'CT'})
        frame_ref_uid = registry.create_and_register_frame_reference_uid(study_uid)
        
        # Create 200 CT slices (typical clinical study)
        slice_datasets = []
        for i in range(200):
            instance_uid = registry.create_and_register_instance_uid(ct_series_uid)
            
            ds = Dataset()
            ds.StudyInstanceUID = study_uid
            ds.SeriesInstanceUID = ct_series_uid
            ds.SOPInstanceUID = instance_uid
            ds.FrameOfReferenceUID = frame_ref_uid
            ds.Modality = "CT"
            ds.InstanceNumber = str(i + 1)
            ds.SliceLocation = str(i * 2.5)  # 2.5mm spacing
            
            slice_datasets.append(ds)
        
        # Verify all slices share same study/series/frame reference
        for ds in slice_datasets:
            assert ds.StudyInstanceUID == study_uid
            assert ds.SeriesInstanceUID == ct_series_uid
            assert ds.FrameOfReferenceUID == frame_ref_uid
        
        # Verify all have unique instance UIDs
        instance_uids = [ds.SOPInstanceUID for ds in slice_datasets]
        assert len(set(instance_uids)) == 200
        
        # Verify registry tracking
        series_instances = registry.get_series_instances(ct_series_uid)
        assert len(series_instances) == 200
        
        errors = registry.validate_hierarchy()
        assert len(errors) == 0
    
    def test_complete_rt_workflow_uids(self):
        """Test complete RT workflow with all DICOM types."""
        registry = UIDRegistry()
        
        # Create study
        study_uid = registry.create_and_register_study_uid({'description': 'RT Planning Study'})
        frame_ref_uid = registry.create_and_register_frame_reference_uid(study_uid)
        
        # CT Planning series (100 slices)
        ct_series_uid = registry.create_and_register_series_uid(study_uid, {'modality': 'CT'})
        ct_instances = []
        for i in range(100):
            ct_instance = registry.create_and_register_instance_uid(ct_series_uid)
            ct_instances.append(ct_instance)
        
        # RT Structure Set
        struct_series_uid = registry.create_and_register_series_uid(study_uid, {'modality': 'RTSTRUCT'})
        struct_instance_uid = registry.create_and_register_instance_uid(struct_series_uid)
        
        # RT Dose
        dose_series_uid = registry.create_and_register_series_uid(study_uid, {'modality': 'RTDOSE'})
        dose_instance_uid = registry.create_and_register_instance_uid(dose_series_uid)
        
        # RT Plan
        plan_series_uid = registry.create_and_register_series_uid(study_uid, {'modality': 'RTPLAN'})
        plan_instance_uid = registry.create_and_register_instance_uid(plan_series_uid)
        
        # Verify registry state
        summary = registry.get_registry_summary()
        assert summary['studies'] == 1
        assert summary['series'] == 4  # CT, STRUCT, DOSE, PLAN
        assert summary['instances'] == 103  # 100 CT + 1 each for RT objects
        assert summary['frame_references'] == 1
        
        # Verify hierarchy
        study_series = registry.get_study_series(study_uid)
        assert len(study_series) == 4
        
        errors = registry.validate_hierarchy()
        assert len(errors) == 0
        
        # Create sample datasets and verify UID consistency
        rt_datasets = []
        
        # Structure Set dataset
        struct_ds = Dataset()
        struct_ds.StudyInstanceUID = study_uid
        struct_ds.SeriesInstanceUID = struct_series_uid
        struct_ds.SOPInstanceUID = struct_instance_uid
        struct_ds.FrameOfReferenceUID = frame_ref_uid
        struct_ds.Modality = "RTSTRUCT"
        rt_datasets.append(struct_ds)
        
        # RT Dose dataset
        dose_ds = Dataset()
        dose_ds.StudyInstanceUID = study_uid
        dose_ds.SeriesInstanceUID = dose_series_uid
        dose_ds.SOPInstanceUID = dose_instance_uid
        dose_ds.FrameOfReferenceUID = frame_ref_uid
        dose_ds.Modality = "RTDOSE"
        rt_datasets.append(dose_ds)
        
        # RT Plan dataset
        plan_ds = Dataset()
        plan_ds.StudyInstanceUID = study_uid
        plan_ds.SeriesInstanceUID = plan_series_uid
        plan_ds.SOPInstanceUID = plan_instance_uid
        plan_ds.FrameOfReferenceUID = frame_ref_uid
        plan_ds.Modality = "RTPLAN"
        rt_datasets.append(plan_ds)
        
        # All RT objects should share study and frame of reference
        for ds in rt_datasets:
            assert ds.StudyInstanceUID == study_uid
            assert ds.FrameOfReferenceUID == frame_ref_uid