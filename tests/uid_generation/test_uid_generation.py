"""
Tests for UID generation functionality.

Tests UID format compliance and uniqueness following DICOM standards.
"""

import pytest
from unittest.mock import patch
import time

from pyrt_dicom.uid_generation.generators import (
    UIDGenerator, HashBasedUIDGenerator, RandomUIDGenerator, 
    DefaultUIDGenerator
)
from pyrt_dicom.utils.exceptions import UIDGenerationError


class TestUIDGenerator:
    """Test abstract base UID generator."""
    
    def test_default_root_uid_used(self):
        """Test that default root UID is used when none specified."""
        generator = RandomUIDGenerator()
        assert generator.root_uid == UIDGenerator.PYRT_DICOM_ROOT
    
    def test_custom_root_uid_used(self):
        """Test that custom root UID is used when specified."""
        custom_root = "1.2.3.4.5"
        generator = RandomUIDGenerator(custom_root)
        assert generator.root_uid == custom_root
    
    def test_invalid_root_uid_rejected(self):
        """Test that invalid root UIDs are rejected."""
        invalid_roots = [
            "",  # empty
            ".1.2.3",  # starts with dot
            "1.2.3.",  # ends with dot
            "1..2.3",  # consecutive dots
            "1.2.a.3",  # contains letters
            "1.2.3-4",  # contains dashes
        ]
        
        for invalid_root in invalid_roots:
            with pytest.raises(UIDGenerationError):
                RandomUIDGenerator(invalid_root)


class TestHashBasedUIDGenerator:
    """Test hash-based UID generation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.generator = HashBasedUIDGenerator()
    
    def test_same_seed_produces_same_uid(self):
        """Test that same seed data produces same UID."""
        generator = HashBasedUIDGenerator()
        seed = "test_data"
        
        uid1 = generator.generate_uid(seed)
        uid2 = generator.generate_uid(seed)
        
        assert uid1 == uid2
    
    def test_different_seeds_produce_different_uids(self):
        """Test that different seed data produces different UIDs."""
        generator = HashBasedUIDGenerator()
        
        uid1 = generator.generate_uid("seed1")
        uid2 = generator.generate_uid("seed2")
        
        assert uid1 != uid2
    
    def test_no_seed_uses_timestamp(self):
        """Test that no seed uses timestamp for uniqueness."""
        generator = HashBasedUIDGenerator()
        
        with patch('time.time', return_value=12345.0):
            uid1 = generator.generate_uid()
            
        with patch('time.time', return_value=67890.0):
            uid2 = generator.generate_uid()
            
        assert uid1 != uid2
    
    def test_bytes_and_string_seeds_work(self):
        """Test that both bytes and string seeds work."""
        generator = HashBasedUIDGenerator()
        
        uid_from_string = generator.generate_uid("test")
        uid_from_bytes = generator.generate_uid(b"test")
        
        # Should be same since string gets converted to bytes
        assert uid_from_string == uid_from_bytes
    
    def test_generated_uid_format_compliance(self):
        """Test that generated UIDs meet DICOM format requirements."""
        generator = HashBasedUIDGenerator()
        uid = generator.generate_uid("test")
        
        # Should start with root UID
        assert uid.startswith(generator.root_uid)
        
        # Should contain only digits and dots
        assert all(c.isdigit() or c == '.' for c in uid)
        
        # Should not exceed maximum length
        assert len(uid) <= UIDGenerator.MAX_UID_LENGTH
    
    def test_specialized_uid_methods(self):
        """Test specialized UID generation methods."""
        generator = HashBasedUIDGenerator()
        
        study_uid = generator.generate_study_instance_uid("study_data")
        series_uid = generator.generate_series_instance_uid("series_data") 
        instance_uid = generator.generate_sop_instance_uid("instance_data")
        frame_uid = generator.generate_frame_of_reference_uid("frame_data")
        
        # All should be valid UIDs
        uids = [study_uid, series_uid, instance_uid, frame_uid]
        for uid in uids:
            assert uid.startswith(generator.root_uid)
            assert len(uid) <= UIDGenerator.MAX_UID_LENGTH
        
        # All should be different (different seeds)
        assert len(set(uids)) == 4


class TestRandomUIDGenerator:
    """Test random UID generation."""
    
    def test_generates_unique_uids(self):
        """Test that multiple calls generate unique UIDs."""
        generator = RandomUIDGenerator()
        
        uids = [generator.generate_uid() for _ in range(100)]
        
        # All should be unique
        assert len(set(uids)) == 100
    
    def test_seed_data_ignored(self):
        """Test that seed data is ignored in random generation."""
        generator = RandomUIDGenerator()
        
        # Same seed should still produce different UIDs
        uid1 = generator.generate_uid("same_seed")
        uid2 = generator.generate_uid("same_seed")
        
        assert uid1 != uid2
    
    def test_generated_uid_format_compliance(self):
        """Test that generated UIDs meet DICOM format requirements."""
        generator = RandomUIDGenerator()
        uid = generator.generate_uid()
        
        # Should start with root UID
        assert uid.startswith(generator.root_uid)
        
        # Should contain only digits and dots
        assert all(c.isdigit() or c == '.' for c in uid)
        
        # Should not exceed maximum length
        assert len(uid) <= UIDGenerator.MAX_UID_LENGTH
    
    def test_no_collisions_in_large_sample(self):
        """Test no UID collisions in large sample."""
        generator = RandomUIDGenerator()
        
        # Generate 10,000 UIDs as specified in success criteria
        uids = [generator.generate_uid() for _ in range(10000)]
        
        # Should all be unique
        assert len(set(uids)) == 10000
    
    def test_specialized_uid_methods(self):
        """Test specialized UID generation methods."""
        generator = RandomUIDGenerator()
        
        study_uid = generator.generate_study_instance_uid()
        series_uid = generator.generate_series_instance_uid()
        instance_uid = generator.generate_sop_instance_uid()
        frame_uid = generator.generate_frame_of_reference_uid()
        
        # All should be valid UIDs
        uids = [study_uid, series_uid, instance_uid, frame_uid]
        for uid in uids:
            assert uid.startswith(generator.root_uid)
            assert len(uid) <= UIDGenerator.MAX_UID_LENGTH
        
        # All should be different (random generation)
        assert len(set(uids)) == 4


class TestDefaultUIDGenerator:
    """Test default UID generator factory."""
    
    def test_create_hash_generator(self):
        """Test creating hash-based generator."""
        generator = DefaultUIDGenerator.create_hash_generator()
        assert isinstance(generator, HashBasedUIDGenerator)
    
    def test_create_random_generator(self):
        """Test creating random generator."""
        generator = DefaultUIDGenerator.create_random_generator()
        assert isinstance(generator, RandomUIDGenerator)
    
    def test_create_default_generator(self):
        """Test creating default generator."""
        generator = DefaultUIDGenerator.create_default_generator()
        assert isinstance(generator, RandomUIDGenerator)
    
    def test_custom_root_uid(self):
        """Test creating generators with custom root UIDs."""
        custom_root = "1.2.3.4.5"
        
        hash_gen = DefaultUIDGenerator.create_hash_generator(custom_root)
        random_gen = DefaultUIDGenerator.create_random_generator(custom_root)
        
        assert hash_gen.root_uid == custom_root
        assert random_gen.root_uid == custom_root


class TestUIDValidation:
    """Test UID validation logic."""
    
    def test_uid_length_validation(self):
        """Test that overly long UIDs are rejected."""
        # Create a generator that would produce a very long UID
        very_long_root = "1.2.3." + "4." * 100  # Very long root
        
        with pytest.raises(UIDGenerationError):
            generator = RandomUIDGenerator(very_long_root)
            generator.generate_uid()
    
    def test_uid_format_validation(self):
        """Test UID format validation catches issues."""
        generator = RandomUIDGenerator()
        
        # Mock a scenario where an invalid UID gets generated
        with patch.object(generator, 'generate_uid', return_value="invalid_uid"):
            with pytest.raises(UIDGenerationError):
                generator._validate_generated_uid("invalid_uid")


class TestPyMedPhysCompatibility:
    """Test PyMedPhys-style UID generation compatibility."""
    
    def test_hash_strategy_reproducible(self):
        """Test that hash strategy is reproducible like PyMedPhys."""
        generator1 = HashBasedUIDGenerator()
        generator2 = HashBasedUIDGenerator()
        
        seed_data = "patient_123_study_456"
        
        uid1 = generator1.generate_uid(seed_data)
        uid2 = generator2.generate_uid(seed_data)
        
        assert uid1 == uid2
    
    def test_random_strategy_unique(self):
        """Test that random strategy provides uniqueness."""
        generator = RandomUIDGenerator()
        
        # Even with identical conditions, should be unique
        uids = [generator.generate_uid() for _ in range(1000)]
        assert len(set(uids)) == 1000