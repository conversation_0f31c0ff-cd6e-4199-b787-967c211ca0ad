"""
Tests for UID registry functionality.

Tests UID relationship consistency and hierarchy management.
"""

import pytest
from unittest.mock import MagicMock

from pyrt_dicom.uid_generation.registry import UIDRegistry, UIDRelationship
from pyrt_dicom.uid_generation.generators import RandomUIDGenerator
from pyrt_dicom.utils.exceptions import UIDGenerationError


class TestUIDRegistry:
    """Test UID registry basic functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.registry = UIDRegistry()
    
    def test_initialization_with_default_generator(self):
        """Test registry initializes with default UID generator."""
        registry = UIDRegistry()
        assert registry.uid_generator is not None
        assert isinstance(registry.uid_generator, RandomUIDGenerator)
    
    def test_initialization_with_custom_generator(self):
        """Test registry initializes with custom UID generator."""
        custom_generator = MagicMock()
        registry = UIDRegistry(custom_generator)
        assert registry.uid_generator is custom_generator
    
    def test_register_study_uid(self):
        """Test registering study UID."""
        registry = UIDRegistry()
        study_uid = "*******.5"
        
        result = registry.register_study_uid(study_uid)
        
        assert result == study_uid
        assert study_uid in registry._study_uids
    
    def test_register_duplicate_study_uid_fails(self):
        """Test that registering duplicate study UID fails."""
        registry = UIDRegistry()
        study_uid = "*******.5"
        
        registry.register_study_uid(study_uid)
        
        with pytest.raises(UIDGenerationError, match="already registered"):
            registry.register_study_uid(study_uid)
    
    def test_register_series_uid(self):
        """Test registering series UID with parent study."""
        registry = UIDRegistry()
        study_uid = "*******.5"
        series_uid = "*******.6"
        
        registry.register_study_uid(study_uid)
        result = registry.register_series_uid(series_uid, study_uid)
        
        assert result == series_uid
        assert series_uid in registry._series_uids
        assert series_uid in registry.get_study_series(study_uid)
        assert registry.get_series_study(series_uid) == study_uid
    
    def test_register_series_uid_without_study_fails(self):
        """Test that registering series UID without parent study fails."""
        registry = UIDRegistry()
        series_uid = "*******.6"
        nonexistent_study = "*******.5"
        
        with pytest.raises(UIDGenerationError, match="Study UID not registered"):
            registry.register_series_uid(series_uid, nonexistent_study)
    
    def test_register_instance_uid(self):
        """Test registering instance UID with parent series."""
        registry = UIDRegistry()
        study_uid = "*******.5"
        series_uid = "*******.6"
        instance_uid = "*******.7"
        
        registry.register_study_uid(study_uid)
        registry.register_series_uid(series_uid, study_uid)
        result = registry.register_instance_uid(instance_uid, series_uid)
        
        assert result == instance_uid
        assert instance_uid in registry._instance_uids
        assert instance_uid in registry.get_series_instances(series_uid)
        assert registry.get_instance_series(instance_uid) == series_uid
    
    def test_register_frame_reference_uid(self):
        """Test registering frame of reference UID with study."""
        registry = UIDRegistry()
        study_uid = "*******.5"
        frame_ref_uid = "*******.8"
        
        registry.register_study_uid(study_uid)
        result = registry.register_frame_reference_uid(frame_ref_uid, study_uid)
        
        assert result == frame_ref_uid
        assert frame_ref_uid in registry._frame_reference_uids
        assert frame_ref_uid in registry.get_study_frame_references(study_uid)


class TestUIDRegistryCreationMethods:
    """Test UID registry creation and registration methods."""
    
    def test_create_and_register_study_uid(self):
        """Test creating and registering study UID in one call."""
        registry = UIDRegistry()
        
        study_uid = registry.create_and_register_study_uid()
        
        assert study_uid in registry._study_uids
        assert len(study_uid) > 0
    
    def test_create_and_register_series_uid(self):
        """Test creating and registering series UID in one call."""
        registry = UIDRegistry()
        
        study_uid = registry.create_and_register_study_uid()
        series_uid = registry.create_and_register_series_uid(study_uid)
        
        assert series_uid in registry._series_uids
        assert series_uid in registry.get_study_series(study_uid)
    
    def test_create_and_register_instance_uid(self):
        """Test creating and registering instance UID in one call."""
        registry = UIDRegistry()
        
        study_uid = registry.create_and_register_study_uid()
        series_uid = registry.create_and_register_series_uid(study_uid)
        instance_uid = registry.create_and_register_instance_uid(series_uid)
        
        assert instance_uid in registry._instance_uids
        assert instance_uid in registry.get_series_instances(series_uid)
    
    def test_create_and_register_frame_reference_uid(self):
        """Test creating and registering frame reference UID in one call."""
        registry = UIDRegistry()
        
        study_uid = registry.create_and_register_study_uid()
        frame_ref_uid = registry.create_and_register_frame_reference_uid(study_uid)
        
        assert frame_ref_uid in registry._frame_reference_uids
        assert frame_ref_uid in registry.get_study_frame_references(study_uid)


class TestUIDRegistryQueries:
    """Test UID registry query methods."""
    
    def test_get_study_series(self):
        """Test getting series for a study."""
        registry = UIDRegistry()
        study_uid = "*******.5"
        series1_uid = "*******.6"
        series2_uid = "*******.7"
        
        registry.register_study_uid(study_uid)
        registry.register_series_uid(series1_uid, study_uid)
        registry.register_series_uid(series2_uid, study_uid)
        
        series = registry.get_study_series(study_uid)
        assert len(series) == 2
        assert series1_uid in series
        assert series2_uid in series
    
    def test_get_study_series_nonexistent_study(self):
        """Test getting series for nonexistent study fails."""
        registry = UIDRegistry()
        
        with pytest.raises(UIDGenerationError, match="Study UID not registered"):
            registry.get_study_series("nonexistent")
    
    def test_get_series_instances(self):
        """Test getting instances for a series."""
        registry = UIDRegistry()
        study_uid = "*******.5"
        series_uid = "*******.6"
        instance1_uid = "*******.7"
        instance2_uid = "*******.8"
        
        registry.register_study_uid(study_uid)
        registry.register_series_uid(series_uid, study_uid)
        registry.register_instance_uid(instance1_uid, series_uid)
        registry.register_instance_uid(instance2_uid, series_uid)
        
        instances = registry.get_series_instances(series_uid)
        assert len(instances) == 2
        assert instance1_uid in instances
        assert instance2_uid in instances
    
    def test_get_study_frame_references(self):
        """Test getting frame references for a study."""
        registry = UIDRegistry()
        study_uid = "*******.5"
        frame_ref1_uid = "*******.9"
        frame_ref2_uid = "*******.10"
        
        registry.register_study_uid(study_uid)
        registry.register_frame_reference_uid(frame_ref1_uid, study_uid)
        registry.register_frame_reference_uid(frame_ref2_uid, study_uid)
        
        frame_refs = registry.get_study_frame_references(study_uid)
        assert len(frame_refs) == 2
        assert frame_ref1_uid in frame_refs
        assert frame_ref2_uid in frame_refs


class TestUIDRegistryRelationshipTracking:
    """Test UID registry relationship tracking."""
    
    def test_relationship_creation(self):
        """Test that relationships are created and tracked."""
        registry = UIDRegistry()
        study_uid = "*******.5"
        series_uid = "*******.6"
        
        registry.register_study_uid(study_uid)
        registry.register_series_uid(series_uid, study_uid)
        
        # Should have one relationship
        assert len(registry._relationships) == 1
        
        relationship = registry._relationships[0]
        assert relationship.parent_uid == study_uid
        assert relationship.child_uid == series_uid
        assert relationship.relationship_type == 'study-series'
    
    def test_metadata_storage(self):
        """Test that metadata is stored with UIDs and relationships."""
        registry = UIDRegistry()
        study_uid = "*******.5"
        series_uid = "*******.6"
        metadata = {'modality': 'CT', 'description': 'Test series'}
        
        registry.register_study_uid(study_uid)
        registry.register_series_uid(series_uid, study_uid, metadata)
        
        relationship = registry._relationships[0]
        assert relationship.metadata == metadata


class TestUIDRegistryValidation:
    """Test UID registry validation functionality."""
    
    def test_validate_hierarchy_valid(self):
        """Test validation passes for valid hierarchy."""
        registry = UIDRegistry()
        
        study_uid = registry.create_and_register_study_uid()
        series_uid = registry.create_and_register_series_uid(study_uid)
        instance_uid = registry.create_and_register_instance_uid(series_uid)
        
        errors = registry.validate_hierarchy()
        assert len(errors) == 0
    
    def test_validate_hierarchy_detects_orphaned_series(self):
        """Test validation detects series without valid parent study."""
        registry = UIDRegistry()
        
        # Manually create invalid state
        series_uid = "*******.6"
        registry._series_uids.add(series_uid)
        # Don't add to _series_to_study mapping
        
        errors = registry.validate_hierarchy()
        assert len(errors) > 0
        assert "has no parent study" in errors[0]
    
    def test_validate_hierarchy_detects_invalid_parent_study(self):
        """Test validation detects series with nonexistent parent study."""
        registry = UIDRegistry()
        
        # Manually create invalid state
        series_uid = "*******.6"
        nonexistent_study = "*******.5"
        registry._series_uids.add(series_uid)
        registry._series_to_study[series_uid] = nonexistent_study
        # Don't register the study UID
        
        errors = registry.validate_hierarchy()
        assert len(errors) > 0
        assert "references non-existent study" in errors[0]


class TestUIDRegistryUtilities:
    """Test UID registry utility methods."""
    
    def test_get_registry_summary(self):
        """Test getting registry summary."""
        registry = UIDRegistry()
        
        study_uid = registry.create_and_register_study_uid()
        series_uid = registry.create_and_register_series_uid(study_uid)
        instance_uid = registry.create_and_register_instance_uid(series_uid)
        frame_ref_uid = registry.create_and_register_frame_reference_uid(study_uid)
        
        summary = registry.get_registry_summary()
        
        assert summary['studies'] == 1
        assert summary['series'] == 1
        assert summary['instances'] == 1
        assert summary['frame_references'] == 1
        assert summary['relationships'] == 3  # study-series, series-instance, frame-reference
    
    def test_clear_registry(self):
        """Test clearing registry data."""
        registry = UIDRegistry()
        
        # Add some data
        study_uid = registry.create_and_register_study_uid()
        series_uid = registry.create_and_register_series_uid(study_uid)
        
        # Clear and verify
        registry.clear()
        
        summary = registry.get_registry_summary()
        assert all(count == 0 for count in summary.values())


class TestUIDRelationshipDataClass:
    """Test UIDRelationship data class."""
    
    def test_relationship_creation(self):
        """Test creating relationship objects."""
        relationship = UIDRelationship(
            parent_uid="*******.5",
            child_uid="*******.6",
            relationship_type="study-series",
            metadata={'key': 'value'}
        )
        
        assert relationship.parent_uid == "*******.5"
        assert relationship.child_uid == "*******.6"
        assert relationship.relationship_type == "study-series"
        assert relationship.metadata == {'key': 'value'}
    
    def test_relationship_default_metadata(self):
        """Test relationship with default empty metadata."""
        relationship = UIDRelationship(
            parent_uid="*******.5",
            child_uid="*******.6",
            relationship_type="study-series"
        )
        
        assert relationship.metadata == {}


class TestUIDRegistryComplexScenarios:
    """Test UID registry with complex clinical scenarios."""
    
    def test_multi_series_study(self):
        """Test study with multiple series and instances."""
        registry = UIDRegistry()
        
        # Create study with multiple series
        study_uid = registry.create_and_register_study_uid()
        ct_series_uid = registry.create_and_register_series_uid(study_uid, {'modality': 'CT'})
        struct_series_uid = registry.create_and_register_series_uid(study_uid, {'modality': 'RTSTRUCT'})
        dose_series_uid = registry.create_and_register_series_uid(study_uid, {'modality': 'RTDOSE'})
        
        # Add instances to CT series
        for i in range(100):  # Typical CT series size
            registry.create_and_register_instance_uid(ct_series_uid)
        
        # Add single instances for RT objects
        registry.create_and_register_instance_uid(struct_series_uid)
        registry.create_and_register_instance_uid(dose_series_uid)
        
        # Add frame of reference
        frame_ref_uid = registry.create_and_register_frame_reference_uid(study_uid)
        
        # Verify structure
        study_series = registry.get_study_series(study_uid)
        assert len(study_series) == 3
        
        ct_instances = registry.get_series_instances(ct_series_uid)
        assert len(ct_instances) == 100
        
        struct_instances = registry.get_series_instances(struct_series_uid)
        assert len(struct_instances) == 1
        
        frame_refs = registry.get_study_frame_references(study_uid)
        assert len(frame_refs) == 1
        
        # Validate hierarchy
        errors = registry.validate_hierarchy()
        assert len(errors) == 0
    
    def test_multiple_studies(self):
        """Test registry with multiple studies."""
        registry = UIDRegistry()
        
        # Create multiple studies
        studies = []
        for i in range(5):
            study_uid = registry.create_and_register_study_uid()
            studies.append(study_uid)
            
            # Each study has a series with instances
            series_uid = registry.create_and_register_series_uid(study_uid)
            registry.create_and_register_instance_uid(series_uid)
            registry.create_and_register_frame_reference_uid(study_uid)
        
        summary = registry.get_registry_summary()
        assert summary['studies'] == 5
        assert summary['series'] == 5
        assert summary['instances'] == 5
        assert summary['frame_references'] == 5
        
        # Validate all relationships
        errors = registry.validate_hierarchy()
        assert len(errors) == 0