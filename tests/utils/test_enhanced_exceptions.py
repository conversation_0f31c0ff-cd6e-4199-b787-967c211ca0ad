"""
Tests for enhanced exception context and recovery functionality.

Validates that enhanced exceptions provide actionable suggestions, clinical context,
DICOM references, and comprehensive debugging information for medical physics workflows.
"""

import pytest
from pyrt_dicom.utils.exceptions import (
    PyrtDicomError,
    DicomCreationError,
    ValidationError,
    CoordinateSystemError,
    UIDGenerationError,
    TemplateError,
)


class TestEnhancedExceptionBase:
    """Test enhanced base exception functionality."""

    def test_base_exception_with_suggestions(self):
        """Test PyrtDicomError with actionable suggestions."""
        suggestions = [
            "Check input data format",
            "Verify DICOM compliance",
            "Enable debug logging"
        ]
        
        error = PyrtDicomError(
            "Base error with suggestions",
            suggestions=suggestions
        )
        
        assert error.suggestions == suggestions
        assert "Suggestions for resolution:" in str(error)
        assert "1. Check input data format" in str(error)
        assert "2. Verify DICOM compliance" in str(error)
        assert "3. Enable debug logging" in str(error)

    def test_base_exception_with_clinical_context(self):
        """Test PyrtDicomError with clinical context information."""
        clinical_context = {
            'valid_range': (0.1, 50.0),
            'units': 'mm',
            'current_value': 75.5,
            'typical_values': '1.0-5.0 for CT, 2.0-10.0 for MR'
        }
        
        error = PyrtDicomError(
            "Error with clinical context",
            clinical_context=clinical_context
        )
        
        assert error.clinical_context == clinical_context
        error_str = str(error)
        assert "Valid range: 0.1 - 50.0" in error_str
        assert "Units: mm" in error_str
        assert "Current value: 75.5" in error_str
        assert "Typical values: 1.0-5.0 for CT, 2.0-10.0 for MR" in error_str

    def test_base_exception_with_dicom_reference(self):
        """Test PyrtDicomError with DICOM standard reference."""
        error = PyrtDicomError(
            "Error with DICOM reference",
            dicom_reference="PS 3.3, C.7.6.1 (General Image Module)"
        )
        
        assert error.dicom_reference == "PS 3.3, C.7.6.1 (General Image Module)"
        assert "DICOM Reference: PS 3.3, C.7.6.1 (General Image Module)" in str(error)

    def test_base_exception_comprehensive(self):
        """Test PyrtDicomError with all enhanced features."""
        error = PyrtDicomError(
            "Comprehensive error example",
            suggestions=["Suggestion 1", "Suggestion 2"],
            clinical_context={'units': 'Gy', 'valid_range': (0.01, 30.0)},
            dicom_reference="PS 3.3, A.16 (RT Dose IOD)"
        )
        
        error_str = str(error)
        assert "Comprehensive error example" in error_str
        assert "Clinical Context:" in error_str
        assert "DICOM Reference:" in error_str
        assert "Suggestions for resolution:" in error_str
        assert "1. Suggestion 1" in error_str
        assert "2. Suggestion 2" in error_str

    def test_add_suggestion_method(self):
        """Test dynamic addition of suggestions."""
        error = PyrtDicomError("Base error")
        assert len(error.suggestions) == 0
        
        error.add_suggestion("First suggestion")
        error.add_suggestion("Second suggestion")
        
        assert len(error.suggestions) == 2
        assert "First suggestion" in error.suggestions
        assert "Second suggestion" in error.suggestions

    def test_add_clinical_context_method(self):
        """Test dynamic addition of clinical context."""
        error = PyrtDicomError("Base error")
        assert len(error.clinical_context) == 0
        
        error.add_clinical_context("units", "mm")
        error.add_clinical_context("valid_range", (0.1, 10.0))
        
        assert error.clinical_context["units"] == "mm"
        assert error.clinical_context["valid_range"] == (0.1, 10.0)


class TestDicomCreationErrorEnhanced:
    """Test enhanced DicomCreationError functionality."""

    def test_dicom_creation_error_with_missing_elements(self):
        """Test DicomCreationError with missing DICOM elements."""
        missing_elements = ["PatientID", "StudyInstanceUID", "SeriesInstanceUID"]
        
        error = DicomCreationError(
            "DICOM creation failed due to missing elements",
            missing_elements=missing_elements
        )
        
        assert "missing_elements" in error.clinical_context
        assert error.clinical_context["missing_elements"] == missing_elements
        assert any("Ensure all required DICOM elements are populated" in s for s in error.suggestions)
        assert any("Check DICOM IOD specification" in s for s in error.suggestions)
        assert "PS 3.5 (Data Structures and Encoding)" in error.dicom_reference

    def test_dicom_creation_error_with_invalid_data_types(self):
        """Test DicomCreationError with invalid data types."""
        invalid_types = {"PatientAge": "should be string, got int", "PixelSpacing": "should be list, got float"}
        
        error = DicomCreationError(
            "Invalid data types for DICOM elements",
            invalid_data_types=invalid_types
        )
        
        assert "invalid_data_types" in error.clinical_context
        assert error.clinical_context["invalid_data_types"] == invalid_types
        assert any("Verify data types match DICOM VR" in s for s in error.suggestions)
        assert any("Convert numeric values to appropriate Python types" in s for s in error.suggestions)

    def test_dicom_creation_error_with_file_path(self):
        """Test DicomCreationError with file path context."""
        file_path = "/path/to/output.dcm"
        
        error = DicomCreationError(
            "Failed to write DICOM file",
            file_path=file_path
        )
        
        assert "file_path" in error.clinical_context
        assert error.clinical_context["file_path"] == file_path
        assert any("Verify output directory exists and is writable" in s for s in error.suggestions)
        assert any("Check available disk space" in s for s in error.suggestions)

    def test_dicom_creation_error_default_suggestions(self):
        """Test DicomCreationError default suggestions when no specific context."""
        # Test simple error (no enhanced features - backward compatibility)
        simple_error = DicomCreationError("Generic DICOM creation error")
        assert len(simple_error.suggestions) == 0  # No suggestions in simple mode
        assert str(simple_error) == "Generic DICOM creation error"  # Simple string output
        
        # Test enhanced error (with suggestions parameter)
        enhanced_error = DicomCreationError(
            "Enhanced DICOM creation error", 
            suggestions=["Custom suggestion"]
        )
        assert len(enhanced_error.suggestions) >= 1
        assert "Custom suggestion" in enhanced_error.suggestions


class TestValidationErrorEnhanced:
    """Test enhanced ValidationError functionality."""

    def test_validation_error_clinical_type(self):
        """Test ValidationError with clinical validation type."""
        error = ValidationError(
            "Dose value exceeds clinical limits",
            parameter_name="dose_max",
            current_value=50.0,
            valid_range=(0.01, 30.0),
            units="Gy",
            validation_type="clinical"
        )
        
        assert error.clinical_context["parameter_name"] == "dose_max"
        assert error.clinical_context["current_value"] == 50.0
        assert error.clinical_context["valid_range"] == (0.01, 30.0)
        assert error.clinical_context["units"] == "Gy"
        assert error.clinical_context["validation_type"] == "Clinical Safety"
        
        assert "Adjust value to be within valid range: 0.01 - 30.0" in error.suggestions
        assert any("Review clinical protocols" in s for s in error.suggestions)
        assert any("Consult medical physics guidelines" in s for s in error.suggestions)

    def test_validation_error_geometric_type(self):
        """Test ValidationError with geometric validation type."""
        error = ValidationError(
            "Coordinate system inconsistency detected",
            validation_type="geometric"
        )
        
        assert error.clinical_context["validation_type"] == "Geometric Consistency"
        assert "PS 3.3, C.7.6.2 (Image Plane Module)" in error.dicom_reference
        assert any("Check coordinate system consistency" in s for s in error.suggestions)
        assert any("Verify image orientation and position parameters" in s for s in error.suggestions)

    def test_validation_error_dicom_compliance_type(self):
        """Test ValidationError with DICOM compliance validation type."""
        error = ValidationError(
            "DICOM standard compliance violation",
            validation_type="dicom_compliance"
        )
        
        assert error.clinical_context["validation_type"] == "DICOM Standard Compliance"
        assert "PS 3.3 (Information Object Definitions)" in error.dicom_reference
        assert any("Review DICOM standard requirements" in s for s in error.suggestions)
        assert any("Check VR (Value Representation) constraints" in s for s in error.suggestions)

    def test_validation_error_default_suggestions(self):
        """Test ValidationError default suggestions."""
        error = ValidationError("Generic validation error")
        
        # With validation_type="clinical" (default), we get clinical suggestions
        assert len(error.suggestions) >= 3
        assert any("Review clinical protocols" in s for s in error.suggestions)
        assert any("Consult medical physics guidelines" in s for s in error.suggestions)
        assert any("Verify data entry and unit conversions" in s for s in error.suggestions)


class TestCoordinateSystemErrorEnhanced:
    """Test enhanced CoordinateSystemError functionality."""

    def test_coordinate_system_error_with_frame_reference(self):
        """Test CoordinateSystemError with Frame of Reference UID."""
        frame_uid = "1.2.826.0.1.3680043.8.498.12345"
        
        error = CoordinateSystemError(
            "Frame of Reference UID mismatch",
            frame_of_reference_uid=frame_uid
        )
        
        assert "frame_of_reference_uid" in error.clinical_context
        assert error.clinical_context["frame_of_reference_uid"] == frame_uid
        assert any("Use reference CT image to establish consistent coordinate system" in s for s in error.suggestions)
        assert "PS 3.3, C.7.6.2 (Image Plane Module)" in error.dicom_reference

    def test_coordinate_system_error_with_patient_position(self):
        """Test CoordinateSystemError with patient position context."""
        error = CoordinateSystemError(
            "Invalid patient position",
            patient_position="INVALID"
        )
        
        assert "patient_position" in error.clinical_context
        assert error.clinical_context["patient_position"] == "INVALID"
        assert "typical_values" in error.clinical_context
        assert "HFS (Head First Supine)" in error.clinical_context["typical_values"]

    def test_coordinate_system_error_with_mismatch(self):
        """Test CoordinateSystemError with coordinate mismatch."""
        mismatch = 7.5  # Large mismatch
        
        error = CoordinateSystemError(
            "Large coordinate mismatch detected",
            coordinate_mismatch_mm=mismatch
        )
        
        assert "coordinate_mismatch_mm" in error.clinical_context
        assert error.clinical_context["coordinate_mismatch_mm"] == mismatch
        assert "tolerance_mm" in error.clinical_context
        assert any("Large coordinate mismatch detected - verify image registration" in s for s in error.suggestions)

    def test_coordinate_system_error_small_mismatch(self):
        """Test CoordinateSystemError with small coordinate mismatch."""
        mismatch = 0.8  # Small mismatch
        
        error = CoordinateSystemError(
            "Small coordinate mismatch detected",
            coordinate_mismatch_mm=mismatch
        )
        
        assert any("Small mismatch may be acceptable for clinical use" in s for s in error.suggestions)


class TestUIDGenerationErrorEnhanced:
    """Test enhanced UIDGenerationError functionality."""

    def test_uid_generation_error_with_uid_value(self):
        """Test UIDGenerationError with specific UID value."""
        uid_value = "1.2.826.0.1.3680043.8.498.invalid_characters_here!"
        
        error = UIDGenerationError(
            "Invalid UID format",
            uid_value=uid_value
        )
        
        assert "uid_value" in error.clinical_context
        assert error.clinical_context["uid_value"] == uid_value
        assert "uid_format" in error.clinical_context
        assert "max_length" in error.clinical_context
        assert "allowed_characters" in error.clinical_context
        assert "PS 3.5, Chapter 9 (Unique Identifiers)" in error.dicom_reference

    def test_uid_generation_error_too_long(self):
        """Test UIDGenerationError with UID that's too long."""
        long_uid = "1.2.826.0.1.3680043.8.498." + "1" * 100  # > 64 characters
        
        error = UIDGenerationError(
            "UID too long",
            uid_value=long_uid
        )
        
        assert "UID exceeds maximum length of 64 characters" in error.suggestions

    def test_uid_generation_error_invalid_characters(self):
        """Test UIDGenerationError with invalid characters."""
        invalid_uid = "1.2.826.0.1.3680043.8.498.abc123"  # contains letters
        
        error = UIDGenerationError(
            "UID contains invalid characters",
            uid_value=invalid_uid
        )
        
        assert "UID contains invalid characters (only digits and periods allowed)" in error.suggestions

    def test_uid_generation_error_with_root_uid(self):
        """Test UIDGenerationError with root UID context."""
        root_uid = "1.2.826.0.1.3680043.8.498"
        
        error = UIDGenerationError(
            "Root UID configuration issue",
            root_uid=root_uid
        )
        
        assert "root_uid" in error.clinical_context
        assert error.clinical_context["root_uid"] == root_uid
        assert any("Verify root UID is properly registered" in s for s in error.suggestions)

    def test_uid_generation_error_no_root_uid(self):
        """Test UIDGenerationError without root UID."""
        error = UIDGenerationError("Missing root UID configuration")
        
        assert any("Configure organizational root UID" in s for s in error.suggestions)


class TestTemplateErrorEnhanced:
    """Test enhanced TemplateError functionality."""

    def test_template_error_with_modality(self):
        """Test TemplateError with specific modality."""
        error = TemplateError(
            "RT Structure template error",
            modality="RTSTRUCT"
        )
        
        assert "modality" in error.clinical_context
        assert error.clinical_context["modality"] == "RTSTRUCT"
        assert "PS 3.3, A.19 (RT Structure Set IOD)" in error.dicom_reference
        assert any("Review RTSTRUCT IOD specification" in s for s in error.suggestions)

    def test_template_error_with_missing_attributes(self):
        """Test TemplateError with missing attributes."""
        missing_attrs = ["StructureSetROISequence", "ROIContourSequence"]
        
        error = TemplateError(
            "Missing required attributes",
            modality="RTSTRUCT",
            missing_attributes=missing_attrs
        )
        
        assert "missing_attributes" in error.clinical_context
        assert error.clinical_context["missing_attributes"] == missing_attrs
        assert "Populate missing required attributes: StructureSetROISequence, ROIContourSequence" in error.suggestions

    def test_template_error_with_iod_reference(self):
        """Test TemplateError with custom IOD reference."""
        iod_ref = "PS 3.3, A.16.1 (RT Dose IOD Modules)"
        
        error = TemplateError(
            "Custom IOD reference error",
            iod_reference=iod_ref
        )
        
        assert "iod_reference" in error.clinical_context
        assert error.clinical_context["iod_reference"] == iod_ref
        assert iod_ref in error.dicom_reference

    def test_template_error_all_modalities(self):
        """Test TemplateError DICOM references for all supported modalities."""
        modalities = ["CT", "RTSTRUCT", "RTDOSE", "RTPLAN"]
        expected_refs = [
            "A.3 (CT Image IOD)",
            "A.19 (RT Structure Set IOD)",
            "A.16 (RT Dose IOD)",
            "A.14 (RT Plan IOD)"
        ]
        
        for modality, expected_ref in zip(modalities, expected_refs):
            error = TemplateError(f"{modality} template error", modality=modality)
            assert expected_ref in error.dicom_reference


class TestEnhancedExceptionChaining:
    """Test exception chaining with enhanced exceptions."""

    def test_exception_chaining_preserves_context(self):
        """Test that exception chaining preserves enhanced context."""
        try:
            try:
                raise ValueError("Original pydicom error")
            except ValueError as original:
                raise ValidationError(
                    "Enhanced validation failed",
                    parameter_name="test_param",
                    current_value=42,
                    valid_range=(0, 10),
                    units="mm"
                ) from original
        except ValidationError as enhanced:
            # Verify enhanced context is preserved
            assert enhanced.clinical_context["parameter_name"] == "test_param"
            assert enhanced.clinical_context["current_value"] == 42
            assert enhanced.clinical_context["valid_range"] == (0, 10)
            assert enhanced.clinical_context["units"] == "mm"
            
            # Verify original exception is chained
            assert isinstance(enhanced.__cause__, ValueError)
            assert "Original pydicom error" in str(enhanced.__cause__)
            
            # Verify suggestions are available
            assert len(enhanced.suggestions) > 0


class TestClinicalScenarios:
    """Test enhanced exceptions in realistic clinical scenarios."""

    def test_dose_validation_scenario(self):
        """Test realistic dose validation error scenario."""
        error = ValidationError(
            "Dose value 45.0 Gy exceeds institutional protocol maximum of 30.0 Gy for this treatment site",
            parameter_name="prescription_dose",
            current_value=45.0,
            valid_range=(0.1, 30.0),
            units="Gy",
            validation_type="clinical"
        )
        
        error_str = str(error)
        assert "45.0 Gy exceeds institutional protocol" in error_str
        assert "Current value: 45.0" in error_str
        assert "Valid range: 0.1 - 30.0" in error_str
        assert "Units: Gy" in error_str
        assert "Review clinical protocols" in error_str
        assert "Clinical Safety" in error_str

    def test_structure_geometry_scenario(self):
        """Test realistic structure geometry error scenario."""
        error = CoordinateSystemError(
            "Structure contour extends beyond CT image bounds: contour max Z=250.5mm > image max Z=200.0mm",
            coordinate_mismatch_mm=50.5,
            clinical_context={
                'structure_name': 'PTV_Prostate',
                'contour_max_z': 250.5,
                'image_max_z': 200.0
            }
        )
        
        error_str = str(error)
        assert "contour max Z=250.5mm > image max Z=200.0mm" in error_str
        assert "coordinate_mismatch_mm: 50.5" in error_str
        assert "Large coordinate mismatch detected" in error_str
        assert "verify image registration" in error_str

    def test_uid_uniqueness_scenario(self):
        """Test realistic UID uniqueness error scenario."""
        duplicate_uid = "1.2.826.0.1.3680043.8.498.12345"
        
        error = UIDGenerationError(
            f"Duplicate UID detected: {duplicate_uid} already exists in current session",
            uid_value=duplicate_uid,
            uid_type="SOPInstanceUID",
            clinical_context={'session_uid_count': 1245}
        )
        
        error_str = str(error)
        assert duplicate_uid in error_str
        assert "already exists in current session" in error_str
        assert "SOPInstanceUID" in error_str
        assert "session_uid_count: 1245" in error_str
        assert "Ensure UID uniqueness across all generated objects" in error_str