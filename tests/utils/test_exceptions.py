"""
Tests for pyrt-dicom custom exception hierarchy.

Tests exception inheritance, messaging, and clinical context handling.
"""

import pytest
from pyrt_dicom.utils.exceptions import (
    PyrtDicomError,
    DicomCreationError,
    ValidationError,
    CoordinateSystemError,
    UIDGenerationError,
    TemplateError,
)


class TestExceptionHierarchy:
    """Test exception class inheritance structure."""

    def test_base_exception_inheritance(self):
        """Test that PyrtDicomError inherits from Exception."""
        assert issubclass(PyrtDicomError, Exception)

    def test_dicom_creation_error_inheritance(self):
        """Test DicomCreationError inheritance from base."""
        assert issubclass(DicomCreationError, PyrtDicomError)
        assert issubclass(DicomCreationError, Exception)

    def test_validation_error_inheritance(self):
        """Test ValidationError inheritance from base."""
        assert issubclass(ValidationError, PyrtDicomError)
        assert issubclass(ValidationError, Exception)

    def test_coordinate_system_error_inheritance(self):
        """Test CoordinateSystemError inheritance from base."""
        assert issubclass(CoordinateSystemError, PyrtDicomError)
        assert issubclass(CoordinateSystemError, Exception)

    def test_uid_generation_error_inheritance(self):
        """Test UIDGenerationError inheritance from base."""
        assert issubclass(UIDGenerationError, PyrtDicomError)
        assert issubclass(UIDGenerationError, Exception)

    def test_template_error_inheritance(self):
        """Test TemplateError inheritance from base."""
        assert issubclass(TemplateError, PyrtDicomError)
        assert issubclass(TemplateError, Exception)


class TestExceptionInstantiation:
    """Test exception instantiation and basic functionality."""

    def test_base_exception_instantiation(self):
        """Test creating PyrtDicomError instances."""
        error = PyrtDicomError("Base error message")
        assert str(error) == "Base error message"
        assert isinstance(error, Exception)

    def test_dicom_creation_error_instantiation(self):
        """Test creating DicomCreationError instances."""
        error = DicomCreationError("Failed to create DICOM dataset")
        assert str(error) == "Failed to create DICOM dataset"
        assert isinstance(error, PyrtDicomError)

    def test_validation_error_instantiation(self):
        """Test creating ValidationError instances."""
        error = ValidationError("Dose value out of clinical range")
        # ValidationError always provides clinical guidance
        assert "Dose value out of clinical range" in str(error)
        assert len(error.suggestions) >= 3  # Should have clinical suggestions
        assert isinstance(error, PyrtDicomError)

    def test_coordinate_system_error_instantiation(self):
        """Test creating CoordinateSystemError instances."""
        error = CoordinateSystemError("Frame of reference mismatch")
        assert str(error) == "Frame of reference mismatch"
        assert isinstance(error, PyrtDicomError)

    def test_uid_generation_error_instantiation(self):
        """Test creating UIDGenerationError instances."""
        error = UIDGenerationError("UID format violation")
        # UIDGenerationError always provides technical guidance
        assert "UID format violation" in str(error)
        assert len(error.suggestions) >= 3  # Should have UID generation suggestions
        assert isinstance(error, PyrtDicomError)

    def test_template_error_instantiation(self):
        """Test creating TemplateError instances."""
        error = TemplateError("Missing required IOD element")
        assert str(error) == "Missing required IOD element"
        assert isinstance(error, PyrtDicomError)


class TestExceptionRaising:
    """Test exception raising and catching behavior."""

    def test_catch_base_exception(self):
        """Test catching exceptions using base class."""
        with pytest.raises(PyrtDicomError):
            raise DicomCreationError("Test error")

        with pytest.raises(PyrtDicomError):
            raise ValidationError("Test error")

        with pytest.raises(PyrtDicomError):
            raise CoordinateSystemError("Test error")

    def test_catch_specific_exceptions(self):
        """Test catching specific exception types."""
        with pytest.raises(DicomCreationError):
            raise DicomCreationError("DICOM creation failed")

        with pytest.raises(ValidationError):
            raise ValidationError("Validation failed")

        with pytest.raises(CoordinateSystemError):
            raise CoordinateSystemError("Coordinate error")

        with pytest.raises(UIDGenerationError):
            raise UIDGenerationError("UID error")

        with pytest.raises(TemplateError):
            raise TemplateError("Template error")

    def test_exception_message_preservation(self):
        """Test that exception messages are preserved correctly."""
        test_message = "Detailed clinical error message with context"
        
        try:
            raise ValidationError(test_message)
        except ValidationError as e:
            # ValidationError adds clinical context, so check the message is included
            assert test_message in str(e)

    def test_exception_args_preservation(self):
        """Test that exception args are preserved."""
        args = ("Error message", "Additional context", 42)
        
        try:
            raise DicomCreationError(*args)
        except DicomCreationError as e:
            assert e.args == args


class TestClinicalErrorScenarios:
    """Test realistic clinical error scenarios."""

    def test_dose_validation_error(self):
        """Test dose validation error scenario."""
        with pytest.raises(ValidationError) as excinfo:
            raise ValidationError("Dose value 10000 cGy exceeds clinical maximum of 5000 cGy")
        
        assert "10000 cGy" in str(excinfo.value)
        assert "clinical maximum" in str(excinfo.value)

    def test_geometry_error(self):
        """Test geometry error scenario."""
        with pytest.raises(CoordinateSystemError) as excinfo:
            raise CoordinateSystemError(
                "Structure contour extends beyond CT image bounds: "
                "contour max Z=250.5mm > image max Z=200.0mm"
            )
        
        assert "250.5mm" in str(excinfo.value)
        assert "200.0mm" in str(excinfo.value)

    def test_uid_uniqueness_error(self):
        """Test UID uniqueness error scenario."""
        with pytest.raises(UIDGenerationError) as excinfo:
            raise UIDGenerationError(
                "Duplicate UID detected: 1.2.826.0.1.3680043.8.498.12345 "
                "already exists in current session"
            )
        
        assert "1.2.826.0.1.3680043.8.498.12345" in str(excinfo.value)
        assert "already exists" in str(excinfo.value)

    def test_dicom_element_missing_error(self):
        """Test DICOM element missing error scenario."""
        with pytest.raises(TemplateError) as excinfo:
            raise TemplateError(
                "Required DICOM element (0008,0060) Modality missing from CT template"
            )
        
        assert "(0008,0060)" in str(excinfo.value)
        assert "Modality" in str(excinfo.value)


class TestExceptionIntegration:
    """Test exception integration with other components."""

    def test_exception_chaining(self):
        """Test exception chaining for debugging."""
        try:
            try:
                raise ValueError("Original pydicom error")
            except ValueError as e:
                raise DicomCreationError("Failed to create RT structure") from e
        except DicomCreationError as e:
            assert "Failed to create RT structure" in str(e)
            assert isinstance(e.__cause__, ValueError)
            assert "Original pydicom error" in str(e.__cause__)

    def test_multiple_exception_types_handling(self):
        """Test handling multiple exception types in a single try block."""
        def risky_operation(operation_type):
            if operation_type == "validation":
                raise ValidationError("Validation failed")
            elif operation_type == "creation":
                raise DicomCreationError("Creation failed")
            elif operation_type == "coordinate":
                raise CoordinateSystemError("Coordinate error")
            
        # Test that we can catch all RT exceptions with base class
        for op_type in ["validation", "creation", "coordinate"]:
            with pytest.raises(PyrtDicomError):
                risky_operation(op_type)

    def test_exception_with_context_manager(self):
        """Test exceptions work properly with context managers."""
        class MockDicomCreator:
            def __enter__(self):
                return self
            
            def __exit__(self, exc_type, exc_val, exc_tb):
                if exc_type is ValidationError:
                    # Handle validation errors gracefully
                    return True  # Suppress the exception
                return False  # Let other exceptions propagate
        
        # Test suppressed exception
        with MockDicomCreator():
            raise ValidationError("This should be suppressed")
        
        # Test non-suppressed exception
        with pytest.raises(DicomCreationError):
            with MockDicomCreator():
                raise DicomCreationError("This should propagate")