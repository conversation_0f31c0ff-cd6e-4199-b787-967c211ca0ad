"""
Tests for pyrt-dicom clinical logging framework.

Tests log formatting, audit trail capabilities, and structured logging compliance.
"""

import json
import logging
from datetime import datetime
from io import StringIO

from pyrt_dicom.utils.logging import (
    ClinicalFormatter,
    get_clinical_logger,
    log_dicom_creation,
    log_validation_result,
)


class TestClinicalFormatter:
    """Test ClinicalFormatter JSON output and structure."""

    def test_formatter_basic_structure(self):
        """Test basic log record formatting produces valid JSON."""
        formatter = ClinicalFormatter()
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="/test/path.py",
            lineno=42,
            msg="Test message",
            args=(),
            exc_info=None,
        )
        record.module = "test_module"
        record.funcName = "test_function"

        formatted = formatter.format(record)

        # Should be valid JSON
        log_data = json.loads(formatted)

        # Check required fields
        assert "timestamp" in log_data
        assert "level" in log_data
        assert "module" in log_data
        assert "function" in log_data
        assert "line" in log_data
        assert "message" in log_data

        # Check field values
        assert log_data["level"] == "INFO"
        assert log_data["module"] == "test_module"
        assert log_data["function"] == "test_function"
        assert log_data["line"] == 42
        assert log_data["message"] == "Test message"

    def test_formatter_timestamp_format(self):
        """Test timestamp is properly formatted as ISO 8601."""
        formatter = ClinicalFormatter()
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="/test/path.py",
            lineno=1,
            msg="Test",
            args=(),
            exc_info=None,
        )
        record.module = "test"
        record.funcName = "test"

        formatted = formatter.format(record)
        log_data = json.loads(formatted)

        # Timestamp should be valid ISO 8601 format
        timestamp = log_data["timestamp"]
        parsed_timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        assert isinstance(parsed_timestamp, datetime)
        assert timestamp.endswith('+00:00') or 'T' in timestamp

    def test_formatter_clinical_context_fields(self):
        """Test clinical context fields are included when present."""
        formatter = ClinicalFormatter()
        record = logging.LogRecord(
            name="test_logger",
            level=logging.WARNING,
            pathname="/test/path.py",
            lineno=10,
            msg="Clinical operation",
            args=(),
            exc_info=None,
        )
        record.module = "clinical"
        record.funcName = "create_structure"
        record.patient_id = "PATIENT_123"
        record.study_uid = "1.2.826.0.1.3680043.8.498.12345"
        record.operation = "RT Structure creation"

        formatted = formatter.format(record)
        log_data = json.loads(formatted)

        # Check clinical fields
        assert log_data["patient_id"] == "PATIENT_123"
        assert log_data["study_uid"] == "1.2.826.0.1.3680043.8.498.12345"
        assert log_data["operation"] == "RT Structure creation"

    def test_formatter_validation_result_field(self):
        """Test validation result field formatting."""
        formatter = ClinicalFormatter()
        record = logging.LogRecord(
            name="test_logger",
            level=logging.ERROR,
            pathname="/test/path.py",
            lineno=50,
            msg="Validation failed",
            args=(),
            exc_info=None,
        )
        record.module = "validation"
        record.funcName = "validate_dose"
        record.validation_result = {
            "type": "dose_range",
            "passed": False,
            "details": {"max_dose": 10000, "limit": 5000}
        }

        formatted = formatter.format(record)
        log_data = json.loads(formatted)

        # Check validation result structure
        assert "validation_result" in log_data
        validation = log_data["validation_result"]
        assert validation["type"] == "dose_range"
        assert validation["passed"] is False
        assert validation["details"]["max_dose"] == 10000

    def test_formatter_message_with_args(self):
        """Test log message formatting with arguments."""
        formatter = ClinicalFormatter()
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="/test/path.py",
            lineno=20,
            msg="Processing %d structures for patient %s",
            args=(5, "PAT_001"),
            exc_info=None,
        )
        record.module = "processor"
        record.funcName = "process_structures"

        formatted = formatter.format(record)
        log_data = json.loads(formatted)

        # getMessage() should format the message with args
        assert log_data["message"] == "Processing 5 structures for patient PAT_001"

    def test_formatter_json_compactness(self):
        """Test JSON output is compact without extra whitespace."""
        formatter = ClinicalFormatter()
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="/test.py",
            lineno=1,
            msg="Test",
            args=(),
            exc_info=None,
        )
        record.module = "test"
        record.funcName = "test"

        formatted = formatter.format(record)

        # Should not contain extra whitespace
        assert ": " not in formatted
        assert ", " not in formatted
        # Should use compact separators
        assert ":" in formatted
        assert "," in formatted


class TestGetClinicalLogger:
    """Test clinical logger configuration and setup."""

    def test_logger_creation(self):
        """Test clinical logger is created properly."""
        logger = get_clinical_logger("test.module")

        assert isinstance(logger, logging.Logger)
        assert logger.name == "test.module"
        assert logger.level == logging.INFO

    def test_logger_has_clinical_formatter(self):
        """Test logger is configured with ClinicalFormatter."""
        logger = get_clinical_logger("test.formatter")

        assert len(logger.handlers) == 1
        handler = logger.handlers[0]
        assert isinstance(handler, logging.StreamHandler)
        assert isinstance(handler.formatter, ClinicalFormatter)

    def test_logger_duplicate_handler_prevention(self):
        """Test that calling get_clinical_logger multiple times doesn't add duplicate handlers."""
        logger1 = get_clinical_logger("test.duplicate")
        logger2 = get_clinical_logger("test.duplicate")

        # Should be the same logger instance
        assert logger1 is logger2
        # Should still have only one handler
        assert len(logger1.handlers) == 1
        assert len(logger2.handlers) == 1

    def test_logger_output_format(self):
        """Test logger produces properly formatted output."""
        # Capture log output
        stream = StringIO()

        # Create logger with custom handler for testing
        logger = logging.getLogger("test.output")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler(stream)
        handler.setFormatter(ClinicalFormatter())
        logger.addHandler(handler)

        logger.info("Test log message")

        output = stream.getvalue().strip()
        log_data = json.loads(output)

        assert log_data["level"] == "INFO"
        assert log_data["message"] == "Test log message"
        assert "timestamp" in log_data


class TestLogDicomCreation:
    """Test log_dicom_creation function."""

    def test_log_dicom_creation_basic(self):
        """Test basic DICOM creation logging."""
        stream = StringIO()
        logger = logging.getLogger("test.dicom.basic")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler(stream)
        handler.setFormatter(ClinicalFormatter())
        logger.addHandler(handler)

        log_dicom_creation(logger, "CT Series creation")

        output = stream.getvalue().strip()
        log_data = json.loads(output)

        assert log_data["level"] == "INFO"
        assert "DICOM creation: CT Series creation" in log_data["message"]
        assert log_data["operation"] == "CT Series creation"

    def test_log_dicom_creation_with_clinical_context(self):
        """Test DICOM creation logging with patient and study context."""
        stream = StringIO()
        logger = logging.getLogger("test.dicom.context")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler(stream)
        handler.setFormatter(ClinicalFormatter())
        logger.addHandler(handler)

        log_dicom_creation(
            logger,
            "RT Structure creation",
            patient_id="PAT_12345",
            study_uid="1.2.826.0.1.3680043.8.498.67890"
        )

        output = stream.getvalue().strip()
        log_data = json.loads(output)

        assert log_data["operation"] == "RT Structure creation"
        assert log_data["patient_id"] == "PAT_12345"
        assert log_data["study_uid"] == "1.2.826.0.1.3680043.8.498.67890"

    def test_log_dicom_creation_with_kwargs(self):
        """Test DICOM creation logging with additional keyword arguments."""
        stream = StringIO()
        logger = logging.getLogger("test.dicom.kwargs")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler(stream)
        handler.setFormatter(ClinicalFormatter())
        logger.addHandler(handler)

        log_dicom_creation(
            logger,
            "RT Dose creation",
            series_uid="1.2.826.0.1.3680043.8.498.11111",
            dose_units="Gy",
            max_dose=75.5
        )

        output = stream.getvalue().strip()
        log_data = json.loads(output)

        assert log_data["operation"] == "RT Dose creation"
        assert log_data["series_uid"] == "1.2.826.0.1.3680043.8.498.11111"
        assert log_data["dose_units"] == "Gy"
        assert log_data["max_dose"] == 75.5

    def test_log_dicom_creation_none_values(self):
        """Test DICOM creation logging handles None values properly."""
        stream = StringIO()
        logger = logging.getLogger("test.dicom.none")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler(stream)
        handler.setFormatter(ClinicalFormatter())
        logger.addHandler(handler)

        log_dicom_creation(
            logger,
            "Anonymous DICOM creation",
            patient_id=None,
            study_uid=None
        )

        output = stream.getvalue().strip()
        log_data = json.loads(output)

        assert log_data["operation"] == "Anonymous DICOM creation"
        assert log_data["patient_id"] is None
        assert log_data["study_uid"] is None


class TestLogValidationResult:
    """Test log_validation_result function."""

    def test_log_validation_result_passed(self):
        """Test validation result logging for passed validation."""
        stream = StringIO()
        logger = logging.getLogger("test.validation.pass")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler(stream)
        handler.setFormatter(ClinicalFormatter())
        logger.addHandler(handler)

        log_validation_result(
            logger,
            "dose_range",
            True,
            details={"max_dose": 65.0, "limit": 80.0}
        )

        output = stream.getvalue().strip()
        log_data = json.loads(output)

        assert log_data["level"] == "INFO"
        assert "Validation PASSED: dose_range" in log_data["message"]
        assert log_data["validation_result"]["type"] == "dose_range"
        assert log_data["validation_result"]["passed"] is True
        assert log_data["validation_result"]["details"]["max_dose"] == 65.0

    def test_log_validation_result_failed(self):
        """Test validation result logging for failed validation."""
        stream = StringIO()
        logger = logging.getLogger("test.validation.fail")
        logger.setLevel(logging.WARNING)
        handler = logging.StreamHandler(stream)
        handler.setFormatter(ClinicalFormatter())
        logger.addHandler(handler)

        log_validation_result(
            logger,
            "geometric_bounds",
            False,
            details={"contour_max_z": 250.5, "image_max_z": 200.0}
        )

        output = stream.getvalue().strip()
        log_data = json.loads(output)

        assert log_data["level"] == "WARNING"
        assert "Validation FAILED: geometric_bounds" in log_data["message"]
        assert log_data["validation_result"]["type"] == "geometric_bounds"
        assert log_data["validation_result"]["passed"] is False
        assert log_data["validation_result"]["details"]["contour_max_z"] == 250.5

    def test_log_validation_result_no_details(self):
        """Test validation result logging without details."""
        stream = StringIO()
        logger = logging.getLogger("test.validation.no_details")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler(stream)
        handler.setFormatter(ClinicalFormatter())
        logger.addHandler(handler)

        log_validation_result(logger, "simple_check", True)

        output = stream.getvalue().strip()
        log_data = json.loads(output)

        assert log_data["validation_result"]["type"] == "simple_check"
        assert log_data["validation_result"]["passed"] is True
        assert log_data["validation_result"]["details"] == {}

    def test_log_validation_result_with_kwargs(self):
        """Test validation result logging with additional context."""
        stream = StringIO()
        logger = logging.getLogger("test.validation.kwargs")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler(stream)
        handler.setFormatter(ClinicalFormatter())
        logger.addHandler(handler)

        log_validation_result(
            logger,
            "structure_volume",
            True,
            details={"volume_cc": 125.7},
            patient_id="PAT_999",
            structure_name="PTV"
        )

        output = stream.getvalue().strip()
        log_data = json.loads(output)

        assert log_data["validation_result"]["details"]["volume_cc"] == 125.7
        assert log_data["patient_id"] == "PAT_999"
        assert log_data["structure_name"] == "PTV"


class TestLoggingIntegration:
    """Test logging integration scenarios."""

    def test_clinical_workflow_logging(self):
        """Test complete clinical workflow logging scenario."""
        stream = StringIO()
        logger = get_clinical_logger("test.workflow")

        # Replace the default handler with our test handler
        logger.handlers.clear()
        handler = logging.StreamHandler(stream)
        handler.setFormatter(ClinicalFormatter())
        logger.addHandler(handler)

        # Simulate a clinical workflow
        patient_id = "PAT_WORKFLOW_001"
        study_uid = "1.2.826.0.1.3680043.8.498.99999"

        # Log DICOM creation
        log_dicom_creation(
            logger,
            "CT series creation from numpy array",
            patient_id=patient_id,
            study_uid=study_uid,
            slice_count=150
        )

        # Log validation
        log_validation_result(
            logger,
            "ct_geometry",
            True,
            details={"pixel_spacing": [0.98, 0.98], "slice_thickness": 2.5},
            patient_id=patient_id,
            study_uid=study_uid
        )

        # Log structure creation
        log_dicom_creation(
            logger,
            "RT structure set creation",
            patient_id=patient_id,
            study_uid=study_uid,
            structure_count=8
        )

        output = stream.getvalue()
        log_lines = [line.strip() for line in output.split('\n') if line.strip()]

        assert len(log_lines) == 3

        # Parse and validate each log entry
        ct_log = json.loads(log_lines[0])
        validation_log = json.loads(log_lines[1])
        struct_log = json.loads(log_lines[2])

        # Check consistency across entries
        for log_entry in [ct_log, validation_log, struct_log]:
            assert log_entry["patient_id"] == patient_id
            assert log_entry["study_uid"] == study_uid

        # Check specific content
        assert "CT series creation" in ct_log["message"]
        assert validation_log["validation_result"]["passed"] is True
        assert "RT structure set creation" in struct_log["message"]

    def test_error_handling_in_logging(self):
        """Test logging handles errors gracefully."""
        logger = get_clinical_logger("test.error_handling")

        # These should not raise exceptions
        log_dicom_creation(logger, "Test operation with problematic chars: äöü")
        log_validation_result(logger, "test", True, details={"complex": {"nested": "data"}})

        # Test with various data types
        log_dicom_creation(
            logger,
            "Data type test",
            integer_value=42,
            float_value=3.14159,
            boolean_value=True,
            none_value=None,
            list_value=[1, 2, 3],
            dict_value={"key": "value"}
        )

    def test_concurrent_logging_safety(self):
        """Test logging is safe for concurrent use."""
        import threading

        stream = StringIO()
        logger = logging.getLogger("test.concurrent")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler(stream)
        handler.setFormatter(ClinicalFormatter())
        logger.addHandler(handler)

        def log_worker(worker_id):
            for i in range(10):
                log_dicom_creation(
                    logger,
                    f"Worker {worker_id} operation {i}",
                    worker_id=worker_id,
                    operation_id=i
                )

        threads = []
        for worker_id in range(3):
            thread = threading.Thread(target=log_worker, args=(worker_id,))
            threads.append(thread)
            thread.start()

        for thread in threads:
            thread.join()

        output = stream.getvalue()
        log_lines = [line.strip() for line in output.split('\n') if line.strip()]

        # Should have 30 log entries (3 workers × 10 operations each)
        assert len(log_lines) == 30

        # Each line should be valid JSON
        for line in log_lines:
            log_data = json.loads(line)
            assert "worker_id" in log_data
            assert "operation_id" in log_data
            assert log_data["worker_id"] in [0, 1, 2]
            assert log_data["operation_id"] in range(10)


class TestLoggingEdgeCases:
    """Test edge cases and error conditions."""

    def test_formatter_with_missing_attributes(self):
        """Test formatter handles records with missing optional attributes."""
        formatter = ClinicalFormatter()

        # Create minimal record
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="/test.py",
            lineno=1,
            msg="Test",
            args=(),
            exc_info=None,
        )
        # Don't set module or funcName - should handle gracefully

        formatted = formatter.format(record)
        log_data = json.loads(formatted)

        # Should still be valid JSON with basic fields
        assert log_data["level"] == "INFO"
        assert log_data["message"] == "Test"

    def test_large_log_message_handling(self):
        """Test handling of large log messages."""
        logger = get_clinical_logger("test.large_message")

        # Create a large message
        large_message = "A" * 10000
        large_details = {"data": "B" * 5000}

        # Should not raise exceptions
        log_dicom_creation(logger, large_message, large_data=large_details)
        log_validation_result(logger, "large_test", True, details=large_details)

    def test_special_characters_in_log_data(self):
        """Test handling of special characters and unicode."""
        stream = StringIO()
        logger = logging.getLogger("test.special_chars")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler(stream)
        handler.setFormatter(ClinicalFormatter())
        logger.addHandler(handler)

        # Test various special characters
        log_dicom_creation(
            logger,
            "Test with special chars: äöü ñ 中文 🏥",
            patient_name="Müller, José",
            notes="Special symbols: \n\t\r\"'\\",
            unicode_test="Testing: αβγ δεζ"
        )

        output = stream.getvalue().strip()
        log_data = json.loads(output)  # Should parse successfully

        assert "äöü ñ 中文 🏥" in log_data["message"]
        assert log_data["patient_name"] == "Müller, José"
