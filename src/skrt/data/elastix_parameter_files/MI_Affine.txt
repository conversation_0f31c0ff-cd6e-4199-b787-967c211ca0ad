// **************** Image Types ******************************

(FixedInternalImagePixelType "float")
(MovingInternalImagePixelType "float")
// (FixedImageDimension 3)
// (MovingImageDimension 3)
(UseDirectionCosines "true")

// **************** Main Components **************************

(Registration "MultiResolutionRegistration")
(Interpolator "BSplineInterpolator")
(ResampleInterpolator "FinalBSplineInterpolator")
(Resampler "DefaultResampler")
(FixedImagePyramid "FixedRecursiveImagePyramid")
(MovingImagePyramid "MovingRecursiveImagePyramid")
(Optimizer "AdaptiveStochasticGradientDescent")
(Transform "AffineTransform")
(Metric "AdvancedMattesMutualInformation")

// ***************** Transformation **************************

(AutomaticScalesEstimation "true")
(AutomaticTransformInitialization "false")
// (AutomaticTransformInitialization "true")
(HowToCombineTransforms "Compose")

// ******************* Similarity measure *********************

//(UseNormalization "true")
(NumberOfHistogramBins 32)
(ErodeMask "true")
(RequiredRatioOfValidSamples 0.10)  // Use if fixed image is too large

// ******************** Multiresolution **********************

(NumberOfResolutions 4)
(ImagePyramidSchedule 8 8 4  4 4 2  2 2 1  1 1 1 )

// ******************* Optimizer ****************************

(MaximumNumberOfIterations 250)

// **************** Image sampling **********************

(NumberOfSpatialSamples 2048)
(NewSamplesEveryIteration "true")
(ImageSampler "Random")
// (ImageSampler "RandomSparseMask")  // use when fixed mask is too small

// ************* Interpolation and Resampling ****************

(BSplineInterpolationOrder 1)
(FinalBSplineInterpolationOrder 3)
(DefaultPixelValue 0)
(WriteResultImage "true")
(ResultImagePixelType "short")
(ResultImageFormat "nii")
