"""
Clinical logging framework for pyrt-dicom.

Provides structured logging with audit trail capabilities for clinical
compliance and debugging support.

## Usage Guide

### Basic Setup
```python
from pyrt_dicom.utils.logging import get_clinical_logger, log_dicom_creation, log_validation_result

# Create a clinical logger for your module
logger = get_clinical_logger(__name__)
```

### DICOM Creation Logging
```python
# Log DICOM creation operations with clinical context
log_dicom_creation(
    logger, 
    "CT series creation",
    patient_id="PAT_001",
    study_uid="1.2.826.0.1.3680043.8.498.12345",
    slice_count=150,
    modality="CT"
)
```

### Validation Logging
```python
# Log validation results (automatically uses INFO for pass, WARNING for fail)
log_validation_result(
    logger,
    "dose_range_check", 
    passed=True,
    details={"max_dose_gy": 75.0, "limit_gy": 80.0},
    patient_id="PAT_001"
)
```

## Output Format

All logs are output as compact JSON with these guaranteed fields:
- `timestamp`: ISO 8601 UTC timestamp
- `level`: Log level (INFO, WARNING, ERROR)
- `module`: Source module name
- `function`: Source function name  
- `line`: Source line number
- `message`: Human-readable log message

Plus any additional fields passed via the logging functions.

### Example Output
```json
{
    "timestamp": "2024-01-15T14:30:22.123456+00:00",
    "level": "INFO",
    "module": "ct_series",
    "function": "create_from_array",
    "line": 45,
    "message": "DICOM creation: CT series creation",
    "operation": "CT series creation",
    "patient_id": "PAT_001",
    "study_uid": "1.2.826.0.1.3680043.8.498.12345",
    "slice_count": 150
}
```

## Tips

1. **Always use clinical logging functions**: Use `log_dicom_creation()` and `log_validation_result()` 
   instead of direct logger calls for consistent clinical context.

2. **Include relevant clinical identifiers**: Always include `patient_id` and `study_uid` when available
   for audit trail requirements.

3. **Thread safety**: The logging system is thread-safe for concurrent DICOM operations.

4. **Performance**: JSON formatting is optimized with compact separators - no performance concerns
   for typical clinical workflows.

5. **Sensitive data**: Patient identifiers can be set to None for anonymous operations.
"""

import logging
import json
from datetime import datetime, timezone
from typing import Any, Dict, Optional


class ClinicalFormatter(logging.Formatter):
    """Custom formatter for clinical audit logging with structured output."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with clinical audit structure."""
        log_entry = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'level': record.levelname,
            'module': getattr(record, 'module', record.name.split('.')[-1]),
            'function': record.funcName,
            'line': record.lineno,
            'message': record.getMessage(),
        }
        
        # Add all extra fields from the record
        # Standard LogRecord attributes to exclude
        standard_attrs = {
            'name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 'filename',
            'module', 'lineno', 'funcName', 'created', 'msecs', 'relativeCreated',
            'thread', 'threadName', 'processName', 'process', 'getMessage', 'stack_info',
            'exc_info', 'exc_text', 'taskName'
        }
        
        # Add any extra attributes that were passed via the 'extra' parameter
        for attr, value in record.__dict__.items():
            if attr not in standard_attrs and not attr.startswith('_'):
                log_entry[attr] = value
            
        return json.dumps(log_entry, separators=(',', ':'))


def get_clinical_logger(name: str) -> logging.Logger:
    """Get a logger configured for clinical audit logging.
    
    Args:
        name: Logger name, typically __name__ from calling module
        
    Returns:
        Configured logger with clinical formatting
    """
    logger = logging.getLogger(name)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
        
    logger.setLevel(logging.INFO)
    
    # Console handler with clinical formatting
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(ClinicalFormatter())
    logger.addHandler(console_handler)
    
    return logger


def log_dicom_creation(
    logger: logging.Logger,
    operation: str,
    patient_id: Optional[str] = None,
    study_uid: Optional[str] = None,
    **kwargs: Any
) -> None:
    """Log DICOM creation operation with clinical context.
    
    Args:
        logger: Logger instance
        operation: Description of the DICOM creation operation
        patient_id: Patient identifier (if not anonymized)
        study_uid: Study instance UID
        **kwargs: Additional clinical or technical context
    """
    extra = {
        'operation': operation,
        'patient_id': patient_id,
        'study_uid': study_uid,
        **kwargs
    }
    
    logger.info(f"DICOM creation: {operation}", extra=extra)


def log_validation_result(
    logger: logging.Logger,
    validation_type: str,
    result: bool,
    details: Optional[Dict[str, Any]] = None,
    **kwargs: Any
) -> None:
    """Log validation results with clinical audit trail.
    
    Args:
        logger: Logger instance  
        validation_type: Type of validation performed
        result: Validation pass/fail result
        details: Additional validation details
        **kwargs: Additional context
    """
    extra = {
        'validation_result': {
            'type': validation_type,
            'passed': result,
            'details': details or {}
        },
        **kwargs
    }
    
    level = logging.INFO if result else logging.WARNING
    status = "PASSED" if result else "FAILED"
    logger.log(level, f"Validation {status}: {validation_type}", extra=extra)