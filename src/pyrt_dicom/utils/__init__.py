"""
Utility modules for pyrt-dicom.

This package contains support utilities:
- exceptions.py: Custom exception hierarchy for RT-specific errors
- logging.py: Clinical audit logging framework
- helpers.py: Common helper functions (to be implemented)
"""

from .exceptions import (
    PyrtDicomError,
    DicomCreationError, 
    ValidationError,
    CoordinateSystemError,
    UIDGenerationError,
    TemplateError
)

from .logging import (
    ClinicalFormatter,
    get_clinical_logger,
    log_dicom_creation,
    log_validation_result
)

__all__ = [
    # Exceptions
    'PyrtDicomError',
    'DicomCreationError',
    'ValidationError', 
    'CoordinateSystemError',
    'UIDGenerationError',
    'TemplateError',
    
    # Logging
    'ClinicalFormatter',
    'get_clinical_logger',
    'log_dicom_creation',
    'log_validation_result',
]