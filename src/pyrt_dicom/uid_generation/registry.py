"""
UID registry for maintaining DICOM UID relationships.

Tracks Study/Series/Instance hierarchy and ensures consistency across
related DICOM objects in RT workflows.
"""

from collections import defaultdict
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, field

from ..utils.exceptions import UIDGenerationError
from .generators import UIDGenerator, DefaultUIDGenerator


@dataclass
class UIDRelationship:
    """Represents relationship between DICOM UIDs."""
    parent_uid: str
    child_uid: str
    relationship_type: str  # 'study-series', 'series-instance', 'frame-reference'
    metadata: Dict[str, str] = field(default_factory=dict)


class UIDRegistry:
    """Registry for tracking UID relationships in DICOM hierarchies.
    
    Maintains Study/Series/Instance relationships and Frame of Reference
    consistency across RT DICOM objects.
    """
    
    def __init__(self, uid_generator: Optional[UIDGenerator] = None):
        """Initialize UID registry.
        
        Args:
            uid_generator: UID generator to use. If None, creates default.
        """
        self.uid_generator = uid_generator or DefaultUIDGenerator.create_default_generator()
        
        # UID storage
        self._study_uids: Set[str] = set()
        self._series_uids: Set[str] = set()
        self._instance_uids: Set[str] = set()
        self._frame_reference_uids: Set[str] = set()
        
        # Relationship tracking
        self._study_series: Dict[str, Set[str]] = defaultdict(set)
        self._series_instances: Dict[str, Set[str]] = defaultdict(set)
        self._frame_references: Dict[str, Set[str]] = defaultdict(set)
        
        # Reverse lookups
        self._series_to_study: Dict[str, str] = {}
        self._instance_to_series: Dict[str, str] = {}
        
        # Relationships for audit/debugging
        self._relationships: List[UIDRelationship] = []
    
    def register_study_uid(self, study_uid: str, metadata: Optional[Dict[str, str]] = None) -> str:
        """Register a Study Instance UID.
        
        Args:
            study_uid: Study Instance UID to register.
            metadata: Optional metadata about the study.
            
        Returns:
            Registered study UID.
            
        Raises:
            UIDGenerationError: If study UID already exists.
        """
        if study_uid in self._study_uids:
            raise UIDGenerationError(f"Study UID already registered: {study_uid}")
        
        self._study_uids.add(study_uid)
        return study_uid
    
    def register_series_uid(self, series_uid: str, study_uid: str, 
                           metadata: Optional[Dict[str, str]] = None) -> str:
        """Register a Series Instance UID linked to a Study.
        
        Args:
            series_uid: Series Instance UID to register.
            study_uid: Parent Study Instance UID.
            metadata: Optional metadata about the series.
            
        Returns:
            Registered series UID.
            
        Raises:
            UIDGenerationError: If series UID already exists or study UID not found.
        """
        if study_uid not in self._study_uids:
            raise UIDGenerationError(f"Study UID not registered: {study_uid}")
        
        if series_uid in self._series_uids:
            raise UIDGenerationError(f"Series UID already registered: {series_uid}")
        
        self._series_uids.add(series_uid)
        self._study_series[study_uid].add(series_uid)
        self._series_to_study[series_uid] = study_uid
        
        # Track relationship
        relationship = UIDRelationship(
            parent_uid=study_uid,
            child_uid=series_uid,
            relationship_type='study-series',
            metadata=metadata or {}
        )
        self._relationships.append(relationship)
        
        return series_uid
    
    def register_instance_uid(self, instance_uid: str, series_uid: str,
                             metadata: Optional[Dict[str, str]] = None) -> str:
        """Register a SOP Instance UID linked to a Series.
        
        Args:
            instance_uid: SOP Instance UID to register.
            series_uid: Parent Series Instance UID.
            metadata: Optional metadata about the instance.
            
        Returns:
            Registered instance UID.
            
        Raises:
            UIDGenerationError: If instance UID already exists or series UID not found.
        """
        if series_uid not in self._series_uids:
            raise UIDGenerationError(f"Series UID not registered: {series_uid}")
        
        if instance_uid in self._instance_uids:
            raise UIDGenerationError(f"Instance UID already registered: {instance_uid}")
        
        self._instance_uids.add(instance_uid)
        self._series_instances[series_uid].add(instance_uid)
        self._instance_to_series[instance_uid] = series_uid
        
        # Track relationship
        relationship = UIDRelationship(
            parent_uid=series_uid,
            child_uid=instance_uid,
            relationship_type='series-instance',
            metadata=metadata or {}
        )
        self._relationships.append(relationship)
        
        return instance_uid
    
    def register_frame_reference_uid(self, frame_ref_uid: str, study_uid: str,
                                   metadata: Optional[Dict[str, str]] = None) -> str:
        """Register a Frame of Reference UID linked to a Study.
        
        Args:
            frame_ref_uid: Frame of Reference UID to register.
            study_uid: Associated Study Instance UID.
            metadata: Optional metadata about the frame of reference.
            
        Returns:
            Registered frame of reference UID.
            
        Raises:
            UIDGenerationError: If frame reference UID already exists or study UID not found.
        """
        if study_uid not in self._study_uids:
            raise UIDGenerationError(f"Study UID not registered: {study_uid}")
        
        if frame_ref_uid in self._frame_reference_uids:
            raise UIDGenerationError(f"Frame of Reference UID already registered: {frame_ref_uid}")
        
        self._frame_reference_uids.add(frame_ref_uid)
        self._frame_references[study_uid].add(frame_ref_uid)
        
        # Track relationship
        relationship = UIDRelationship(
            parent_uid=study_uid,
            child_uid=frame_ref_uid,
            relationship_type='frame-reference',
            metadata=metadata or {}
        )
        self._relationships.append(relationship)
        
        return frame_ref_uid
    
    def create_and_register_study_uid(self, metadata: Optional[Dict[str, str]] = None) -> str:
        """Create and register a new Study Instance UID.
        
        Args:
            metadata: Optional metadata about the study.
            
        Returns:
            New Study Instance UID.
        """
        study_uid = self.uid_generator.generate_study_instance_uid()
        return self.register_study_uid(study_uid, metadata)
    
    def create_and_register_series_uid(self, study_uid: str, 
                                     metadata: Optional[Dict[str, str]] = None) -> str:
        """Create and register a new Series Instance UID.
        
        Args:
            study_uid: Parent Study Instance UID.
            metadata: Optional metadata about the series.
            
        Returns:
            New Series Instance UID.
        """
        series_uid = self.uid_generator.generate_series_instance_uid()
        return self.register_series_uid(series_uid, study_uid, metadata)
    
    def create_and_register_instance_uid(self, series_uid: str,
                                       metadata: Optional[Dict[str, str]] = None) -> str:
        """Create and register a new SOP Instance UID.
        
        Args:
            series_uid: Parent Series Instance UID.
            metadata: Optional metadata about the instance.
            
        Returns:
            New SOP Instance UID.
        """
        instance_uid = self.uid_generator.generate_sop_instance_uid()
        return self.register_instance_uid(instance_uid, series_uid, metadata)
    
    def create_and_register_frame_reference_uid(self, study_uid: str,
                                              metadata: Optional[Dict[str, str]] = None) -> str:
        """Create and register a new Frame of Reference UID.
        
        Args:
            study_uid: Associated Study Instance UID.
            metadata: Optional metadata about the frame of reference.
            
        Returns:
            New Frame of Reference UID.
        """
        frame_ref_uid = self.uid_generator.generate_frame_of_reference_uid()
        return self.register_frame_reference_uid(frame_ref_uid, study_uid, metadata)
    
    def get_study_series(self, study_uid: str) -> Set[str]:
        """Get all series UIDs for a study.
        
        Args:
            study_uid: Study Instance UID.
            
        Returns:
            Set of Series Instance UIDs.
            
        Raises:
            UIDGenerationError: If study UID not found.
        """
        if study_uid not in self._study_uids:
            raise UIDGenerationError(f"Study UID not registered: {study_uid}")
        
        return self._study_series[study_uid].copy()
    
    def get_series_instances(self, series_uid: str) -> Set[str]:
        """Get all instance UIDs for a series.
        
        Args:
            series_uid: Series Instance UID.
            
        Returns:
            Set of SOP Instance UIDs.
            
        Raises:
            UIDGenerationError: If series UID not found.
        """
        if series_uid not in self._series_uids:
            raise UIDGenerationError(f"Series UID not registered: {series_uid}")
        
        return self._series_instances[series_uid].copy()
    
    def get_study_frame_references(self, study_uid: str) -> Set[str]:
        """Get all frame of reference UIDs for a study.
        
        Args:
            study_uid: Study Instance UID.
            
        Returns:
            Set of Frame of Reference UIDs.
            
        Raises:
            UIDGenerationError: If study UID not found.
        """
        if study_uid not in self._study_uids:
            raise UIDGenerationError(f"Study UID not registered: {study_uid}")
        
        return self._frame_references[study_uid].copy()
    
    def get_series_study(self, series_uid: str) -> str:
        """Get study UID for a series.
        
        Args:
            series_uid: Series Instance UID.
            
        Returns:
            Study Instance UID.
            
        Raises:
            UIDGenerationError: If series UID not found.
        """
        if series_uid not in self._series_to_study:
            raise UIDGenerationError(f"Series UID not registered: {series_uid}")
        
        return self._series_to_study[series_uid]
    
    def get_instance_series(self, instance_uid: str) -> str:
        """Get series UID for an instance.
        
        Args:
            instance_uid: SOP Instance UID.
            
        Returns:
            Series Instance UID.
            
        Raises:
            UIDGenerationError: If instance UID not found.
        """
        if instance_uid not in self._instance_to_series:
            raise UIDGenerationError(f"Instance UID not registered: {instance_uid}")
        
        return self._instance_to_series[instance_uid]
    
    def validate_hierarchy(self) -> List[str]:
        """Validate UID hierarchy consistency.
        
        Returns:
            List of validation errors (empty if valid).
        """
        errors = []
        
        # Check all series have valid parent study
        for series_uid in self._series_uids:
            if series_uid not in self._series_to_study:
                errors.append(f"Series {series_uid} has no parent study")
            else:
                study_uid = self._series_to_study[series_uid]
                if study_uid not in self._study_uids:
                    errors.append(f"Series {series_uid} references non-existent study {study_uid}")
        
        # Check all instances have valid parent series
        for instance_uid in self._instance_uids:
            if instance_uid not in self._instance_to_series:
                errors.append(f"Instance {instance_uid} has no parent series")
            else:
                series_uid = self._instance_to_series[instance_uid]
                if series_uid not in self._series_uids:
                    errors.append(f"Instance {instance_uid} references non-existent series {series_uid}")
        
        return errors
    
    def get_registry_summary(self) -> Dict[str, int]:
        """Get summary of registry contents.
        
        Returns:
            Dictionary with counts of each UID type.
        """
        return {
            'studies': len(self._study_uids),
            'series': len(self._series_uids),
            'instances': len(self._instance_uids),
            'frame_references': len(self._frame_reference_uids),
            'relationships': len(self._relationships)
        }
    
    def clear(self) -> None:
        """Clear all registry data."""
        self._study_uids.clear()
        self._series_uids.clear()
        self._instance_uids.clear()
        self._frame_reference_uids.clear()
        self._study_series.clear()
        self._series_instances.clear()
        self._frame_references.clear()
        self._series_to_study.clear()
        self._instance_to_series.clear()
        self._relationships.clear()