"""
DICOM UID Generation Strategies for Radiotherapy Workflows.

Provides clinical-grade UID generation for RT DICOM creation with both reproducible
and random strategies. Follows PyMedPhys patterns while ensuring full DICOM compliance
for medical physics applications.

## Quick Start

```python
import pyrt_dicom as prt

# For most RT workflows - use random UIDs
generator = prt.RandomUIDGenerator()
study_uid = generator.generate_study_instance_uid()
series_uid = generator.generate_series_instance_uid()
instance_uid = generator.generate_sop_instance_uid()

# For reproducible research workflows - use hash-based UIDs
hash_gen = prt.HashBasedUIDGenerator()
patient_seed = "PatientID_123_StudyDate_20231201"
study_uid = hash_gen.generate_study_instance_uid(patient_seed)
print(f"Study UID: {study_uid}")
# Same seed always produces same UID - useful for research reproducibility
```

## Clinical Workflow Examples

### Complete RT Treatment Planning Workflow
```python
# Create generator for treatment planning study
generator = prt.RandomUIDGenerator()

# Study level - one per patient treatment course
study_uid = generator.generate_study_instance_uid()
frame_ref_uid = generator.generate_frame_of_reference_uid()

# Series level - one per DICOM modality
ct_series_uid = generator.generate_series_instance_uid()
struct_series_uid = generator.generate_series_instance_uid()
dose_series_uid = generator.generate_series_instance_uid()
plan_series_uid = generator.generate_series_instance_uid()

# Instance level - individual DICOM objects
struct_instance_uid = generator.generate_sop_instance_uid()
dose_instance_uid = generator.generate_sop_instance_uid() 
plan_instance_uid = generator.generate_sop_instance_uid()

# CT instances (typically 100-200 slices)
ct_instance_uids = []
for slice_num in range(150):
    ct_instance_uids.append(generator.generate_sop_instance_uid())
```

### Research Workflow with Reproducible UIDs
```python
# For research studies requiring consistent UIDs across runs
hash_gen = prt.HashBasedUIDGenerator()

# Base seed from patient and study identifiers
base_seed = "Research_Study_ABC_Patient_001_Planning_CT"

# Generate consistent UIDs for this patient's data
study_uid = hash_gen.generate_study_instance_uid(base_seed)
ct_series_uid = hash_gen.generate_series_instance_uid(f"{base_seed}_CT_Series")
struct_uid = hash_gen.generate_sop_instance_uid(f"{base_seed}_Structures")

# Re-running this code will always generate the same UIDs
# Useful for reproducible research and data consistency
```

See the UIDRegistry class for managing UID relationships in complex RT workflows.

"""

import hashlib
import time
import uuid
from abc import ABC, abstractmethod
from typing import Optional, Union

from ..utils.exceptions import UIDGenerationError


class UIDGenerator(ABC):
    """Abstract base class for DICOM UID generation strategies.
    
    Provides the foundation for all UID generation in pyrt-dicom, ensuring
    DICOM standard compliance and clinical workflow requirements.
    
    ## Overview
    
    This abstract base class defines the interface for generating DICOM-compliant
    Unique Identifiers (UIDs) used throughout radiotherapy workflows. All UIDs
    generated follow the DICOM standard (Part 5, Chapter 9) with proper format
    validation and length constraints.
    
    ## DICOM UID Requirements
    
    - Maximum length: 64 characters
    - Characters: Only digits (0-9) and dots (.)
    - Format: Hierarchical dot notation (e.g., "*******.5.678")
    - No leading/trailing dots or consecutive dots
    - Must start with registered organization root UID
    
    ## Clinical Context
    
    In radiotherapy DICOM workflows, UIDs establish critical relationships:
    
    - **Study Instance UID**: Links all objects for one patient treatment course
    - **Series Instance UID**: Groups objects by modality (CT, RTSTRUCT, RTDOSE, RTPLAN)
    - **SOP Instance UID**: Uniquely identifies each DICOM object
    - **Frame of Reference UID**: Ensures spatial consistency across RT objects
    
    ## Subclasses
    
    - `HashBasedUIDGenerator`: Reproducible UIDs from seed data
    - `RandomUIDGenerator`: Unique UIDs for each generation
    
    ## Example
    
    ```python
    # Subclasses implement the abstract methods
    generator = RandomUIDGenerator()
    
    # Generate UIDs for RT workflow
    study_uid = generator.generate_study_instance_uid()
    series_uid = generator.generate_series_instance_uid()
    instance_uid = generator.generate_sop_instance_uid()
    frame_ref_uid = generator.generate_frame_of_reference_uid()
    
    print(f"Study: {study_uid}")
    print(f"Series: {series_uid}")
    print(f"Instance: {instance_uid}")
    print(f"Frame Ref: {frame_ref_uid}")
    ```
    
    Note:
        This is an abstract class and cannot be instantiated directly.
        Use one of the concrete subclasses instead.
    """
    
    # DICOM UID root for pyrt-dicom (placeholder - should be registered with HL7)
    # PYRT_DICOM_ROOT = "1.2.826.0.1.3680043.10.711"
    
    # Medical Connections offers free valid UIDs: http://www.medicalconnections.co.uk/FreeUID.html
    # The following root UID is currently being borrowed from PyMedPhys._pinnacle.pinnacle_plan.py
    PYRT_DICOM_ROOT = "1.2.826.0.1.3680043.10.202"
    
    # Maximum length for DICOM UIDs per DICOM standard
    MAX_UID_LENGTH = 64
    
    def __init__(self, root_uid: Optional[str] = None):
        """Initialize UID generator with optional custom root UID.
        
        Args:
            root_uid: Custom root UID. If None, uses default pyrt-dicom root.
            
        Raises:
            UIDGenerationError: If root_uid format is invalid.
        """
        self.root_uid = root_uid if root_uid is not None else self.PYRT_DICOM_ROOT
        self._validate_root_uid()
        
    def _validate_root_uid(self) -> None:
        """Validate root UID format compliance."""
        if not self.root_uid:
            raise UIDGenerationError("Root UID cannot be empty")
            
        # Check basic format: numbers and dots only
        if not all(c.isdigit() or c == '.' for c in self.root_uid):
            raise UIDGenerationError(
                f"Invalid root UID format: {self.root_uid}. "
                "UIDs must contain only digits and dots."
            )
            
        # Check doesn't start or end with dot
        if self.root_uid.startswith('.') or self.root_uid.endswith('.'):
            raise UIDGenerationError(
                f"Root UID cannot start or end with dot: {self.root_uid}"
            )
            
        # Check for consecutive dots
        if '..' in self.root_uid:
            raise UIDGenerationError(
                f"Root UID cannot contain consecutive dots: {self.root_uid}"
            )
    
    @abstractmethod
    def generate_uid(self, seed_data: Optional[Union[str, bytes]] = None) -> str:
        """Generate a DICOM-compliant UID.
        
        Args:
            seed_data: Optional seed data for UID generation.
            
        Returns:
            DICOM-compliant UID string.
            
        Raises:
            UIDGenerationError: If UID generation fails or produces invalid UID.
        """
        pass
    
    def _validate_generated_uid(self, uid: str) -> None:
        """Validate generated UID meets DICOM requirements."""
        if len(uid) > self.MAX_UID_LENGTH:
            raise UIDGenerationError(
                f"Generated UID exceeds maximum length {self.MAX_UID_LENGTH}: "
                f"'{uid}' ({len(uid)} characters)"
            )
            
        # Validate format
        if not uid.startswith(self.root_uid):
            raise UIDGenerationError(
                f"Generated UID does not start with root: {uid}"
            )
            
        if not all(c.isdigit() or c == '.' for c in uid):
            raise UIDGenerationError(
                f"Generated UID contains invalid characters: {uid}"
            )
    
    def generate_study_instance_uid(self, seed_data: Optional[Union[str, bytes]] = None) -> str:
        """Generate a DICOM Study Instance UID.
        
        The Study Instance UID uniquely identifies a complete imaging study,
        typically corresponding to one patient treatment planning session.
        All DICOM objects (CT, RTSTRUCT, RTDOSE, RTPLAN) for the same 
        treatment course should share the same Study Instance UID.
        
        Args:
            seed_data: Optional seed for reproducible generation. Interpretation
                      depends on the concrete generator implementation.
                      
        Returns:
            DICOM-compliant Study Instance UID string.
            
        Example:
            ```python
            generator = RandomUIDGenerator()
            study_uid = generator.generate_study_instance_uid()
            # Use this UID for all RT objects in the treatment plan
            ```
        """
        return self.generate_uid(seed_data)
    
    def generate_series_instance_uid(self, seed_data: Optional[Union[str, bytes]] = None) -> str:
        """Generate a DICOM Series Instance UID.
        
        The Series Instance UID uniquely identifies a series of related 
        DICOM instances, typically grouped by modality and acquisition.
        Each modality (CT, RTSTRUCT, RTDOSE, RTPLAN) should have its own 
        unique Series Instance UID.
        
        Args:
            seed_data: Optional seed for reproducible generation.
                      
        Returns:
            DICOM-compliant Series Instance UID string.
            
        Example:
            ```python
            generator = RandomUIDGenerator() 
            ct_series_uid = generator.generate_series_instance_uid()
            struct_series_uid = generator.generate_series_instance_uid()
            dose_series_uid = generator.generate_series_instance_uid()
            ```
        """
        return self.generate_uid(seed_data)
    
    def generate_sop_instance_uid(self, seed_data: Optional[Union[str, bytes]] = None) -> str:
        """Generate a DICOM SOP Instance UID.
        
        The SOP (Service-Object Pair) Instance UID uniquely identifies a 
        single DICOM object. Every DICOM file must have a unique SOP 
        Instance UID, including each CT slice in a multi-slice series.
        
        Args:
            seed_data: Optional seed for reproducible generation.
                      
        Returns:
            DICOM-compliant SOP Instance UID string.
            
        Example:
            ```python
            generator = RandomUIDGenerator()
            # Generate UID for each DICOM object
            struct_instance_uid = generator.generate_sop_instance_uid()
            dose_instance_uid = generator.generate_sop_instance_uid()
            
            # For multi-slice CT series
            ct_slice_uids = []
            for i in range(150):  # 150 CT slices
                ct_slice_uids.append(generator.generate_sop_instance_uid())
            ```
        """
        return self.generate_uid(seed_data)
    
    def generate_frame_of_reference_uid(self, seed_data: Optional[Union[str, bytes]] = None) -> str:
        """Generate a DICOM Frame of Reference UID.
        
        The Frame of Reference UID establishes a spatial coordinate system
        shared across multiple DICOM objects. Critical for RT workflows where
        CT images, structures, and dose distributions must be spatially aligned.
        All RT objects for the same patient geometry should share the same
        Frame of Reference UID.
        
        Args:
            seed_data: Optional seed for reproducible generation.
                      
        Returns:
            DICOM-compliant Frame of Reference UID string.
            
        Example:
            ```python
            generator = RandomUIDGenerator()
            frame_ref_uid = generator.generate_frame_of_reference_uid()
            
            # Use same Frame of Reference UID for all spatially related objects:
            # - All CT slices in the planning series
            # - RT Structure Set with contours
            # - RT Dose distribution
            # - RT Plan geometry
            ```
        
        Note:
            Proper Frame of Reference UID usage is critical for spatial
            consistency in radiation therapy treatment planning systems.
        """
        return self.generate_uid(seed_data)


class HashBasedUIDGenerator(UIDGenerator):
    """Hash-based UID generation for reproducible UIDs from input data.
    
    Generates consistent, reproducible UIDs by hashing input seed data. Following
    PyMedPhys patterns, this generator ensures the same input always produces the
    same UID, making it ideal for research workflows, data consistency validation,
    and reproducible analysis pipelines.
    
    ## Use Cases
    
    - **Research Studies**: Consistent UIDs across multiple analysis runs
    - **Data Validation**: Verify DICOM object identity using seed data
    - **Reproducible Workflows**: Ensure identical UIDs in repeated processing
    - **Testing**: Generate known UIDs for unit tests and validation
    
    ## Clinical Example: Research Data Consistency
    
    ```python
    # Research workflow requiring consistent UIDs
    hash_gen = HashBasedUIDGenerator()
    
    # Patient and study identifiers as seed
    patient_id = "RESEARCH_001"
    study_date = "20231201"
    modality = "CT_PLANNING"
    
    # Generate consistent study UID
    seed = f"{patient_id}_{study_date}_{modality}"
    study_uid = hash_gen.generate_study_instance_uid(seed)
    
    # Re-running this code will ALWAYS generate the same UID
    # Useful for reproducible research and data integrity checks
    print(f"Study UID: {study_uid}")  # Always the same for this seed
    ```
    
    ## Multi-Run Consistency Example
    
    ```python
    # Day 1: Process patient data
    hash_gen = HashBasedUIDGenerator()
    study_uid_day1 = hash_gen.generate_study_instance_uid("Patient_123_Study_1")
    
    # Day 30: Re-process same patient data 
    hash_gen_new = HashBasedUIDGenerator()
    study_uid_day30 = hash_gen_new.generate_study_instance_uid("Patient_123_Study_1")
    
    assert study_uid_day1 == study_uid_day30  # Always True!
    ```
    
    ## RT Workflow with Consistent UIDs
    
    ```python
    hash_gen = HashBasedUIDGenerator()
    base_seed = "Institution_ABC_Patient_001_TreatmentPlan_1"
    
    # All UIDs derived from consistent base
    study_uid = hash_gen.generate_study_instance_uid(base_seed)
    frame_ref_uid = hash_gen.generate_frame_of_reference_uid(f"{base_seed}_FrameRef")
    
    # Series UIDs for each modality
    ct_series_uid = hash_gen.generate_series_instance_uid(f"{base_seed}_CT")
    struct_series_uid = hash_gen.generate_series_instance_uid(f"{base_seed}_STRUCT")
    dose_series_uid = hash_gen.generate_series_instance_uid(f"{base_seed}_DOSE")
    
    # Instance UIDs with slice information
    ct_slice_uids = []
    for slice_num in range(100):
        slice_seed = f"{base_seed}_CT_Slice_{slice_num:03d}"
        ct_slice_uids.append(hash_gen.generate_sop_instance_uid(slice_seed))
    ```
    
    Warning:
        Never use patient identifiable information directly as seed data.
        Use anonymized or hashed patient identifiers to maintain privacy.
    
    Note:
        Hash-based generation uses SHA-256 for cryptographic security and
        reproducibility across different systems and Python versions.
    """
    
    def generate_uid(self, seed_data: Optional[Union[str, bytes]] = None) -> str:
        """Generate hash-based UID from seed data.
        
        Args:
            seed_data: Data to use for hash-based UID generation. If None,
                      uses current timestamp for uniqueness.
                      
        Returns:
            DICOM-compliant UID based on hash of seed data.
            
        Raises:
            UIDGenerationError: If UID generation fails.
        """
        if seed_data is None:
            # Use timestamp as seed for uniqueness when no data provided
            seed_data = str(time.time())
        
        # Convert to bytes if string
        if isinstance(seed_data, str):
            seed_data = seed_data.encode('utf-8')
        
        # Generate hash
        hash_object = hashlib.sha256(seed_data)
        hash_hex = hash_object.hexdigest()
        
        # Convert hex to decimal string (DICOM UIDs must be numeric)
        hash_int = int(hash_hex, 16)
        hash_decimal = str(hash_int)
        
        # Truncate to fit within UID length constraints
        max_suffix_length = self.MAX_UID_LENGTH - len(self.root_uid) - 1
        if len(hash_decimal) > max_suffix_length:
            hash_decimal = hash_decimal[:max_suffix_length]
        
        # Construct final UID
        uid = f"{self.root_uid}.{hash_decimal}"
        
        self._validate_generated_uid(uid)
        return uid


class RandomUIDGenerator(UIDGenerator):
    """Random UID generation for unique UIDs without reproducibility requirements.
    
    Generates truly unique UIDs using cryptographically secure random components
    combined with high-precision timestamps. Each call produces a different UID
    regardless of input parameters, ensuring uniqueness across all generations.
    This is the recommended generator for most clinical RT workflows.
    
    ## Use Cases
    
    - **Clinical Production**: Standard clinical DICOM creation workflows
    - **Patient Treatment Plans**: Unique identification for each treatment
    - **Multi-Patient Studies**: Ensure no UID conflicts between patients
    - **Concurrent Processing**: Thread-safe unique UID generation
    
    ## Clinical Example: Standard RT Workflow
    
    ```python
    # Standard clinical RT treatment planning workflow
    generator = RandomUIDGenerator()
    
    # Study-level UIDs (one per patient treatment course)
    study_uid = generator.generate_study_instance_uid()
    frame_ref_uid = generator.generate_frame_of_reference_uid()
    
    print(f"Treatment Planning Study: {study_uid}")
    print(f"Spatial Reference Frame: {frame_ref_uid}")
    
    # Series UIDs (one per modality)
    ct_series_uid = generator.generate_series_instance_uid()
    struct_series_uid = generator.generate_series_instance_uid() 
    dose_series_uid = generator.generate_series_instance_uid()
    plan_series_uid = generator.generate_series_instance_uid()
    
    # Instance UIDs (one per DICOM object)
    struct_instance = generator.generate_sop_instance_uid()
    dose_instance = generator.generate_sop_instance_uid()
    plan_instance = generator.generate_sop_instance_uid()
    
    # CT slice instances (typically 100-300 slices)
    ct_instances = []
    for slice_num in range(200):
        ct_instances.append(generator.generate_sop_instance_uid())
    
    print(f"Generated {len(ct_instances)} unique CT slice UIDs")
    ```
    
    ## Multi-Patient Processing Example
    
    ```python
    # Processing multiple patients - each gets unique UIDs
    generator = RandomUIDGenerator()
    
    patient_studies = {}
    for patient_id in ["P001", "P002", "P003"]:
        # Each patient gets completely unique UIDs
        patient_studies[patient_id] = {
            'study_uid': generator.generate_study_instance_uid(),
            'frame_ref_uid': generator.generate_frame_of_reference_uid(),
            'ct_series_uid': generator.generate_series_instance_uid(),
            'struct_series_uid': generator.generate_series_instance_uid()
        }
    
    # Verify all UIDs are unique across all patients
    all_uids = []
    for patient_data in patient_studies.values():
        all_uids.extend(patient_data.values())
    
    assert len(all_uids) == len(set(all_uids))  # All UIDs unique
    ```
    
    ## High-Volume Generation Example
    
    ```python
    # Generate large numbers of unique UIDs (e.g., for batch processing)
    generator = RandomUIDGenerator()
    
    # Generate 10,000 unique UIDs - all guaranteed unique
    large_uid_set = set()
    for i in range(10000):
        uid = generator.generate_sop_instance_uid()
        large_uid_set.add(uid)
    
    print(f"Generated {len(large_uid_set)} unique UIDs")  # Should be 10,000
    assert len(large_uid_set) == 10000  # No collisions!
    ```
    
    ## Thread-Safe Usage
    
    ```python
    import concurrent.futures
    import threading
    
    # RandomUIDGenerator is thread-safe
    generator = RandomUIDGenerator()
    generated_uids = set()
    uid_lock = threading.Lock()
    
    def generate_uids_worker(worker_id, count):
        local_uids = []
        for i in range(count):
            uid = generator.generate_sop_instance_uid()
            local_uids.append(uid)
        
        with uid_lock:
            generated_uids.update(local_uids)
    
    # Run 10 workers, each generating 1000 UIDs
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(generate_uids_worker, i, 1000) for i in range(10)]
        concurrent.futures.wait(futures)
    
    print(f"Total unique UIDs generated: {len(generated_uids)}")
    # Should be 10,000 - no collisions even with concurrent generation
    ```
    
    Note:
        Uses UUID4 (random) + microsecond timestamps + SHA-256 hashing
        for cryptographically secure uniqueness guarantees.
    
    Note:
        The seed_data parameter is ignored in random generation but kept
        for API consistency with HashBasedUIDGenerator.
    """
    
    def generate_uid(self, seed_data: Optional[Union[str, bytes]] = None) -> str:
        """Generate random UID.
        
        Args:
            seed_data: Ignored for random generation, but kept for API consistency.
                      
        Returns:
            DICOM-compliant random UID.
            
        Raises:
            UIDGenerationError: If UID generation fails.
        """
        # Use UUID4 for randomness combined with timestamp
        random_uuid = uuid.uuid4()
        timestamp = int(time.time() * 1000000)  # microsecond precision
        
        # Combine UUID and timestamp for extra uniqueness
        combined_data = f"{random_uuid.hex}{timestamp}"
        
        # Convert to integer to ensure numeric UID
        combined_int = int(hashlib.sha256(combined_data.encode()).hexdigest(), 16)
        uid_suffix = str(combined_int)
        
        # Truncate to fit within UID length constraints
        max_suffix_length = self.MAX_UID_LENGTH - len(self.root_uid) - 1
        if len(uid_suffix) > max_suffix_length:
            uid_suffix = uid_suffix[:max_suffix_length]
        
        # Construct final UID
        uid = f"{self.root_uid}.{uid_suffix}"
        
        self._validate_generated_uid(uid)
        return uid


class DefaultUIDGenerator:
    """Factory class for creating UID generator instances.
    
    Provides convenient factory methods for creating the most commonly used
    UID generator configurations in pyrt-dicom workflows. Simplifies generator
    selection for medical physicists and researchers.
    
    ## Quick Generator Selection Guide
    
    - **Clinical Production**: Use `create_default_generator()` (Random)
    - **Research/Reproducible**: Use `create_hash_generator()`
    - **Custom Root UID**: Use specific methods with root_uid parameter
    
    ## Examples
    
    ### Standard Clinical Workflow
    ```python
    # Most common usage - random UIDs for clinical work
    generator = DefaultUIDGenerator.create_default_generator()
    study_uid = generator.generate_study_instance_uid()
    ```
    
    ### Research Workflow  
    ```python
    # Reproducible UIDs for research
    hash_gen = DefaultUIDGenerator.create_hash_generator()
    consistent_uid = hash_gen.generate_study_instance_uid("research_seed")
    ```
    
    ### Custom Organization Root UID
    ```python
    # Using your institution's registered UID root
    institution_root = "1.2.840.113619.2.55.3.604688119"  # Example
    custom_gen = DefaultUIDGenerator.create_random_generator(institution_root)
    study_uid = custom_gen.generate_study_instance_uid()
    ```
    
    ### Batch Processing Multiple Patients
    ```python
    # Create generator once, use for multiple patients
    clinical_gen = DefaultUIDGenerator.create_default_generator()
    
    patients = ["P001", "P002", "P003"]
    for patient_id in patients:
        study_uid = clinical_gen.generate_study_instance_uid()
        print(f"Patient {patient_id}: {study_uid}")
    ```
    
    Note:
        All factory methods return properly initialized generator instances
        ready for immediate use in clinical and research workflows.
    """
    
    @staticmethod
    def create_hash_generator(root_uid: Optional[str] = None) -> HashBasedUIDGenerator:
        """Create a hash-based UID generator for reproducible workflows.
        
        Creates a HashBasedUIDGenerator instance for use cases requiring
        consistent, reproducible UIDs from seed data. Ideal for research
        workflows, validation testing, and data consistency verification.
        
        Args:
            root_uid: Optional custom organization root UID. If None, uses
                     the default pyrt-dicom root UID.
                     
        Returns:
            Configured HashBasedUIDGenerator instance.
            
        Example:
            ```python
            # Create hash generator for research reproducibility
            hash_gen = DefaultUIDGenerator.create_hash_generator()
            
            # Generate consistent UIDs from patient data
            patient_seed = "Patient_001_Study_Planning_CT_20231201"
            study_uid = hash_gen.generate_study_instance_uid(patient_seed)
            
            # Same seed always produces same UID
            assert study_uid == hash_gen.generate_study_instance_uid(patient_seed)
            ```
        """
        return HashBasedUIDGenerator(root_uid)
    
    @staticmethod
    def create_random_generator(root_uid: Optional[str] = None) -> RandomUIDGenerator:
        """Create a random UID generator for unique UID workflows.
        
        Creates a RandomUIDGenerator instance for use cases requiring
        guaranteed unique UIDs. Recommended for most clinical production
        workflows where reproducibility is not required.
        
        Args:
            root_uid: Optional custom organization root UID. If None, uses
                     the default pyrt-dicom root UID.
                     
        Returns:
            Configured RandomUIDGenerator instance.
            
        Example:
            ```python
            # Create random generator for clinical production
            random_gen = DefaultUIDGenerator.create_random_generator()
            
            # Generate unique UIDs for treatment planning
            study_uid = random_gen.generate_study_instance_uid()
            series_uid = random_gen.generate_series_instance_uid()
            
            # Each call produces a different UID
            assert study_uid != random_gen.generate_study_instance_uid()
            ```
        """
        return RandomUIDGenerator(root_uid)
    
    @staticmethod
    def create_default_generator() -> RandomUIDGenerator:
        """Create the default UID generator for general pyrt-dicom usage.
        
        Creates a RandomUIDGenerator with default settings, suitable for
        most clinical RT DICOM creation workflows. This is the recommended
        generator for standard medical physics applications.
        
        Returns:
            RandomUIDGenerator instance with default configuration.
            
        Example:
            ```python
            # Most common usage pattern in pyrt-dicom
            generator = DefaultUIDGenerator.create_default_generator()
            
            # Create UIDs for complete RT workflow
            study_uid = generator.generate_study_instance_uid()
            frame_ref_uid = generator.generate_frame_of_reference_uid()
            
            # Series UIDs for each RT modality
            ct_series = generator.generate_series_instance_uid()
            struct_series = generator.generate_series_instance_uid()
            dose_series = generator.generate_series_instance_uid()
            plan_series = generator.generate_series_instance_uid()
            
            print(f"Generated UIDs for RT treatment planning study: {study_uid}")
            ```
            
        Note:
            This method is equivalent to `RandomUIDGenerator()` but provides
            clearer intent and future-proofing if default behavior changes.
        """
        return RandomUIDGenerator()