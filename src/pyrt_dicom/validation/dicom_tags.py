"""
DICOM tag value validation for RT objects.

This module provides comprehensive validation for DICOM tag values against
Value Representation (VR) constraints and clinical parameter ranges.

## Clinical Context

DICOM tag validation ensures:
- DICOM standard compliance (PS 3.5 VR definitions)
- Multi-vendor TPS compatibility
- Clinical parameter safety and reasonableness
- Data integrity across RT workflow

## Usage

```python
from pyrt_dicom.validation.dicom_tags import DicomTagValidator, validate_dicom_tag_value

# Validate individual tag values
validator = DicomTagValidator()
errors = validator.validate_tag_value('LO', 'PatientID', 'RT001')

# Validate against VR constraints
from pyrt_dicom.validation.dicom_tags import validate_vr_constraints

vr_errors = validate_vr_constraints('DS', '1.5', 'SliceThickness')
```
"""

import re
from typing import Dict, List, Optional, Union, Any

from ..utils.exceptions import ValidationError


# DICOM VR (Value Representation) constraints
DICOM_VR_CONSTRAINTS = {
    "AE": {  # Application Entity
        "max_length": 16,
        "character_set": r"^[A-Za-z0-9\s]*$",
        "description": "Application Entity title",
    },
    "AS": {  # Age String
        "max_length": 4,
        "format": r"^\d{3}[DWMY]$",
        "description": "Age string (nnnD/W/M/Y)",
    },
    "CS": {  # Code String
        "max_length": 16,
        "character_set": r"^[A-Z0-9_\s]*$",
        "description": "Code string (uppercase alphanumeric)",
    },
    "DA": {  # Date
        "max_length": 8,
        "format": r"^\d{8}$",
        "description": "Date (YYYYMMDD)",
    },
    "DS": {  # Decimal String
        "max_length": 16,
        "format": r"^[+-]?(\d+\.?\d*|\.\d+)([eE][+-]?\d+)?$",
        "description": "Decimal string (floating point number)",
    },
    "DT": {  # Date Time
        "max_length": 26,
        "format": r"^\d{4}(\d{2}(\d{2}(\d{2}(\d{2}(\d{2}(\.\d{1,6})?)?)?)?)?)?([+-]\d{4})?$",
        "description": "Date time (YYYYMMDDHHMMSS.FFFFFF&ZZXX)",
    },
    "IS": {  # Integer String
        "max_length": 12,
        "format": r"^[+-]?\d+$",
        "description": "Integer string",
    },
    "LO": {  # Long String
        "max_length": 64,
        "character_set": r"^[^\x00-\x1F\x7F]*$",  # No control characters
        "description": "Long string",
    },
    "LT": {  # Long Text
        "max_length": 10240,
        "character_set": r"^[^\x00-\x08\x0B\x0C\x0E-\x1F\x7F]*$",  # Allow \t, \n, \r
        "description": "Long text",
    },
    "PN": {  # Person Name
        "max_length": 64,
        "character_set": r"^[A-Za-z0-9\s\.\-_\^=\']*$",  # Allow ^ and = for PN components, and apostrophes
        "description": "Person name",
    },
    "SH": {  # Short String
        "max_length": 16,
        "character_set": r"^[^\x00-\x1F\x7F]*$",  # No control characters
        "description": "Short string",
    },
    "ST": {  # Short Text
        "max_length": 1024,
        "character_set": r"^[^\x00-\x08\x0B\x0C\x0E-\x1F\x7F]*$",  # Allow \t, \n, \r
        "description": "Short text",
    },
    "TM": {  # Time
        "max_length": 16,
        "format": r"^\d{2}(\d{2}(\d{2}(\.\d{1,6})?)?)?$",
        "description": "Time (HHMMSS.FFFFFF)",
    },
    "UI": {  # Unique Identifier
        "max_length": 64,
        "format": r"^[0-9]+(\.[0-9]+)*$",  # More strict: numbers separated by periods, no leading/trailing periods
        "description": "Unique identifier (UID)",
    },
    "US": {  # Unsigned Short
        "max_value": 65535,
        "min_value": 0,
        "description": "Unsigned short integer (0-65535)",
    },
    "UL": {  # Unsigned Long
        "max_value": 4294967295,
        "min_value": 0,
        "description": "Unsigned long integer (0-4294967295)",
    },
}


# Clinical parameter ranges for RT-specific DICOM tags
RT_CLINICAL_RANGES = {
    "SliceThickness": {
        "vr": "DS",
        "min_value": 0.1,  # mm
        "max_value": 50.0,  # mm
        "units": "mm",
        "description": "CT slice thickness",
    },
    "PixelSpacing": {
        "vr": "DS",
        "min_value": 0.01,  # mm
        "max_value": 10.0,  # mm
        "units": "mm",
        "description": "Pixel spacing in mm",
    },
    "DoseGridScaling": {
        "vr": "DS",
        "min_value": 1e-10,
        "max_value": 1000.0,
        "units": "Gy",
        "description": "Dose grid scaling factor",
    },
    "NominalBeamEnergy": {
        "vr": "DS",
        "min_value": 1.0,  # MV
        "max_value": 25.0,  # MV
        "units": "MV",
        "description": "Nominal beam energy",
    },
    "GantryAngle": {
        "vr": "DS",
        "min_value": 0.0,
        "max_value": 359.9,
        "units": "degrees",
        "description": "Gantry angle",
    },
    "CollimatorAngle": {
        "vr": "DS",
        "min_value": 0.0,
        "max_value": 359.9,
        "units": "degrees",
        "description": "Collimator angle",
    },
    "CouchAngle": {
        "vr": "DS",
        "min_value": 0.0,
        "max_value": 359.9,
        "units": "degrees",
        "description": "Patient support angle",
    },
}


class DicomTagValidator:
    """
    Comprehensive DICOM tag value validator.

    Validates DICOM tag values against VR constraints and clinical
    parameter ranges for RT-specific tags.

    Clinical Notes
    --------------
    - VR constraints ensure DICOM standard compliance
    - Clinical ranges prevent unsafe parameter values
    - RT-specific validation covers dose, geometry, and beam parameters
    - Multi-vendor compatibility requires strict VR adherence
    """

    def __init__(self, strict_mode: bool = True) -> None:
        """
        Initialize DICOM tag validator.

        Parameters
        ----------
        strict_mode : bool, default True
            If True, enforce strict DICOM VR compliance
            If False, allow some flexibility for clinical workflows
        """
        self.strict_mode = strict_mode
        self.vr_constraints = DICOM_VR_CONSTRAINTS.copy()
        self.clinical_ranges = RT_CLINICAL_RANGES.copy()

    def validate_tag_value(
        self, vr: str, tag_name: str, value: Any, clinical_validation: bool = True
    ) -> List[str]:
        """
        Validate DICOM tag value against VR constraints and clinical ranges.

        Parameters
        ----------
        vr : str
            DICOM Value Representation (e.g., 'DS', 'LO', 'CS')
        tag_name : str
            Name of the DICOM tag for error reporting
        value : Any
            Value to validate
        clinical_validation : bool, default True
            Enable clinical range validation for RT parameters

        Returns
        -------
        List[str]
            List of validation errors (empty if valid)

        Examples
        --------
        >>> validator = DicomTagValidator()
        >>> errors = validator.validate_tag_value('DS', 'SliceThickness', '2.5')
        >>> len(errors)
        0
        """
        errors = []

        # Convert to string for validation
        str_value = str(value).strip() if value is not None else ""

        # Skip validation for empty values (handled by required field validation)
        if not str_value:
            return errors

        # VR constraint validation
        vr_errors = self._validate_vr_constraints(vr, tag_name, str_value)
        errors.extend(vr_errors)

        # Clinical range validation for RT parameters
        if clinical_validation and tag_name in self.clinical_ranges:
            clinical_errors = self._validate_clinical_range(tag_name, str_value)
            errors.extend(clinical_errors)

        return errors

    def _validate_vr_constraints(self, vr: str, tag_name: str, value: str) -> List[str]:
        """Validate value against DICOM VR constraints."""
        errors = []

        if vr not in self.vr_constraints:
            if self.strict_mode:
                errors.append(f"Unknown VR '{vr}' for tag {tag_name}")
            return errors

        constraints = self.vr_constraints[vr]

        # Length validation
        if "max_length" in constraints:
            if len(value) > constraints["max_length"]:
                errors.append(
                    f"{tag_name} exceeds maximum length for VR {vr}: "
                    f"{len(value)} > {constraints['max_length']} characters"
                )

        # Format validation (regex)
        if "format" in constraints:
            if not re.match(constraints["format"], value):
                errors.append(
                    f"{tag_name} format invalid for VR {vr}. "
                    f"Expected: {constraints['description']}. Got: '{value}'"
                )

        # Character set validation
        if "character_set" in constraints:
            if not re.match(constraints["character_set"], value):
                errors.append(
                    f"{tag_name} contains invalid characters for VR {vr}. "
                    f"Expected: {constraints['description']}. Got: '{value}'"
                )

        # Numeric range validation for US/UL
        if vr in ["US", "UL"]:
            try:
                numeric_value = int(value)
                if (
                    "min_value" in constraints
                    and numeric_value < constraints["min_value"]
                ):
                    errors.append(
                        f"{tag_name} value {numeric_value} below minimum for VR {vr}: "
                        f"{constraints['min_value']}"
                    )
                if (
                    "max_value" in constraints
                    and numeric_value > constraints["max_value"]
                ):
                    errors.append(
                        f"{tag_name} value {numeric_value} above maximum for VR {vr}: "
                        f"{constraints['max_value']}"
                    )
            except ValueError:
                errors.append(f"{tag_name} must be valid integer for VR {vr}")

        return errors

    def _validate_clinical_range(self, tag_name: str, value: str) -> List[str]:
        """Validate value against clinical parameter ranges."""
        errors = []

        if tag_name not in self.clinical_ranges:
            return errors

        range_info = self.clinical_ranges[tag_name]

        try:
            # Convert to float for numeric validation
            if range_info["vr"] in ["DS", "IS"]:
                numeric_value = float(value)

                if (
                    "min_value" in range_info
                    and numeric_value < range_info["min_value"]
                ):
                    errors.append(
                        f"{tag_name} value {numeric_value}{range_info.get('units', '')} "
                        f"below clinical minimum: {range_info['min_value']}{range_info.get('units', '')}. "
                        f"{range_info['description']}"
                    )

                if (
                    "max_value" in range_info
                    and numeric_value > range_info["max_value"]
                ):
                    errors.append(
                        f"{tag_name} value {numeric_value}{range_info.get('units', '')} "
                        f"above clinical maximum: {range_info['max_value']}{range_info.get('units', '')}. "
                        f"{range_info['description']}"
                    )

        except ValueError:
            errors.append(
                f"{tag_name} must be valid number for clinical validation. "
                f"Got: '{value}'"
            )

        return errors


# Convenience functions for common validation tasks


def validate_dicom_tag_value(
    vr: str,
    tag_name: str,
    value: Any,
    clinical_validation: bool = True,
    strict_mode: bool = True,
) -> List[str]:
    """
    Validate DICOM tag value against VR constraints.

    Parameters
    ----------
    vr : str
        DICOM Value Representation
    tag_name : str
        Name of the DICOM tag
    value : Any
        Value to validate
    clinical_validation : bool, default True
        Enable clinical range validation
    strict_mode : bool, default True
        Enable strict DICOM compliance

    Returns
    -------
    List[str]
        List of validation errors
    """
    validator = DicomTagValidator(strict_mode=strict_mode)
    return validator.validate_tag_value(vr, tag_name, value, clinical_validation)


def validate_vr_constraints(vr: str, value: Any, tag_name: str = "Tag") -> List[str]:
    """
    Validate value against DICOM VR constraints only.

    Parameters
    ----------
    vr : str
        DICOM Value Representation
    value : Any
        Value to validate
    tag_name : str, default 'Tag'
        Name for error reporting

    Returns
    -------
    List[str]
        List of validation errors
    """
    validator = DicomTagValidator()
    return validator._validate_vr_constraints(vr, tag_name, str(value))


def get_clinical_range(tag_name: str) -> Optional[Dict[str, Any]]:
    """
    Get clinical range information for RT parameter.

    Parameters
    ----------
    tag_name : str
        Name of the RT parameter

    Returns
    -------
    Optional[Dict[str, Any]]
        Clinical range information or None if not found
    """
    return RT_CLINICAL_RANGES.get(tag_name)
