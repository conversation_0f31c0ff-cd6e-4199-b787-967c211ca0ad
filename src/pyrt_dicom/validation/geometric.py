# Copyright (C) 2024 Pirate DICOM Contributors

"""
Geometric validation utilities for RT DICOM coordinate systems.

Provides validation for coordinate system consistency, geometric bounds checking,
and clinical safety validation for RT DICOM objects.
"""

from typing import Dict, List, Optional
import numpy as np
from numpy.typing import NDArray

from ..utils.exceptions import CoordinateSystemError
from ..coordinates.transforms import CoordinateTransformer
from ..coordinates.reference_frames import GeometricParameters, FrameOfReference


# Clinical geometric limits for validation
CLINICAL_GEOMETRIC_LIMITS = {
    "max_patient_extent": 600.0,      # mm, maximum patient dimension
    "min_patient_extent": 10.0,       # mm, minimum meaningful dimension
    "max_pixel_spacing": 10.0,        # mm, coarsest reasonable resolution
    "min_pixel_spacing": 0.01,        # mm, finest reasonable resolution
    "max_slice_thickness": 50.0,      # mm, thickest reasonable slice
    "min_slice_thickness": 0.1,       # mm, thinnest reasonable slice
    "max_structure_volume": 10000,    # cc, largest reasonable structure
    "min_structure_volume": 0.001,    # cc, smallest meaningful structure
    "max_coordinate_value": 2000.0,   # mm, maximum coordinate in any direction
    "contour_point_tolerance": 0.01,  # mm, contour closure tolerance
    "geometric_tolerance": 1e-6,      # general floating point tolerance
}


class GeometricValidator:
    """
    Comprehensive geometric validation for RT DICOM objects.
    
    Provides validation methods for coordinate system consistency,
    clinical geometric constraints, and DICOM standard compliance.
    """
    
    def __init__(
        self, 
        clinical_limits: Optional[Dict[str, float]] = None,
        tolerance: float = 1e-6,
    ) -> None:
        """
        Initialize geometric validator.
        
        Parameters
        ----------
        clinical_limits : Dict[str, float], optional
            Custom clinical limits (overrides defaults)
        tolerance : float
            General tolerance for floating point comparisons
        """
        self.limits = CLINICAL_GEOMETRIC_LIMITS.copy()
        if clinical_limits:
            self.limits.update(clinical_limits)
        self.tolerance = tolerance
    
    def validate_geometric_parameters(
        self, params: GeometricParameters
    ) -> List[str]:
        """
        Validate geometric parameters for clinical reasonableness.
        
        Parameters
        ----------
        params : GeometricParameters
            Geometric parameters to validate
            
        Returns
        -------
        List[str]
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Validate pixel spacing
        for i, spacing in enumerate(params.pixel_spacing):
            if spacing < self.limits["min_pixel_spacing"]:
                errors.append(
                    f"Pixel spacing[{i}] too small: {spacing}mm < "
                    f"{self.limits['min_pixel_spacing']}mm"
                )
            elif spacing > self.limits["max_pixel_spacing"]:
                errors.append(
                    f"Pixel spacing[{i}] too large: {spacing}mm > "
                    f"{self.limits['max_pixel_spacing']}mm"
                )
        
        # Validate slice thickness
        if params.slice_thickness < self.limits["min_slice_thickness"]:
            errors.append(
                f"Slice thickness too small: {params.slice_thickness}mm < "
                f"{self.limits['min_slice_thickness']}mm"
            )
        elif params.slice_thickness > self.limits["max_slice_thickness"]:
            errors.append(
                f"Slice thickness too large: {params.slice_thickness}mm > "
                f"{self.limits['max_slice_thickness']}mm"
            )
        
        # Validate image position coordinates
        for i, coord in enumerate(params.image_position):
            if abs(coord) > self.limits["max_coordinate_value"]:
                errors.append(
                    f"Image position coordinate[{i}] too large: {coord}mm > "
                    f"±{self.limits['max_coordinate_value']}mm"
                )
        
        # Validate image orientation vector normalization (done in transforms.py)
        # Just check that it exists and has correct length
        if len(params.image_orientation) != 6:
            errors.append(
                f"Image orientation must have 6 elements, got {len(params.image_orientation)}"
            )
        
        return errors
    
    def validate_coordinate_consistency(
        self,
        transformer1: CoordinateTransformer,
        transformer2: CoordinateTransformer,
        test_points: Optional[NDArray[np.float64]] = None,
    ) -> List[str]:
        """
        Validate consistency between two coordinate transformers.
        
        Parameters
        ----------
        transformer1, transformer2 : CoordinateTransformer
            Coordinate transformers to compare
        test_points : NDArray, optional
            Test points for validation (if None, uses standard test points)
            
        Returns
        -------
        List[str]
            List of validation errors (empty if consistent)
        """
        errors = []
        
        # Check patient positions match
        if transformer1.patient_position != transformer2.patient_position:
            errors.append(
                f"Patient position mismatch: '{transformer1.patient_position}' vs "
                f"'{transformer2.patient_position}'"
            )
        
        # Check image orientations match
        orient1 = np.array(transformer1.image_orientation)
        orient2 = np.array(transformer2.image_orientation)
        if not np.allclose(orient1, orient2, atol=self.tolerance):
            errors.append(
                f"Image orientation mismatch: {transformer1.image_orientation} vs "
                f"{transformer2.image_orientation}"
            )
        
        # Use default test points if none provided
        if test_points is None:
            test_points = np.array([
                [0, 0, 0],      # Origin
                [100, 100, 50], # Typical body dimensions
                [256, 256, 100] # Common image dimensions
            ], dtype=np.float64)
        
        # Test coordinate transformations for consistency
        try:
            # Transform test points using both transformers
            patient_coords1 = transformer1.dicom_to_patient(test_points)
            patient_coords2 = transformer2.dicom_to_patient(test_points)
            
            # Check if transformations are consistent
            if not np.allclose(patient_coords1, patient_coords2, atol=1.0):  # 1mm tolerance
                max_diff = np.max(np.abs(patient_coords1 - patient_coords2))
                errors.append(
                    f"Coordinate transformation inconsistency: maximum difference "
                    f"{max_diff:.3f}mm > 1.0mm tolerance"
                )
        
        except Exception as e:
            errors.append(f"Coordinate transformation error: {str(e)}")
        
        return errors
    
    def validate_structure_bounds(
        self,
        coordinates: NDArray[np.float64],
        structure_name: str,
    ) -> List[str]:
        """
        Validate structure coordinates for clinical reasonableness.
        
        Parameters
        ----------
        coordinates : NDArray
            Structure coordinates (N x 3 array)
        structure_name : str
            Name of structure for error reporting
            
        Returns
        -------
        List[str]
            List of validation errors
        """
        errors = []
        
        if coordinates.size == 0:
            errors.append(f"Structure '{structure_name}' has no coordinates")
            return errors
        
        if coordinates.ndim != 2 or coordinates.shape[1] != 3:
            errors.append(
                f"Structure '{structure_name}' coordinates must be N x 3 array, "
                f"got shape {coordinates.shape}"
            )
            return errors
        
        # Check coordinate bounds
        mins = np.min(coordinates, axis=0)
        maxs = np.max(coordinates, axis=0)
        extents = maxs - mins
        
        for i, (min_coord, max_coord, extent) in enumerate(zip(mins, maxs, extents)):
            axis_name = ["x", "y", "z"][i]
            
            # Check individual coordinate bounds
            if abs(min_coord) > self.limits["max_coordinate_value"]:
                errors.append(
                    f"Structure '{structure_name}' {axis_name}-coordinate minimum "
                    f"{min_coord:.1f}mm exceeds limit ±{self.limits['max_coordinate_value']}mm"
                )
            
            if abs(max_coord) > self.limits["max_coordinate_value"]:
                errors.append(
                    f"Structure '{structure_name}' {axis_name}-coordinate maximum "
                    f"{max_coord:.1f}mm exceeds limit ±{self.limits['max_coordinate_value']}mm"
                )
            
            # Check extent reasonableness
            if extent > self.limits["max_patient_extent"]:
                errors.append(
                    f"Structure '{structure_name}' {axis_name}-extent {extent:.1f}mm > "
                    f"maximum patient extent {self.limits['max_patient_extent']}mm"
                )
            
            if extent < self.limits["min_patient_extent"]:
                errors.append(
                    f"Structure '{structure_name}' {axis_name}-extent {extent:.1f}mm < "
                    f"minimum meaningful extent {self.limits['min_patient_extent']}mm"
                )
        
        # Estimate volume for reasonableness check
        volume_estimate = np.prod(extents) / 1000  # Convert mm³ to cc
        
        if volume_estimate > self.limits["max_structure_volume"]:
            errors.append(
                f"Structure '{structure_name}' estimated volume {volume_estimate:.1f}cc > "
                f"maximum reasonable volume {self.limits['max_structure_volume']}cc"
            )
        
        if volume_estimate < self.limits["min_structure_volume"]:
            errors.append(
                f"Structure '{structure_name}' estimated volume {volume_estimate:.3f}cc < "
                f"minimum meaningful volume {self.limits['min_structure_volume']}cc"
            )
        
        return errors


# Convenience functions for common validation tasks

def validate_coordinate_bounds(
    coordinates: NDArray[np.float64],
    limits: Optional[Dict[str, float]] = None,
) -> List[str]:
    """
    Validate coordinate bounds against clinical limits.
    
    Parameters
    ----------
    coordinates : NDArray
        Coordinates to validate (N x 3 array)
    limits : Dict[str, float], optional
        Custom limits (uses defaults if None)
        
    Returns
    -------
    List[str]
        List of validation errors
    """
    validator = GeometricValidator(clinical_limits=limits)
    return validator.validate_structure_bounds(coordinates, "coordinates")


def validate_geometric_consistency(
    frame_of_reference: FrameOfReference,
    new_params: GeometricParameters,
    frame_uid: str,
) -> List[str]:
    """
    Validate geometric consistency with existing frame of reference.
    
    Parameters
    ----------
    frame_of_reference : FrameOfReference
        Frame of reference manager
    new_params : GeometricParameters
        New parameters to validate
    frame_uid : str
        Frame of Reference UID to validate against
        
    Returns
    -------
    List[str]
        List of validation errors
    """
    errors = []
    
    try:
        # This will raise CoordinateSystemError if inconsistent
        frame_of_reference.validate_geometric_consistency(frame_uid, new_params)
    except CoordinateSystemError as e:
        errors.append(str(e))
    
    # Additional geometric parameter validation
    validator = GeometricValidator()
    param_errors = validator.validate_geometric_parameters(new_params)
    errors.extend(param_errors)
    
    return errors


def validate_structure_geometry(
    mask: NDArray[np.uint8],
    structure_name: str,
    geometric_params: GeometricParameters,
) -> List[str]:
    """
    Validate structure geometry from binary mask.

    Parameters
    ----------
    mask : NDArray
        Binary mask (3D array)
    structure_name : str
        Name of structure
    geometric_params : GeometricParameters
        Geometric parameters for coordinate conversion

    Returns
    -------
    List[str]
        List of validation errors
    """
    errors = []

    if mask.size == 0:
        errors.append(f"Structure '{structure_name}' mask is empty")
        return errors

    if mask.ndim != 3:
        errors.append(
            f"Structure '{structure_name}' mask must be 3D, got {mask.ndim}D"
        )
        return errors

    # Check mask content
    if not np.any(mask):
        errors.append(f"Structure '{structure_name}' mask contains no positive voxels")
        return errors

    if not np.all((mask == 0) | (mask == 1)):
        errors.append(f"Structure '{structure_name}' mask must be binary (0 or 1)")

    # Calculate structure volume
    voxel_volume = (
        geometric_params.pixel_spacing[0] *
        geometric_params.pixel_spacing[1] *
        geometric_params.slice_thickness
    ) / 1000  # Convert mm³ to cc

    structure_volume = np.sum(mask) * voxel_volume

    limits = CLINICAL_GEOMETRIC_LIMITS
    if structure_volume > limits["max_structure_volume"]:
        errors.append(
            f"Structure '{structure_name}' volume {structure_volume:.1f}cc > "
            f"maximum {limits['max_structure_volume']}cc"
        )

    if structure_volume < limits["min_structure_volume"]:
        errors.append(
            f"Structure '{structure_name}' volume {structure_volume:.3f}cc < "
            f"minimum {limits['min_structure_volume']}cc"
        )

    return errors


def validate_coordinate_transformation_inputs(
    source_coords: NDArray[np.float64],
    transformation_matrix: Optional[NDArray[np.float64]] = None,
    target_frame: Optional[str] = None,
) -> List[str]:
    """
    Validate coordinate transformation input parameters.

    Parameters
    ----------
    source_coords : NDArray
        Source coordinates to transform (N x 3 array)
    transformation_matrix : NDArray, optional
        4x4 transformation matrix
    target_frame : str, optional
        Target coordinate frame identifier

    Returns
    -------
    List[str]
        List of validation errors
    """
    errors = []
    limits = CLINICAL_GEOMETRIC_LIMITS

    # Validate source coordinates
    if source_coords.size == 0:
        errors.append("Source coordinates array is empty")
        return errors

    if source_coords.ndim != 2 or source_coords.shape[1] != 3:
        errors.append(
            f"Source coordinates must be N x 3 array, got shape {source_coords.shape}"
        )
        return errors

    # Check coordinate bounds
    max_coord = np.max(np.abs(source_coords))
    if max_coord > limits["max_coordinate_value"]:
        errors.append(
            f"Source coordinates exceed maximum range: {max_coord:.1f}mm > "
            f"±{limits['max_coordinate_value']}mm"
        )

    # Validate transformation matrix if provided
    if transformation_matrix is not None:
        if transformation_matrix.shape != (4, 4):
            errors.append(
                f"Transformation matrix must be 4x4, got shape {transformation_matrix.shape}"
            )
        else:
            # Check for valid transformation matrix properties
            det = np.linalg.det(transformation_matrix[:3, :3])
            if abs(det) < 1e-10:
                errors.append("Transformation matrix is singular (determinant near zero)")

            # Check for reasonable scaling (determinant should be close to ±1 for rigid transforms)
            if abs(abs(det) - 1.0) > 0.1:
                errors.append(
                    f"Transformation matrix has unusual scaling (determinant = {det:.3f}). "
                    "Expected near ±1.0 for rigid transformations"
                )

    return errors


def validate_image_orientation_and_position(
    image_orientation: List[float],
    image_position: List[float],
    pixel_spacing: List[float],
    slice_thickness: float,
) -> List[str]:
    """
    Validate image orientation and position parameters for clinical reasonableness.

    Parameters
    ----------
    image_orientation : List[float]
        6-element image orientation patient vector
    image_position : List[float]
        3-element image position patient vector
    pixel_spacing : List[float]
        2-element pixel spacing in mm
    slice_thickness : float
        Slice thickness in mm

    Returns
    -------
    List[str]
        List of validation errors
    """
    errors = []
    limits = CLINICAL_GEOMETRIC_LIMITS

    # Validate image orientation
    if len(image_orientation) != 6:
        errors.append(
            f"Image orientation must have 6 elements, got {len(image_orientation)}"
        )
        return errors

    # Check that orientation vectors are unit vectors
    row_vector = np.array(image_orientation[:3])
    col_vector = np.array(image_orientation[3:])

    row_magnitude = np.linalg.norm(row_vector)
    col_magnitude = np.linalg.norm(col_vector)

    if abs(row_magnitude - 1.0) > limits["geometric_tolerance"]:
        errors.append(
            f"Image orientation row vector is not unit length: {row_magnitude:.6f}"
        )

    if abs(col_magnitude - 1.0) > limits["geometric_tolerance"]:
        errors.append(
            f"Image orientation column vector is not unit length: {col_magnitude:.6f}"
        )

    # Check that vectors are orthogonal
    dot_product = np.dot(row_vector, col_vector)
    if abs(dot_product) > limits["geometric_tolerance"]:
        errors.append(
            f"Image orientation vectors are not orthogonal: dot product = {dot_product:.6f}"
        )

    # Validate image position
    if len(image_position) != 3:
        errors.append(
            f"Image position must have 3 elements, got {len(image_position)}"
        )
    else:
        for i, coord in enumerate(image_position):
            if abs(coord) > limits["max_coordinate_value"]:
                errors.append(
                    f"Image position coordinate[{i}] too large: {coord}mm > "
                    f"±{limits['max_coordinate_value']}mm"
                )

    # Validate pixel spacing
    if len(pixel_spacing) != 2:
        errors.append(
            f"Pixel spacing must have 2 elements, got {len(pixel_spacing)}"
        )
    else:
        for i, spacing in enumerate(pixel_spacing):
            if spacing < limits["min_pixel_spacing"]:
                errors.append(
                    f"Pixel spacing[{i}] too small: {spacing}mm < "
                    f"{limits['min_pixel_spacing']}mm"
                )
            elif spacing > limits["max_pixel_spacing"]:
                errors.append(
                    f"Pixel spacing[{i}] too large: {spacing}mm > "
                    f"{limits['max_pixel_spacing']}mm"
                )

    # Validate slice thickness
    if slice_thickness < limits["min_slice_thickness"]:
        errors.append(
            f"Slice thickness too small: {slice_thickness}mm < "
            f"{limits['min_slice_thickness']}mm"
        )
    elif slice_thickness > limits["max_slice_thickness"]:
        errors.append(
            f"Slice thickness too large: {slice_thickness}mm > "
            f"{limits['max_slice_thickness']}mm"
        )

    return errors
    
    if structure_volume < limits["min_structure_volume"]:
        errors.append(
            f"Structure '{structure_name}' volume {structure_volume:.3f}cc < "
            f"minimum {limits['min_structure_volume']}cc"
        )
    
    return errors


def check_contour_closure(
    contour_points: NDArray[np.float64],
    tolerance: Optional[float] = None,
) -> bool:
    """
    Check if contour is properly closed.
    
    Parameters
    ----------
    contour_points : NDArray
        Contour points (N x 2 or N x 3 array)
    tolerance : float, optional
        Distance tolerance for closure check
        
    Returns
    -------
    bool
        True if contour is closed within tolerance
    """
    if tolerance is None:
        tolerance = CLINICAL_GEOMETRIC_LIMITS["contour_point_tolerance"]
    
    if len(contour_points) < 3:
        return False
    
    # Check distance between first and last points
    first_point = contour_points[0]
    last_point = contour_points[-1]
    distance = np.linalg.norm(last_point - first_point)
    
    return bool(distance <= tolerance)