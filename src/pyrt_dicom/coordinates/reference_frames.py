# Copyright (C) 2024 Pirate DICOM Contributors

"""
Frame of reference management for RT DICOM coordinate systems.

Provides consistent Frame of Reference UID management and geometric relationship
tracking across CT, RTSTRUCT, RTDOSE, and RTPLAN objects to ensure all RT
objects share the same coordinate system.
"""

from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, field
import numpy as np

from ..utils.exceptions import CoordinateSystemError
from ..uid_generation.generators import UIDGenerator, DefaultUIDGenerator
from .transforms import CoordinateTransformer, DICOM_PATIENT_ORIENTATIONS


@dataclass
class GeometricParameters:
    """Geometric parameters for a DICOM image frame of reference."""
    
    image_position: Tuple[float, float, float]
    image_orientation: List[float]  # 6-element vector
    pixel_spacing: Tuple[float, float]
    slice_thickness: float
    slice_locations: Optional[List[float]] = None
    patient_position: str = "HFS"
    
    def __post_init__(self):
        """Validate geometric parameters after initialization."""
        if len(self.image_orientation) != 6:
            raise CoordinateSystemError(
                f"Image orientation must have 6 elements, got {len(self.image_orientation)}"
            )
        
        if self.patient_position not in DICOM_PATIENT_ORIENTATIONS:
            valid_positions = list(DICOM_PATIENT_ORIENTATIONS.keys())
            raise CoordinateSystemError(
                f"Invalid patient position '{self.patient_position}'. "
                f"Must be one of: {valid_positions}"
            )
        
        if len(self.pixel_spacing) != 2:
            raise CoordinateSystemError(
                f"Pixel spacing must have 2 elements, got {len(self.pixel_spacing)}"
            )
        
        if self.slice_thickness <= 0:
            raise CoordinateSystemError(
                f"Slice thickness must be positive, got {self.slice_thickness}"
            )
        
        if any(ps <= 0 for ps in self.pixel_spacing):
            raise CoordinateSystemError(
                f"Pixel spacing values must be positive, got {self.pixel_spacing}"
            )


@dataclass 
class FrameOfReferenceInfo:
    """Information about a DICOM Frame of Reference."""
    
    uid: str
    geometric_parameters: GeometricParameters
    description: str = ""
    associated_objects: Set[str] = field(default_factory=set)
    creation_timestamp: Optional[str] = None
    
    def add_associated_object(self, object_uid: str, object_type: str) -> None:
        """Add an associated DICOM object to this frame of reference."""
        self.associated_objects.add(f"{object_type}:{object_uid}")
    
    def get_associated_objects_by_type(self, object_type: str) -> List[str]:
        """Get all associated objects of a specific type."""
        prefix = f"{object_type}:"
        return [
            obj.split(":", 1)[1] for obj in self.associated_objects
            if obj.startswith(prefix)
        ]


class FrameOfReference:
    """
    Frame of Reference management for RT DICOM coordinate systems.
    
    Ensures consistent coordinate system handling across all RT objects
    (CT, RTSTRUCT, RTDOSE, RTPLAN) by maintaining Frame of Reference UIDs
    and geometric parameters.
    """
    
    def __init__(self, uid_generator: Optional[UIDGenerator] = None) -> None:
        """
        Initialize Frame of Reference manager.
        
        Parameters
        ----------
        uid_generator : UIDGenerator, optional
            UID generator for creating Frame of Reference UIDs
        """
        self.uid_generator = uid_generator or DefaultUIDGenerator.create_default_generator()
        self._frames: Dict[str, FrameOfReferenceInfo] = {}
        self._primary_frame_uid: Optional[str] = None
    
    def create_frame_of_reference(
        self,
        geometric_parameters: GeometricParameters,
        description: str = "",
        uid: Optional[str] = None,
    ) -> str:
        """
        Create a new Frame of Reference.
        
        Parameters
        ----------
        geometric_parameters : GeometricParameters
            Geometric parameters defining the coordinate system
        description : str, optional
            Description of the frame of reference
        uid : str, optional
            Specific UID to use (if None, generates new one)
            
        Returns
        -------
        str
            Frame of Reference UID
            
        Raises
        ------
        CoordinateSystemError
            If UID already exists or parameters are invalid
        """
        if uid is None:
            uid = self.uid_generator.generate_frame_of_reference_uid()
        
        if uid in self._frames:
            raise CoordinateSystemError(
                f"Frame of Reference UID '{uid}' already exists"
            )
        
        frame_info = FrameOfReferenceInfo(
            uid=uid,
            geometric_parameters=geometric_parameters,
            description=description,
        )
        
        self._frames[uid] = frame_info
        
        # Set as primary frame if it's the first one
        if self._primary_frame_uid is None:
            self._primary_frame_uid = uid
            
        return uid
    
    def get_frame_of_reference(self, uid: str) -> FrameOfReferenceInfo:
        """
        Get Frame of Reference information by UID.
        
        Parameters
        ----------
        uid : str
            Frame of Reference UID
            
        Returns
        -------
        FrameOfReferenceInfo
            Frame of reference information
            
        Raises
        ------
        CoordinateSystemError
            If Frame of Reference UID not found
        """
        if uid not in self._frames:
            raise CoordinateSystemError(
                f"Frame of Reference UID '{uid}' not found"
            )
        return self._frames[uid]
    
    def associate_object(
        self,
        frame_uid: str,
        object_uid: str,
        object_type: str,
    ) -> None:
        """
        Associate a DICOM object with a Frame of Reference.
        
        Parameters
        ----------
        frame_uid : str
            Frame of Reference UID
        object_uid : str
            DICOM object UID (Series Instance UID or SOP Instance UID)
        object_type : str
            Type of DICOM object ('CT', 'RTSTRUCT', 'RTDOSE', 'RTPLAN')
            
        Raises
        ------
        CoordinateSystemError
            If Frame of Reference UID not found
        """
        if frame_uid not in self._frames:
            raise CoordinateSystemError(
                f"Frame of Reference UID '{frame_uid}' not found"
            )
        
        self._frames[frame_uid].add_associated_object(object_uid, object_type)
    
    def validate_geometric_consistency(
        self,
        frame_uid: str,
        new_parameters: GeometricParameters,
        tolerance: float = 1e-3,
    ) -> bool:
        """
        Validate that new geometric parameters are consistent with existing frame.
        
        Parameters
        ----------
        frame_uid : str
            Frame of Reference UID
        new_parameters : GeometricParameters
            New geometric parameters to validate
        tolerance : float
            Tolerance for floating point comparisons (mm)
            
        Returns
        -------
        bool
            True if parameters are consistent
            
        Raises
        ------
        CoordinateSystemError
            If validation fails with specific error details
        """
        if frame_uid not in self._frames:
            raise CoordinateSystemError(
                f"Frame of Reference UID '{frame_uid}' not found"
            )
        
        existing = self._frames[frame_uid].geometric_parameters
        
        # Check patient position
        if existing.patient_position != new_parameters.patient_position:
            raise CoordinateSystemError(
                f"Patient position mismatch: existing '{existing.patient_position}' "
                f"vs new '{new_parameters.patient_position}'"
            )
        
        # Check image orientation (direction cosines)
        existing_orientation = np.array(existing.image_orientation)
        new_orientation = np.array(new_parameters.image_orientation)
        
        if not np.allclose(existing_orientation, new_orientation, atol=tolerance):
            raise CoordinateSystemError(
                f"Image orientation mismatch: existing {existing.image_orientation} "
                f"vs new {new_parameters.image_orientation} (tolerance={tolerance})"
            )
        
        # Check pixel spacing consistency
        existing_spacing = np.array(existing.pixel_spacing)
        new_spacing = np.array(new_parameters.pixel_spacing)
        
        if not np.allclose(existing_spacing, new_spacing, atol=tolerance):
            raise CoordinateSystemError(
                f"Pixel spacing mismatch: existing {existing.pixel_spacing} "
                f"vs new {new_parameters.pixel_spacing} (tolerance={tolerance})"
            )
        
        # Check slice thickness consistency (allow some variation for different modalities)
        slice_thickness_tolerance = max(tolerance, 0.1)  # At least 0.1mm tolerance
        if not np.isclose(
            existing.slice_thickness, 
            new_parameters.slice_thickness, 
            atol=slice_thickness_tolerance
        ):
            raise CoordinateSystemError(
                f"Slice thickness mismatch: existing {existing.slice_thickness}mm "
                f"vs new {new_parameters.slice_thickness}mm "
                f"(tolerance={slice_thickness_tolerance}mm)"
            )
        
        return True
    
    def get_coordinate_transformer(self, frame_uid: str) -> CoordinateTransformer:
        """
        Get coordinate transformer for a Frame of Reference.
        
        Parameters
        ----------
        frame_uid : str
            Frame of Reference UID
            
        Returns
        -------
        CoordinateTransformer
            Coordinate transformer configured for this frame
            
        Raises
        ------
        CoordinateSystemError
            If Frame of Reference UID not found
        """
        if frame_uid not in self._frames:
            raise CoordinateSystemError(
                f"Frame of Reference UID '{frame_uid}' not found"
            )
        
        params = self._frames[frame_uid].geometric_parameters
        
        return CoordinateTransformer(
            patient_position=params.patient_position,
            image_orientation=params.image_orientation,
            pixel_spacing=params.pixel_spacing,
            slice_thickness=params.slice_thickness,
            image_position=params.image_position,
        )
    
    def get_primary_frame_uid(self) -> Optional[str]:
        """Get the primary Frame of Reference UID."""
        return self._primary_frame_uid
    
    def set_primary_frame_uid(self, uid: str) -> None:
        """
        Set the primary Frame of Reference UID.
        
        Parameters
        ----------
        uid : str
            Frame of Reference UID to set as primary
            
        Raises
        ------
        CoordinateSystemError
            If UID not found
        """
        if uid not in self._frames:
            raise CoordinateSystemError(
                f"Frame of Reference UID '{uid}' not found"
            )
        self._primary_frame_uid = uid
    
    def list_frames(self) -> List[str]:
        """List all Frame of Reference UIDs."""
        return list(self._frames.keys())
    
    def get_frame_summary(self, uid: str) -> Dict[str, any]:
        """
        Get summary information about a Frame of Reference.
        
        Parameters
        ----------
        uid : str
            Frame of Reference UID
            
        Returns
        -------
        Dict
            Summary information including associated objects
            
        Raises
        ------
        CoordinateSystemError
            If Frame of Reference UID not found
        """
        if uid not in self._frames:
            raise CoordinateSystemError(
                f"Frame of Reference UID '{uid}' not found"
            )
        
        frame_info = self._frames[uid]
        params = frame_info.geometric_parameters
        
        return {
            "uid": uid,
            "description": frame_info.description,
            "patient_position": params.patient_position,
            "image_position": params.image_position,
            "pixel_spacing": params.pixel_spacing,
            "slice_thickness": params.slice_thickness,
            "associated_objects": len(frame_info.associated_objects),
            "object_types": {
                obj_type: len(frame_info.get_associated_objects_by_type(obj_type))
                for obj_type in ["CT", "RTSTRUCT", "RTDOSE", "RTPLAN"]
            },
            "is_primary": uid == self._primary_frame_uid,
        }
    
    def validate_frame_consistency(self) -> List[str]:
        """
        Validate consistency across all frames of reference.
        
        Returns
        -------
        List[str]
            List of validation warnings/errors
        """
        warnings = []
        
        if not self._frames:
            warnings.append("No frames of reference defined")
            return warnings
        
        if self._primary_frame_uid is None:
            warnings.append("No primary frame of reference set")
        elif self._primary_frame_uid not in self._frames:
            warnings.append(
                f"Primary frame UID '{self._primary_frame_uid}' not found in frames"
            )
        
        # Check for orphaned frames (no associated objects)
        for uid, frame_info in self._frames.items():
            if not frame_info.associated_objects:
                warnings.append(f"Frame of Reference '{uid}' has no associated objects")
        
        # Check for geometric inconsistencies between frames
        if len(self._frames) > 1:
            frame_uids = list(self._frames.keys())
            primary_frame = self._frames[frame_uids[0]]
            
            for uid in frame_uids[1:]:
                current_frame = self._frames[uid]
                
                if (primary_frame.geometric_parameters.patient_position !=
                    current_frame.geometric_parameters.patient_position):
                    warnings.append(
                        f"Patient position inconsistency between frames: "
                        f"'{primary_frame.geometric_parameters.patient_position}' vs "
                        f"'{current_frame.geometric_parameters.patient_position}'"
                    )
        
        return warnings