# Copyright (C) 2024 Pirate DICOM Contributors

"""
Coordinate system utilities for RT DICOM creation.

This module provides coordinate transformation utilities and frame of reference
management for consistent geometric handling across all RT DICOM types.
"""

from .reference_frames import FrameOfReference, GeometricParameters
from .transforms import (
    DICOM_PATIENT_ORIENTATIONS,
    CoordinateTransformer,
    dicom_to_patient_coordinates,
    patient_to_dicom_coordinates,
    transform_image_orientation,
    validate_patient_position,
)

__all__ = [
    "FrameOfReference",
    "GeometricParameters",
    "CoordinateTransformer", 
    "DICOM_PATIENT_ORIENTATIONS",
    "dicom_to_patient_coordinates",
    "patient_to_dicom_coordinates",
    "transform_image_orientation",
    "validate_patient_position",
]