# Copyright (C) 2024 Pirate DICOM Contributors

"""
Coordinate transformation utilities for RT DICOM creation.

This module provides coordinate system transformations between patient anatomical
coordinates and DICOM image coordinates, inspired by PyMedPhys patterns but
generalized for any input format.

Key Features:
- Patient position handling following DICOM standard orientations
- Image orientation patient vector transformations
- Sub-millimeter geometric accuracy for clinical applications
- Comprehensive validation for clinical safety
"""

from typing import List, Optional, Tuple, Union

import numpy as np
from numpy.typing import NDArray

from ..utils.exceptions import CoordinateSystemError

# DICOM Patient Position definitions (from PyMedPhys orientation.py)
DICOM_PATIENT_ORIENTATIONS = {
    "HFP": "Head First-Prone",
    "HFS": "Head First-Supine", 
    "HFDR": "Head First-Decubitus Right",
    "HFDL": "Head First-Decubitus Left",
    "FFDR": "Feet First-Decubitus Right",
    "FFDL": "Feet First-Decubitus Left",
    "FFP": "Feet First-Prone",
    "FFS": "Feet First-Supine",
    "LFP": "Left First-Prone",
    "LFS": "Left First-Supine", 
    "RFP": "Right First-Prone",
    "RFS": "Right First-Supine",
    "AFDR": "Anterior First-Decubitus Right",
    "AFDL": "Anterior First-Decubitus Left",
    "PFDR": "Posterior First-Decubitus Right",
    "PFDL": "Posterior First-Decubitus Left",
    "SITTING": "In the sitting position, the patient's face is towards the front of the chair",
}

# Image orientation patient vectors for common orientations (from PyMedPhys)
IMAGE_ORIENTATION_MAP = {
    "FFDL": [0, 1, 0, 1, 0, 0],
    "FFDR": [0, -1, 0, -1, 0, 0],
    "FFP": [1, 0, 0, 0, -1, 0],
    "FFS": [-1, 0, 0, 0, 1, 0],
    "HFDL": [0, -1, 0, 1, 0, 0],
    "HFDR": [0, 1, 0, -1, 0, 0],
    "HFP": [-1, 0, 0, 0, -1, 0],
    "HFS": [1, 0, 0, 0, 1, 0],
}


class CoordinateTransformer:
    """
    Coordinate transformation utilities for RT DICOM creation.
    
    Provides transformations between patient anatomical coordinates and DICOM
    image coordinates with sub-millimeter accuracy for clinical applications.
    """

    def __init__(
        self,
        patient_position: str = "HFS",
        image_orientation: Optional[List[float]] = None,
        pixel_spacing: Optional[Tuple[float, float]] = None,
        slice_thickness: Optional[float] = None,
        image_position: Optional[Tuple[float, float, float]] = None,
    ) -> None:
        """
        Initialize coordinate transformer.

        Parameters
        ----------
        patient_position : str
            DICOM patient position (e.g., "HFS", "HFP")
        image_orientation : List[float], optional
            6-element image orientation patient vector
        pixel_spacing : Tuple[float, float], optional  
            Row and column pixel spacing in mm
        slice_thickness : float, optional
            Slice thickness in mm
        image_position : Tuple[float, float, float], optional
            Image position patient coordinates in mm

        Raises
        ------
        CoordinateSystemError
            If patient position is invalid or parameters are inconsistent
        """
        self.patient_position = self._validate_patient_position(patient_position)
        self.image_orientation = self._validate_image_orientation(
            image_orientation, patient_position
        )
        self.pixel_spacing = pixel_spacing
        self.slice_thickness = slice_thickness
        self.image_position = image_position or (0.0, 0.0, 0.0)

        # Create transformation matrices
        self._row_cosines = np.array(self.image_orientation[:3])
        self._col_cosines = np.array(self.image_orientation[3:])
        self._slice_cosines = np.cross(self._row_cosines, self._col_cosines)

    @staticmethod
    def _validate_patient_position(patient_position: str) -> str:
        """Validate patient position against DICOM standard."""
        if patient_position not in DICOM_PATIENT_ORIENTATIONS:
            valid_positions = list(DICOM_PATIENT_ORIENTATIONS.keys())
            raise CoordinateSystemError(
                f"Invalid patient position '{patient_position}'. "
                f"Must be one of: {valid_positions}"
            )
        return patient_position

    @staticmethod  
    def _validate_image_orientation(
        image_orientation: Optional[List[float]], patient_position: str
    ) -> List[float]:
        """Validate and get image orientation patient vector."""
        if image_orientation is None:
            if patient_position in IMAGE_ORIENTATION_MAP:
                return IMAGE_ORIENTATION_MAP[patient_position]
            else:
                raise CoordinateSystemError(
                    f"No default image orientation available for patient position '{patient_position}'",
                    patient_position=patient_position,
                    clinical_context={
                        'available_defaults': list(IMAGE_ORIENTATION_MAP.keys()),
                        'solution': 'Provide explicit image_orientation parameter'
                    }
                )

        if len(image_orientation) != 6:
            raise CoordinateSystemError(
                f"Image orientation must have 6 elements (row and column direction cosines), got {len(image_orientation)}",
                clinical_context={
                    'required_elements': 6,
                    'current_elements': len(image_orientation),
                    'format': '[row_x, row_y, row_z, col_x, col_y, col_z]',
                    'units': 'direction cosines (unit vectors)'
                }
            )

        # Validate that row and column cosines are orthogonal and normalized
        row_cosines = np.array(image_orientation[:3])
        col_cosines = np.array(image_orientation[3:])

        row_magnitude = np.linalg.norm(row_cosines)
        col_magnitude = np.linalg.norm(col_cosines)
        dot_product = np.dot(row_cosines, col_cosines)

        if not np.isclose(row_magnitude, 1.0, atol=1e-6):
            raise CoordinateSystemError(
                f"Row direction cosines must be normalized (magnitude=1.0), "
                f"got magnitude={row_magnitude:.6f}"
            )

        if not np.isclose(col_magnitude, 1.0, atol=1e-6):
            raise CoordinateSystemError(
                f"Column direction cosines must be normalized (magnitude=1.0), "
                f"got magnitude={col_magnitude:.6f}"
            )

        if not np.isclose(dot_product, 0.0, atol=1e-6):
            raise CoordinateSystemError(
                f"Row and column direction cosines must be orthogonal "
                f"(dot product=0.0), got dot product={dot_product:.6f}"
            )

        return image_orientation

    def dicom_to_patient(
        self,
        coordinates: Union[NDArray[np.float64], List[Tuple[float, float, float]]],
    ) -> NDArray[np.float64]:
        """
        Transform DICOM image coordinates to patient anatomical coordinates.

        Parameters
        ----------
        coordinates : array-like
            DICOM coordinates as (row, column, slice) or array of such coordinates

        Returns
        -------
        NDArray[np.float64]
            Patient coordinates as (x, y, z) in mm

        Raises
        ------
        CoordinateSystemError
            If pixel spacing or slice thickness not set
        """
        if self.pixel_spacing is None or self.slice_thickness is None:
            raise CoordinateSystemError(
                "Pixel spacing and slice thickness must be set for coordinate transformation"
            )

        coords = np.asarray(coordinates)
        input_was_1d = coords.ndim == 1
        if coords.ndim == 1:
            coords = coords.reshape(1, -1)

        if coords.shape[1] != 3:
            raise CoordinateSystemError(
                f"Coordinates must have 3 dimensions (row, column, slice), "
                f"got shape {coords.shape}"
            )

        # Convert image indices to physical distances
        physical_coords = np.zeros_like(coords, dtype=np.float64)
        physical_coords[:, 0] = coords[:, 0] * self.pixel_spacing[0]  # rows
        physical_coords[:, 1] = coords[:, 1] * self.pixel_spacing[1]  # columns
        physical_coords[:, 2] = coords[:, 2] * self.slice_thickness    # slices

        # Transform to patient coordinate system
        patient_coords = np.zeros_like(physical_coords)
        for i in range(len(physical_coords)):
            patient_coords[i] = (
                self.image_position +
                physical_coords[i, 0] * self._row_cosines +
                physical_coords[i, 1] * self._col_cosines +
                physical_coords[i, 2] * self._slice_cosines
            )

        return patient_coords.squeeze() if input_was_1d else patient_coords

    def patient_to_dicom(
        self,
        coordinates: Union[NDArray[np.float64], List[Tuple[float, float, float]]],
    ) -> NDArray[np.float64]:
        """
        Transform patient anatomical coordinates to DICOM image coordinates.

        Parameters
        ----------
        coordinates : array-like
            Patient coordinates as (x, y, z) in mm

        Returns
        -------
        NDArray[np.float64]
            DICOM coordinates as (row, column, slice)

        Raises
        ------
        CoordinateSystemError
            If pixel spacing or slice thickness not set
        """
        if self.pixel_spacing is None or self.slice_thickness is None:
            raise CoordinateSystemError(
                "Pixel spacing and slice thickness must be set for coordinate transformation"
            )

        coords = np.asarray(coordinates)
        input_was_1d = coords.ndim == 1
        if coords.ndim == 1:
            coords = coords.reshape(1, -1)

        if coords.shape[1] != 3:
            raise CoordinateSystemError(
                f"Coordinates must have 3 dimensions (x, y, z), got shape {coords.shape}"
            )

        # Create transformation matrix from patient to image coordinates
        transform_matrix = np.column_stack([
            self._row_cosines,
            self._col_cosines, 
            self._slice_cosines
        ])

        # Transform to image coordinate system
        image_coords = np.zeros_like(coords, dtype=np.float64)
        image_position = np.array(self.image_position)
        
        for i in range(len(coords)):
            # Subtract image position to get relative coordinates
            relative_coords = coords[i] - image_position
            
            # Transform using inverse matrix
            physical_coords = np.linalg.solve(transform_matrix, relative_coords)
            
            # Convert physical distances to image indices
            image_coords[i, 0] = physical_coords[0] / self.pixel_spacing[0]  # rows
            image_coords[i, 1] = physical_coords[1] / self.pixel_spacing[1]  # columns
            image_coords[i, 2] = physical_coords[2] / self.slice_thickness    # slices

        return image_coords.squeeze() if input_was_1d else image_coords

    def get_transformation_matrix(self) -> NDArray[np.float64]:
        """
        Get 4x4 transformation matrix for homogeneous coordinates.

        Returns
        -------
        NDArray[np.float64]
            4x4 transformation matrix from image to patient coordinates
        """
        if self.pixel_spacing is None or self.slice_thickness is None:
            raise CoordinateSystemError(
                "Pixel spacing and slice thickness must be set to get transformation matrix"
            )

        matrix = np.eye(4)
        
        # Set rotation part
        matrix[0, :3] = self._row_cosines * self.pixel_spacing[0]
        matrix[1, :3] = self._col_cosines * self.pixel_spacing[1]
        matrix[2, :3] = self._slice_cosines * self.slice_thickness
        
        # Set translation part
        matrix[:3, 3] = self.image_position

        return matrix


# Convenience functions for common operations

def validate_patient_position(patient_position: str, image_orientation: List[float]) -> None:
    """
    Validate that patient position matches image orientation (PyMedPhys pattern).

    Parameters
    ----------
    patient_position : str
        DICOM patient position
    image_orientation : List[float]
        6-element image orientation patient vector

    Raises
    ------
    CoordinateSystemError
        If patient position doesn't match image orientation
    """
    if patient_position not in DICOM_PATIENT_ORIENTATIONS:
        valid_positions = list(DICOM_PATIENT_ORIENTATIONS.keys())
        raise CoordinateSystemError(
            f"Invalid patient position '{patient_position}'. "
            f"Must be one of: {valid_positions}"
        )

    if patient_position in IMAGE_ORIENTATION_MAP:
        expected_orientation = IMAGE_ORIENTATION_MAP[patient_position]
        if not np.allclose(image_orientation, expected_orientation, atol=1e-6):
            raise CoordinateSystemError(
                f"Patient position '{patient_position}' does not match image orientation. "
                f"Expected {expected_orientation}, got {image_orientation}."
            )


def transform_image_orientation(
    from_position: str, to_position: str
) -> List[float]:
    """
    Transform image orientation from one patient position to another.

    Parameters
    ----------
    from_position : str
        Source patient position
    to_position : str
        Target patient position

    Returns
    -------
    List[float]
        6-element image orientation vector for target position

    Raises
    ------
    CoordinateSystemError
        If positions are invalid or transformation not supported
    """
    if from_position not in IMAGE_ORIENTATION_MAP:
        raise CoordinateSystemError(
            f"No image orientation mapping available for position '{from_position}'"
        )
    
    if to_position not in IMAGE_ORIENTATION_MAP:
        raise CoordinateSystemError(
            f"No image orientation mapping available for position '{to_position}'"
        )

    return IMAGE_ORIENTATION_MAP[to_position]


def dicom_to_patient_coordinates(
    dicom_coords: Union[NDArray[np.float64], List[Tuple[float, float, float]]],
    patient_position: str = "HFS",
    pixel_spacing: Tuple[float, float] = (1.0, 1.0),
    slice_thickness: float = 1.0,
    image_position: Tuple[float, float, float] = (0.0, 0.0, 0.0),
    image_orientation: Optional[List[float]] = None,
) -> NDArray[np.float64]:
    """
    Convenience function for DICOM to patient coordinate transformation.

    Parameters
    ----------
    dicom_coords : array-like
        DICOM coordinates as (row, column, slice)
    patient_position : str
        DICOM patient position
    pixel_spacing : Tuple[float, float]
        Row and column pixel spacing in mm
    slice_thickness : float
        Slice thickness in mm
    image_position : Tuple[float, float, float]
        Image position patient coordinates in mm
    image_orientation : List[float], optional
        6-element image orientation patient vector

    Returns
    -------
    NDArray[np.float64]
        Patient coordinates as (x, y, z) in mm
    """
    transformer = CoordinateTransformer(
        patient_position=patient_position,
        image_orientation=image_orientation,
        pixel_spacing=pixel_spacing,
        slice_thickness=slice_thickness,
        image_position=image_position,
    )
    return transformer.dicom_to_patient(dicom_coords)


def patient_to_dicom_coordinates(
    patient_coords: Union[NDArray[np.float64], List[Tuple[float, float, float]]],
    patient_position: str = "HFS",
    pixel_spacing: Tuple[float, float] = (1.0, 1.0),
    slice_thickness: float = 1.0,
    image_position: Tuple[float, float, float] = (0.0, 0.0, 0.0),
    image_orientation: Optional[List[float]] = None,
) -> NDArray[np.float64]:
    """
    Convenience function for patient to DICOM coordinate transformation.

    Parameters
    ----------
    patient_coords : array-like
        Patient coordinates as (x, y, z) in mm
    patient_position : str
        DICOM patient position
    pixel_spacing : Tuple[float, float]
        Row and column pixel spacing in mm
    slice_thickness : float
        Slice thickness in mm
    image_position : Tuple[float, float, float]
        Image position patient coordinates in mm
    image_orientation : List[float], optional
        6-element image orientation patient vector

    Returns
    -------
    NDArray[np.float64]
        DICOM coordinates as (row, column, slice)
    """
    transformer = CoordinateTransformer(
        patient_position=patient_position,
        image_orientation=image_orientation,
        pixel_spacing=pixel_spacing,
        slice_thickness=slice_thickness,
        image_position=image_position,
    )
    return transformer.patient_to_dicom(patient_coords)