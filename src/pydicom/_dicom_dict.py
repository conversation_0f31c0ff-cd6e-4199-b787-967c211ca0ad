"""DICOM data dictionary auto-generated by generate_dicom_dict.py"""

# Each dict entry is Tag: (VR, VM, Name, Retired, Keyword)
DicomDictionary: dict[int, tuple[str, str, str, str, str]] = {
    0x00000000: ('UL', '1', "Command Group Length", '', 'CommandGroupLength'),
    0x00000001: ('UL', '1', "Command Length to End", 'Retired', 'CommandLengthToEnd'),
    0x00000002: ('UI', '1', "Affected SOP Class UID", '', 'AffectedSOPClassUID'),
    0x00000003: ('UI', '1', "Requested SOP Class UID", '', 'RequestedSOPClassUID'),
    0x00000010: ('SH', '1', "Command Recognition Code", 'Retired', 'CommandRecognitionCode'),
    0x00000100: ('US', '1', "Command Field", '', 'CommandField'),
    0x00000110: ('US', '1', "Message ID", '', 'MessageID'),
    0x00000120: ('US', '1', "Message ID Being Responded To", '', 'MessageIDBeingRespondedTo'),
    0x00000200: ('AE', '1', "Initiator", 'Retired', 'Initiator'),
    0x00000300: ('AE', '1', "Receiver", 'Retired', 'Receiver'),
    0x00000400: ('AE', '1', "Find Location", 'Retired', 'FindLocation'),
    0x00000600: ('AE', '1', "Move Destination", '', 'MoveDestination'),
    0x00000700: ('US', '1', "Priority", '', 'Priority'),
    0x00000800: ('US', '1', "Command Data Set Type", '', 'CommandDataSetType'),
    0x00000850: ('US', '1', "Number of Matches", 'Retired', 'NumberOfMatches'),
    0x00000860: ('US', '1', "Response Sequence Number", 'Retired', 'ResponseSequenceNumber'),
    0x00000900: ('US', '1', "Status", '', 'Status'),
    0x00000901: ('AT', '1-n', "Offending Element", '', 'OffendingElement'),
    0x00000902: ('LO', '1', "Error Comment", '', 'ErrorComment'),
    0x00000903: ('US', '1', "Error ID", '', 'ErrorID'),
    0x00001000: ('UI', '1', "Affected SOP Instance UID", '', 'AffectedSOPInstanceUID'),
    0x00001001: ('UI', '1', "Requested SOP Instance UID", '', 'RequestedSOPInstanceUID'),
    0x00001002: ('US', '1', "Event Type ID", '', 'EventTypeID'),
    0x00001005: ('AT', '1-n', "Attribute Identifier List", '', 'AttributeIdentifierList'),
    0x00001008: ('US', '1', "Action Type ID", '', 'ActionTypeID'),
    0x00001020: ('US', '1', "Number of Remaining Sub-operations", '', 'NumberOfRemainingSuboperations'),
    0x00001021: ('US', '1', "Number of Completed Sub-operations", '', 'NumberOfCompletedSuboperations'),
    0x00001022: ('US', '1', "Number of Failed Sub-operations", '', 'NumberOfFailedSuboperations'),
    0x00001023: ('US', '1', "Number of Warning Sub-operations", '', 'NumberOfWarningSuboperations'),
    0x00001030: ('AE', '1', "Move Originator Application Entity Title", '', 'MoveOriginatorApplicationEntityTitle'),
    0x00001031: ('US', '1', "Move Originator Message ID", '', 'MoveOriginatorMessageID'),
    0x00004000: ('LT', '1', "Dialog Receiver", 'Retired', 'DialogReceiver'),
    0x00004010: ('LT', '1', "Terminal Type", 'Retired', 'TerminalType'),
    0x00005010: ('SH', '1', "Message Set ID", 'Retired', 'MessageSetID'),
    0x00005020: ('SH', '1', "End Message ID", 'Retired', 'EndMessageID'),
    0x00005110: ('LT', '1', "Display Format", 'Retired', 'DisplayFormat'),
    0x00005120: ('LT', '1', "Page Position ID", 'Retired', 'PagePositionID'),
    0x00005130: ('CS', '1', "Text Format ID", 'Retired', 'TextFormatID'),
    0x00005140: ('CS', '1', "Normal/Reverse", 'Retired', 'NormalReverse'),
    0x00005150: ('CS', '1', "Add Gray Scale", 'Retired', 'AddGrayScale'),
    0x00005160: ('CS', '1', "Borders", 'Retired', 'Borders'),
    0x00005170: ('IS', '1', "Copies", 'Retired', 'Copies'),
    0x00005180: ('CS', '1', "Command Magnification Type", 'Retired', 'CommandMagnificationType'),
    0x00005190: ('CS', '1', "Erase", 'Retired', 'Erase'),
    0x000051A0: ('CS', '1', "Print", 'Retired', 'Print'),
    0x000051B0: ('US', '1-n', "Overlays", 'Retired', 'Overlays'),
    0x00020000: ('UL', '1', "File Meta Information Group Length", '', 'FileMetaInformationGroupLength'),
    0x00020001: ('OB', '1', "File Meta Information Version", '', 'FileMetaInformationVersion'),
    0x00020002: ('UI', '1', "Media Storage SOP Class UID", '', 'MediaStorageSOPClassUID'),
    0x00020003: ('UI', '1', "Media Storage SOP Instance UID", '', 'MediaStorageSOPInstanceUID'),
    0x00020010: ('UI', '1', "Transfer Syntax UID", '', 'TransferSyntaxUID'),
    0x00020012: ('UI', '1', "Implementation Class UID", '', 'ImplementationClassUID'),
    0x00020013: ('SH', '1', "Implementation Version Name", '', 'ImplementationVersionName'),
    0x00020016: ('AE', '1', "Source Application Entity Title", '', 'SourceApplicationEntityTitle'),
    0x00020017: ('AE', '1', "Sending Application Entity Title", '', 'SendingApplicationEntityTitle'),
    0x00020018: ('AE', '1', "Receiving Application Entity Title", '', 'ReceivingApplicationEntityTitle'),
    0x00020026: ('UR', '1', "Source Presentation Address", '', 'SourcePresentationAddress'),
    0x00020027: ('UR', '1', "Sending Presentation Address", '', 'SendingPresentationAddress'),
    0x00020028: ('UR', '1', "Receiving Presentation Address", '', 'ReceivingPresentationAddress'),
    0x00020031: ('OB', '1', "RTV Meta Information Version", '', 'RTVMetaInformationVersion'),
    0x00020032: ('UI', '1', "RTV Communication SOP Class UID", '', 'RTVCommunicationSOPClassUID'),
    0x00020033: ('UI', '1', "RTV Communication SOP Instance UID", '', 'RTVCommunicationSOPInstanceUID'),
    0x00020035: ('OB', '1', "RTV Source Identifier", '', 'RTVSourceIdentifier'),
    0x00020036: ('OB', '1', "RTV Flow Identifier", '', 'RTVFlowIdentifier'),
    0x00020037: ('UL', '1', "RTV Flow RTP Sampling Rate", '', 'RTVFlowRTPSamplingRate'),
    0x00020038: ('FD', '1', "RTV Flow Actual Frame Duration", '', 'RTVFlowActualFrameDuration'),
    0x00020100: ('UI', '1', "Private Information Creator UID", '', 'PrivateInformationCreatorUID'),
    0x00020102: ('OB', '1', "Private Information", '', 'PrivateInformation'),
    0x00041130: ('CS', '1', "File-set ID", '', 'FileSetID'),
    0x00041141: ('CS', '1-8', "File-set Descriptor File ID", '', 'FileSetDescriptorFileID'),
    0x00041142: ('CS', '1', "Specific Character Set of File-set Descriptor File", '', 'SpecificCharacterSetOfFileSetDescriptorFile'),
    0x00041200: ('UL', '1', "Offset of the First Directory Record of the Root Directory Entity", '', 'OffsetOfTheFirstDirectoryRecordOfTheRootDirectoryEntity'),
    0x00041202: ('UL', '1', "Offset of the Last Directory Record of the Root Directory Entity", '', 'OffsetOfTheLastDirectoryRecordOfTheRootDirectoryEntity'),
    0x00041212: ('US', '1', "File-set Consistency Flag", '', 'FileSetConsistencyFlag'),
    0x00041220: ('SQ', '1', "Directory Record Sequence", '', 'DirectoryRecordSequence'),
    0x00041400: ('UL', '1', "Offset of the Next Directory Record", '', 'OffsetOfTheNextDirectoryRecord'),
    0x00041410: ('US', '1', "Record In-use Flag", '', 'RecordInUseFlag'),
    0x00041420: ('UL', '1', "Offset of Referenced Lower-Level Directory Entity", '', 'OffsetOfReferencedLowerLevelDirectoryEntity'),
    0x00041430: ('CS', '1', "Directory Record Type", '', 'DirectoryRecordType'),
    0x00041432: ('UI', '1', "Private Record UID", '', 'PrivateRecordUID'),
    0x00041500: ('CS', '1-8', "Referenced File ID", '', 'ReferencedFileID'),
    0x00041504: ('UL', '1', "MRDR Directory Record Offset", 'Retired', 'MRDRDirectoryRecordOffset'),
    0x00041510: ('UI', '1', "Referenced SOP Class UID in File", '', 'ReferencedSOPClassUIDInFile'),
    0x00041511: ('UI', '1', "Referenced SOP Instance UID in File", '', 'ReferencedSOPInstanceUIDInFile'),
    0x00041512: ('UI', '1', "Referenced Transfer Syntax UID in File", '', 'ReferencedTransferSyntaxUIDInFile'),
    0x0004151A: ('UI', '1-n', "Referenced Related General SOP Class UID in File", '', 'ReferencedRelatedGeneralSOPClassUIDInFile'),
    0x00041600: ('UL', '1', "Number of References", 'Retired', 'NumberOfReferences'),
    0x00080001: ('UL', '1', "Length to End", 'Retired', 'LengthToEnd'),
    0x00080005: ('CS', '1-n', "Specific Character Set", '', 'SpecificCharacterSet'),
    0x00080006: ('SQ', '1', "Language Code Sequence", '', 'LanguageCodeSequence'),
    0x00080008: ('CS', '2-n', "Image Type", '', 'ImageType'),
    0x00080010: ('SH', '1', "Recognition Code", 'Retired', 'RecognitionCode'),
    0x00080012: ('DA', '1', "Instance Creation Date", '', 'InstanceCreationDate'),
    0x00080013: ('TM', '1', "Instance Creation Time", '', 'InstanceCreationTime'),
    0x00080014: ('UI', '1', "Instance Creator UID", '', 'InstanceCreatorUID'),
    0x00080015: ('DT', '1', "Instance Coercion DateTime", '', 'InstanceCoercionDateTime'),
    0x00080016: ('UI', '1', "SOP Class UID", '', 'SOPClassUID'),
    0x00080017: ('UI', '1', "Acquisition UID", '', 'AcquisitionUID'),
    0x00080018: ('UI', '1', "SOP Instance UID", '', 'SOPInstanceUID'),
    0x00080019: ('UI', '1', "Pyramid UID", '', 'PyramidUID'),
    0x0008001A: ('UI', '1-n', "Related General SOP Class UID", '', 'RelatedGeneralSOPClassUID'),
    0x0008001B: ('UI', '1', "Original Specialized SOP Class UID", '', 'OriginalSpecializedSOPClassUID'),
    0x0008001C: ('CS', '1', "Synthetic Data", '', 'SyntheticData'),
    0x00080020: ('DA', '1', "Study Date", '', 'StudyDate'),
    0x00080021: ('DA', '1', "Series Date", '', 'SeriesDate'),
    0x00080022: ('DA', '1', "Acquisition Date", '', 'AcquisitionDate'),
    0x00080023: ('DA', '1', "Content Date", '', 'ContentDate'),
    0x00080024: ('DA', '1', "Overlay Date", 'Retired', 'OverlayDate'),
    0x00080025: ('DA', '1', "Curve Date", 'Retired', 'CurveDate'),
    0x0008002A: ('DT', '1', "Acquisition DateTime", '', 'AcquisitionDateTime'),
    0x00080030: ('TM', '1', "Study Time", '', 'StudyTime'),
    0x00080031: ('TM', '1', "Series Time", '', 'SeriesTime'),
    0x00080032: ('TM', '1', "Acquisition Time", '', 'AcquisitionTime'),
    0x00080033: ('TM', '1', "Content Time", '', 'ContentTime'),
    0x00080034: ('TM', '1', "Overlay Time", 'Retired', 'OverlayTime'),
    0x00080035: ('TM', '1', "Curve Time", 'Retired', 'CurveTime'),
    0x00080040: ('US', '1', "Data Set Type", 'Retired', 'DataSetType'),
    0x00080041: ('LO', '1', "Data Set Subtype", 'Retired', 'DataSetSubtype'),
    0x00080042: ('CS', '1', "Nuclear Medicine Series Type", 'Retired', 'NuclearMedicineSeriesType'),
    0x00080050: ('SH', '1', "Accession Number", '', 'AccessionNumber'),
    0x00080051: ('SQ', '1', "Issuer of Accession Number Sequence", '', 'IssuerOfAccessionNumberSequence'),
    0x00080052: ('CS', '1', "Query/Retrieve Level", '', 'QueryRetrieveLevel'),
    0x00080053: ('CS', '1', "Query/Retrieve View", '', 'QueryRetrieveView'),
    0x00080054: ('AE', '1-n', "Retrieve AE Title", '', 'RetrieveAETitle'),
    0x00080055: ('AE', '1', "Station AE Title", '', 'StationAETitle'),
    0x00080056: ('CS', '1', "Instance Availability", '', 'InstanceAvailability'),
    0x00080058: ('UI', '1-n', "Failed SOP Instance UID List", '', 'FailedSOPInstanceUIDList'),
    0x00080060: ('CS', '1', "Modality", '', 'Modality'),
    0x00080061: ('CS', '1-n', "Modalities in Study", '', 'ModalitiesInStudy'),
    0x00080062: ('UI', '1-n', "SOP Classes in Study", '', 'SOPClassesInStudy'),
    0x00080063: ('SQ', '1', "Anatomic Regions in Study Code Sequence", '', 'AnatomicRegionsInStudyCodeSequence'),
    0x00080064: ('CS', '1', "Conversion Type", '', 'ConversionType'),
    0x00080068: ('CS', '1', "Presentation Intent Type", '', 'PresentationIntentType'),
    0x00080070: ('LO', '1', "Manufacturer", '', 'Manufacturer'),
    0x00080080: ('LO', '1', "Institution Name", '', 'InstitutionName'),
    0x00080081: ('ST', '1', "Institution Address", '', 'InstitutionAddress'),
    0x00080082: ('SQ', '1', "Institution Code Sequence", '', 'InstitutionCodeSequence'),
    0x00080090: ('PN', '1', "Referring Physician's Name", '', 'ReferringPhysicianName'),
    0x00080092: ('ST', '1', "Referring Physician's Address", '', 'ReferringPhysicianAddress'),
    0x00080094: ('SH', '1-n', "Referring Physician's Telephone Numbers", '', 'ReferringPhysicianTelephoneNumbers'),
    0x00080096: ('SQ', '1', "Referring Physician Identification Sequence", '', 'ReferringPhysicianIdentificationSequence'),
    0x0008009C: ('PN', '1-n', "Consulting Physician's Name", '', 'ConsultingPhysicianName'),
    0x0008009D: ('SQ', '1', "Consulting Physician Identification Sequence", '', 'ConsultingPhysicianIdentificationSequence'),
    0x00080100: ('SH', '1', "Code Value", '', 'CodeValue'),
    0x00080101: ('LO', '1', "Extended Code Value", '', 'ExtendedCodeValue'),
    0x00080102: ('SH', '1', "Coding Scheme Designator", '', 'CodingSchemeDesignator'),
    0x00080103: ('SH', '1', "Coding Scheme Version", '', 'CodingSchemeVersion'),
    0x00080104: ('LO', '1', "Code Meaning", '', 'CodeMeaning'),
    0x00080105: ('CS', '1', "Mapping Resource", '', 'MappingResource'),
    0x00080106: ('DT', '1', "Context Group Version", '', 'ContextGroupVersion'),
    0x00080107: ('DT', '1', "Context Group Local Version", '', 'ContextGroupLocalVersion'),
    0x00080108: ('LT', '1', "Extended Code Meaning", '', 'ExtendedCodeMeaning'),
    0x00080109: ('SQ', '1', "Coding Scheme Resources Sequence", '', 'CodingSchemeResourcesSequence'),
    0x0008010A: ('CS', '1', "Coding Scheme URL Type", '', 'CodingSchemeURLType'),
    0x0008010B: ('CS', '1', "Context Group Extension Flag", '', 'ContextGroupExtensionFlag'),
    0x0008010C: ('UI', '1', "Coding Scheme UID", '', 'CodingSchemeUID'),
    0x0008010D: ('UI', '1', "Context Group Extension Creator UID", '', 'ContextGroupExtensionCreatorUID'),
    0x0008010E: ('UR', '1', "Coding Scheme URL", '', 'CodingSchemeURL'),
    0x0008010F: ('CS', '1', "Context Identifier", '', 'ContextIdentifier'),
    0x00080110: ('SQ', '1', "Coding Scheme Identification Sequence", '', 'CodingSchemeIdentificationSequence'),
    0x00080112: ('LO', '1', "Coding Scheme Registry", '', 'CodingSchemeRegistry'),
    0x00080114: ('ST', '1', "Coding Scheme External ID", '', 'CodingSchemeExternalID'),
    0x00080115: ('ST', '1', "Coding Scheme Name", '', 'CodingSchemeName'),
    0x00080116: ('ST', '1', "Coding Scheme Responsible Organization", '', 'CodingSchemeResponsibleOrganization'),
    0x00080117: ('UI', '1', "Context UID", '', 'ContextUID'),
    0x00080118: ('UI', '1', "Mapping Resource UID", '', 'MappingResourceUID'),
    0x00080119: ('UC', '1', "Long Code Value", '', 'LongCodeValue'),
    0x00080120: ('UR', '1', "URN Code Value", '', 'URNCodeValue'),
    0x00080121: ('SQ', '1', "Equivalent Code Sequence", '', 'EquivalentCodeSequence'),
    0x00080122: ('LO', '1', "Mapping Resource Name", '', 'MappingResourceName'),
    0x00080123: ('SQ', '1', "Context Group Identification Sequence", '', 'ContextGroupIdentificationSequence'),
    0x00080124: ('SQ', '1', "Mapping Resource Identification Sequence", '', 'MappingResourceIdentificationSequence'),
    0x00080201: ('SH', '1', "Timezone Offset From UTC", '', 'TimezoneOffsetFromUTC'),
    0x00080202: ('OB', '1', "Retired-blank", 'Retired', ''),
    0x00080220: ('SQ', '1', "Responsible Group Code Sequence", '', 'ResponsibleGroupCodeSequence'),
    0x00080221: ('CS', '1', "Equipment Modality", '', 'EquipmentModality'),
    0x00080222: ('LO', '1', "Manufacturer's Related Model Group", '', 'ManufacturerRelatedModelGroup'),
    0x00080300: ('SQ', '1', "Private Data Element Characteristics Sequence", '', 'PrivateDataElementCharacteristicsSequence'),
    0x00080301: ('US', '1', "Private Group Reference", '', 'PrivateGroupReference'),
    0x00080302: ('LO', '1', "Private Creator Reference", '', 'PrivateCreatorReference'),
    0x00080303: ('CS', '1', "Block Identifying Information Status", '', 'BlockIdentifyingInformationStatus'),
    0x00080304: ('US', '1-n', "Nonidentifying Private Elements", '', 'NonidentifyingPrivateElements'),
    0x00080305: ('SQ', '1', "Deidentification Action Sequence", '', 'DeidentificationActionSequence'),
    0x00080306: ('US', '1-n', "Identifying Private Elements", '', 'IdentifyingPrivateElements'),
    0x00080307: ('CS', '1', "Deidentification Action", '', 'DeidentificationAction'),
    0x00080308: ('US', '1', "Private Data Element", '', 'PrivateDataElement'),
    0x00080309: ('UL', '1-3', "Private Data Element Value Multiplicity", '', 'PrivateDataElementValueMultiplicity'),
    0x0008030A: ('CS', '1', "Private Data Element Value Representation", '', 'PrivateDataElementValueRepresentation'),
    0x0008030B: ('UL', '1-2', "Private Data Element Number of Items", '', 'PrivateDataElementNumberOfItems'),
    0x0008030C: ('UC', '1', "Private Data Element Name", '', 'PrivateDataElementName'),
    0x0008030D: ('UC', '1', "Private Data Element Keyword", '', 'PrivateDataElementKeyword'),
    0x0008030E: ('UT', '1', "Private Data Element Description", '', 'PrivateDataElementDescription'),
    0x0008030F: ('UT', '1', "Private Data Element Encoding", '', 'PrivateDataElementEncoding'),
    0x00080310: ('SQ', '1', "Private Data Element Definition Sequence", '', 'PrivateDataElementDefinitionSequence'),
    0x00080400: ('SQ', '1', "Scope of Inventory Sequence", '', 'ScopeOfInventorySequence'),
    0x00080401: ('LT', '1', "Inventory Purpose", '', 'InventoryPurpose'),
    0x00080402: ('LT', '1', "Inventory Instance Description", '', 'InventoryInstanceDescription'),
    0x00080403: ('CS', '1', "Inventory Level", '', 'InventoryLevel'),
    0x00080404: ('DT', '1', "Item Inventory DateTime", '', 'ItemInventoryDateTime'),
    0x00080405: ('CS', '1', "Removed from Operational Use", '', 'RemovedFromOperationalUse'),
    0x00080406: ('SQ', '1', "Reason for Removal Code Sequence", '', 'ReasonForRemovalCodeSequence'),
    0x00080407: ('UR', '1', "Stored Instance Base URI", '', 'StoredInstanceBaseURI'),
    0x00080408: ('UR', '1', "Folder Access URI", '', 'FolderAccessURI'),
    0x00080409: ('UR', '1', "File Access URI", '', 'FileAccessURI'),
    0x0008040A: ('CS', '1', "Container File Type", '', 'ContainerFileType'),
    0x0008040B: ('UR', '1', "Filename in Container", '', 'FilenameInContainer'),
    0x0008040C: ('UV', '1', "File Offset in Container", '', 'FileOffsetInContainer'),
    0x0008040D: ('UV', '1', "File Length in Container", '', 'FileLengthInContainer'),
    0x0008040E: ('UI', '1', "Stored Instance Transfer Syntax UID", '', 'StoredInstanceTransferSyntaxUID'),
    0x0008040F: ('CS', '1-n', "Extended Matching Mechanisms", '', 'ExtendedMatchingMechanisms'),
    0x00080410: ('SQ', '1', "Range Matching Sequence", '', 'RangeMatchingSequence'),
    0x00080411: ('SQ', '1', "List of UID Matching Sequence", '', 'ListOfUIDMatchingSequence'),
    0x00080412: ('SQ', '1', "Empty Value Matching Sequence", '', 'EmptyValueMatchingSequence'),
    0x00080413: ('SQ', '1', "General Matching Sequence", '', 'GeneralMatchingSequence'),
    0x00080414: ('US', '1', "Requested Status Interval", '', 'RequestedStatusInterval'),
    0x00080415: ('CS', '1', "Retain Instances", '', 'RetainInstances'),
    0x00080416: ('DT', '1', "Expiration DateTime", '', 'ExpirationDateTime'),
    0x00080417: ('CS', '1', "Transaction Status", '', 'TransactionStatus'),
    0x00080418: ('LT', '1', "Transaction Status Comment", '', 'TransactionStatusComment'),
    0x00080419: ('SQ', '1', "File Set Access Sequence", '', 'FileSetAccessSequence'),
    0x0008041A: ('SQ', '1', "File Access Sequence", '', 'FileAccessSequence'),
    0x0008041B: ('OB', '1', "Record Key", '', 'RecordKey'),
    0x0008041C: ('OB', '1', "Prior Record Key", '', 'PriorRecordKey'),
    0x0008041D: ('SQ', '1', "Metadata Sequence", '', 'MetadataSequence'),
    0x0008041E: ('SQ', '1', "Updated Metadata Sequence", '', 'UpdatedMetadataSequence'),
    0x0008041F: ('DT', '1', "Study Update DateTime", '', 'StudyUpdateDateTime'),
    0x00080420: ('SQ', '1', "Inventory Access End Points Sequence", '', 'InventoryAccessEndPointsSequence'),
    0x00080421: ('SQ', '1', "Study Access End Points Sequence", '', 'StudyAccessEndPointsSequence'),
    0x00080422: ('SQ', '1', "Incorporated Inventory Instance Sequence", '', 'IncorporatedInventoryInstanceSequence'),
    0x00080423: ('SQ', '1', "Inventoried Studies Sequence", '', 'InventoriedStudiesSequence'),
    0x00080424: ('SQ', '1', "Inventoried Series Sequence", '', 'InventoriedSeriesSequence'),
    0x00080425: ('SQ', '1', "Inventoried Instances Sequence", '', 'InventoriedInstancesSequence'),
    0x00080426: ('CS', '1', "Inventory Completion Status", '', 'InventoryCompletionStatus'),
    0x00080427: ('UL', '1', "Number of Study Records in Instance", '', 'NumberOfStudyRecordsInInstance'),
    0x00080428: ('UV', '1', "Total Number of Study Records", '', 'TotalNumberOfStudyRecords'),
    0x00080429: ('UV', '1', "Maximum Number of Records", '', 'MaximumNumberOfRecords'),
    0x00081000: ('AE', '1', "Network ID", 'Retired', 'NetworkID'),
    0x00081010: ('SH', '1', "Station Name", '', 'StationName'),
    0x00081030: ('LO', '1', "Study Description", '', 'StudyDescription'),
    0x00081032: ('SQ', '1', "Procedure Code Sequence", '', 'ProcedureCodeSequence'),
    0x0008103E: ('LO', '1', "Series Description", '', 'SeriesDescription'),
    0x0008103F: ('SQ', '1', "Series Description Code Sequence", '', 'SeriesDescriptionCodeSequence'),
    0x00081040: ('LO', '1', "Institutional Department Name", '', 'InstitutionalDepartmentName'),
    0x00081041: ('SQ', '1', "Institutional Department Type Code Sequence", '', 'InstitutionalDepartmentTypeCodeSequence'),
    0x00081048: ('PN', '1-n', "Physician(s) of Record", '', 'PhysiciansOfRecord'),
    0x00081049: ('SQ', '1', "Physician(s) of Record Identification Sequence", '', 'PhysiciansOfRecordIdentificationSequence'),
    0x00081050: ('PN', '1-n', "Performing Physician's Name", '', 'PerformingPhysicianName'),
    0x00081052: ('SQ', '1', "Performing Physician Identification Sequence", '', 'PerformingPhysicianIdentificationSequence'),
    0x00081060: ('PN', '1-n', "Name of Physician(s) Reading Study", '', 'NameOfPhysiciansReadingStudy'),
    0x00081062: ('SQ', '1', "Physician(s) Reading Study Identification Sequence", '', 'PhysiciansReadingStudyIdentificationSequence'),
    0x00081070: ('PN', '1-n', "Operators' Name", '', 'OperatorsName'),
    0x00081072: ('SQ', '1', "Operator Identification Sequence", '', 'OperatorIdentificationSequence'),
    0x00081080: ('LO', '1-n', "Admitting Diagnoses Description", '', 'AdmittingDiagnosesDescription'),
    0x00081084: ('SQ', '1', "Admitting Diagnoses Code Sequence", '', 'AdmittingDiagnosesCodeSequence'),
    0x00081088: ('LO', '1', "Pyramid Description", '', 'PyramidDescription'),
    0x00081090: ('LO', '1', "Manufacturer's Model Name", '', 'ManufacturerModelName'),
    0x00081100: ('SQ', '1', "Referenced Results Sequence", 'Retired', 'ReferencedResultsSequence'),
    0x00081110: ('SQ', '1', "Referenced Study Sequence", '', 'ReferencedStudySequence'),
    0x00081111: ('SQ', '1', "Referenced Performed Procedure Step Sequence", '', 'ReferencedPerformedProcedureStepSequence'),
    0x00081112: ('SQ', '1', "Referenced Instances by SOP Class Sequence", '', 'ReferencedInstancesBySOPClassSequence'),
    0x00081115: ('SQ', '1', "Referenced Series Sequence", '', 'ReferencedSeriesSequence'),
    0x00081120: ('SQ', '1', "Referenced Patient Sequence", '', 'ReferencedPatientSequence'),
    0x00081125: ('SQ', '1', "Referenced Visit Sequence", '', 'ReferencedVisitSequence'),
    0x00081130: ('SQ', '1', "Referenced Overlay Sequence", 'Retired', 'ReferencedOverlaySequence'),
    0x00081134: ('SQ', '1', "Referenced Stereometric Instance Sequence", '', 'ReferencedStereometricInstanceSequence'),
    0x0008113A: ('SQ', '1', "Referenced Waveform Sequence", '', 'ReferencedWaveformSequence'),
    0x00081140: ('SQ', '1', "Referenced Image Sequence", '', 'ReferencedImageSequence'),
    0x00081145: ('SQ', '1', "Referenced Curve Sequence", 'Retired', 'ReferencedCurveSequence'),
    0x0008114A: ('SQ', '1', "Referenced Instance Sequence", '', 'ReferencedInstanceSequence'),
    0x0008114B: ('SQ', '1', "Referenced Real World Value Mapping Instance Sequence", '', 'ReferencedRealWorldValueMappingInstanceSequence'),
    0x0008114C: ('SQ', '1', "Referenced Segmentation Sequence", '', 'ReferencedSegmentationSequence'),
    0x00081150: ('UI', '1', "Referenced SOP Class UID", '', 'ReferencedSOPClassUID'),
    0x00081155: ('UI', '1', "Referenced SOP Instance UID", '', 'ReferencedSOPInstanceUID'),
    0x00081156: ('SQ', '1', "Definition Source Sequence", '', 'DefinitionSourceSequence'),
    0x0008115A: ('UI', '1-n', "SOP Classes Supported", '', 'SOPClassesSupported'),
    0x00081160: ('IS', '1-n', "Referenced Frame Number", '', 'ReferencedFrameNumber'),
    0x00081161: ('UL', '1-n', "Simple Frame List", '', 'SimpleFrameList'),
    0x00081162: ('UL', '3-3n', "Calculated Frame List", '', 'CalculatedFrameList'),
    0x00081163: ('FD', '2', "Time Range", '', 'TimeRange'),
    0x00081164: ('SQ', '1', "Frame Extraction Sequence", '', 'FrameExtractionSequence'),
    0x00081167: ('UI', '1', "Multi-frame Source SOP Instance UID", '', 'MultiFrameSourceSOPInstanceUID'),
    0x00081190: ('UR', '1', "Retrieve URL", '', 'RetrieveURL'),
    0x00081195: ('UI', '1', "Transaction UID", '', 'TransactionUID'),
    0x00081196: ('US', '1', "Warning Reason", '', 'WarningReason'),
    0x00081197: ('US', '1', "Failure Reason", '', 'FailureReason'),
    0x00081198: ('SQ', '1', "Failed SOP Sequence", '', 'FailedSOPSequence'),
    0x00081199: ('SQ', '1', "Referenced SOP Sequence", '', 'ReferencedSOPSequence'),
    0x0008119A: ('SQ', '1', "Other Failures Sequence", '', 'OtherFailuresSequence'),
    0x0008119B: ('SQ', '1', "Failed Study Sequence", '', 'FailedStudySequence'),
    0x00081200: ('SQ', '1', "Studies Containing Other Referenced Instances Sequence", '', 'StudiesContainingOtherReferencedInstancesSequence'),
    0x00081250: ('SQ', '1', "Related Series Sequence", '', 'RelatedSeriesSequence'),
    0x00082110: ('CS', '1', "Lossy Image Compression (Retired)", 'Retired', 'LossyImageCompressionRetired'),
    0x00082111: ('ST', '1', "Derivation Description", '', 'DerivationDescription'),
    0x00082112: ('SQ', '1', "Source Image Sequence", '', 'SourceImageSequence'),
    0x00082120: ('SH', '1', "Stage Name", '', 'StageName'),
    0x00082122: ('IS', '1', "Stage Number", '', 'StageNumber'),
    0x00082124: ('IS', '1', "Number of Stages", '', 'NumberOfStages'),
    0x00082127: ('SH', '1', "View Name", '', 'ViewName'),
    0x00082128: ('IS', '1', "View Number", '', 'ViewNumber'),
    0x00082129: ('IS', '1', "Number of Event Timers", '', 'NumberOfEventTimers'),
    0x0008212A: ('IS', '1', "Number of Views in Stage", '', 'NumberOfViewsInStage'),
    0x00082130: ('DS', '1-n', "Event Elapsed Time(s)", '', 'EventElapsedTimes'),
    0x00082132: ('LO', '1-n', "Event Timer Name(s)", '', 'EventTimerNames'),
    0x00082133: ('SQ', '1', "Event Timer Sequence", '', 'EventTimerSequence'),
    0x00082134: ('FD', '1', "Event Time Offset", '', 'EventTimeOffset'),
    0x00082135: ('SQ', '1', "Event Code Sequence", '', 'EventCodeSequence'),
    0x00082142: ('IS', '1', "Start Trim", '', 'StartTrim'),
    0x00082143: ('IS', '1', "Stop Trim", '', 'StopTrim'),
    0x00082144: ('IS', '1', "Recommended Display Frame Rate", '', 'RecommendedDisplayFrameRate'),
    0x00082200: ('CS', '1', "Transducer Position", 'Retired', 'TransducerPosition'),
    0x00082204: ('CS', '1', "Transducer Orientation", 'Retired', 'TransducerOrientation'),
    0x00082208: ('CS', '1', "Anatomic Structure", 'Retired', 'AnatomicStructure'),
    0x00082218: ('SQ', '1', "Anatomic Region Sequence", '', 'AnatomicRegionSequence'),
    0x00082220: ('SQ', '1', "Anatomic Region Modifier Sequence", '', 'AnatomicRegionModifierSequence'),
    0x00082228: ('SQ', '1', "Primary Anatomic Structure Sequence", '', 'PrimaryAnatomicStructureSequence'),
    0x00082229: ('SQ', '1', "Anatomic Structure, Space or Region Sequence", 'Retired', 'AnatomicStructureSpaceOrRegionSequence'),
    0x00082230: ('SQ', '1', "Primary Anatomic Structure Modifier Sequence", '', 'PrimaryAnatomicStructureModifierSequence'),
    0x00082240: ('SQ', '1', "Transducer Position Sequence", 'Retired', 'TransducerPositionSequence'),
    0x00082242: ('SQ', '1', "Transducer Position Modifier Sequence", 'Retired', 'TransducerPositionModifierSequence'),
    0x00082244: ('SQ', '1', "Transducer Orientation Sequence", 'Retired', 'TransducerOrientationSequence'),
    0x00082246: ('SQ', '1', "Transducer Orientation Modifier Sequence", 'Retired', 'TransducerOrientationModifierSequence'),
    0x00082251: ('SQ', '1', "Anatomic Structure Space Or Region Code Sequence (Trial)", 'Retired', 'AnatomicStructureSpaceOrRegionCodeSequenceTrial'),
    0x00082253: ('SQ', '1', "Anatomic Portal Of Entrance Code Sequence (Trial)", 'Retired', 'AnatomicPortalOfEntranceCodeSequenceTrial'),
    0x00082255: ('SQ', '1', "Anatomic Approach Direction Code Sequence (Trial)", 'Retired', 'AnatomicApproachDirectionCodeSequenceTrial'),
    0x00082256: ('ST', '1', "Anatomic Perspective Description (Trial)", 'Retired', 'AnatomicPerspectiveDescriptionTrial'),
    0x00082257: ('SQ', '1', "Anatomic Perspective Code Sequence (Trial)", 'Retired', 'AnatomicPerspectiveCodeSequenceTrial'),
    0x00082258: ('ST', '1', "Anatomic Location Of Examining Instrument Description (Trial)", 'Retired', 'AnatomicLocationOfExaminingInstrumentDescriptionTrial'),
    0x00082259: ('SQ', '1', "Anatomic Location Of Examining Instrument Code Sequence (Trial)", 'Retired', 'AnatomicLocationOfExaminingInstrumentCodeSequenceTrial'),
    0x0008225A: ('SQ', '1', "Anatomic Structure Space Or Region Modifier Code Sequence (Trial)", 'Retired', 'AnatomicStructureSpaceOrRegionModifierCodeSequenceTrial'),
    0x0008225C: ('SQ', '1', "On Axis Background Anatomic Structure Code Sequence (Trial)", 'Retired', 'OnAxisBackgroundAnatomicStructureCodeSequenceTrial'),
    0x00083001: ('SQ', '1', "Alternate Representation Sequence", '', 'AlternateRepresentationSequence'),
    0x00083002: ('UI', '1-n', "Available Transfer Syntax UID", '', 'AvailableTransferSyntaxUID'),
    0x00083010: ('UI', '1-n', "Irradiation Event UID", '', 'IrradiationEventUID'),
    0x00083011: ('SQ', '1', "Source Irradiation Event Sequence", '', 'SourceIrradiationEventSequence'),
    0x00083012: ('UI', '1', "Radiopharmaceutical Administration Event UID", '', 'RadiopharmaceuticalAdministrationEventUID'),
    0x00084000: ('LT', '1', "Identifying Comments", 'Retired', 'IdentifyingComments'),
    0x00089007: ('CS', '4-5', "Frame Type", '', 'FrameType'),
    0x00089092: ('SQ', '1', "Referenced Image Evidence Sequence", '', 'ReferencedImageEvidenceSequence'),
    0x00089121: ('SQ', '1', "Referenced Raw Data Sequence", '', 'ReferencedRawDataSequence'),
    0x00089123: ('UI', '1', "Creator-Version UID", '', 'CreatorVersionUID'),
    0x00089124: ('SQ', '1', "Derivation Image Sequence", '', 'DerivationImageSequence'),
    0x00089154: ('SQ', '1', "Source Image Evidence Sequence", '', 'SourceImageEvidenceSequence'),
    0x00089205: ('CS', '1', "Pixel Presentation", '', 'PixelPresentation'),
    0x00089206: ('CS', '1', "Volumetric Properties", '', 'VolumetricProperties'),
    0x00089207: ('CS', '1', "Volume Based Calculation Technique", '', 'VolumeBasedCalculationTechnique'),
    0x00089208: ('CS', '1', "Complex Image Component", '', 'ComplexImageComponent'),
    0x00089209: ('CS', '1', "Acquisition Contrast", '', 'AcquisitionContrast'),
    0x00089215: ('SQ', '1', "Derivation Code Sequence", '', 'DerivationCodeSequence'),
    0x00089237: ('SQ', '1', "Referenced Presentation State Sequence", '', 'ReferencedPresentationStateSequence'),
    0x00089410: ('SQ', '1', "Referenced Other Plane Sequence", '', 'ReferencedOtherPlaneSequence'),
    0x00089458: ('SQ', '1', "Frame Display Sequence", '', 'FrameDisplaySequence'),
    0x00089459: ('FL', '1', "Recommended Display Frame Rate in Float", '', 'RecommendedDisplayFrameRateInFloat'),
    0x00089460: ('CS', '1', "Skip Frame Range Flag", '', 'SkipFrameRangeFlag'),
    0x00100010: ('PN', '1', "Patient's Name", '', 'PatientName'),
    0x00100020: ('LO', '1', "Patient ID", '', 'PatientID'),
    0x00100021: ('LO', '1', "Issuer of Patient ID", '', 'IssuerOfPatientID'),
    0x00100022: ('CS', '1', "Type of Patient ID", '', 'TypeOfPatientID'),
    0x00100024: ('SQ', '1', "Issuer of Patient ID Qualifiers Sequence", '', 'IssuerOfPatientIDQualifiersSequence'),
    0x00100026: ('SQ', '1', "Source Patient Group Identification Sequence", '', 'SourcePatientGroupIdentificationSequence'),
    0x00100027: ('SQ', '1', "Group of Patients Identification Sequence", '', 'GroupOfPatientsIdentificationSequence'),
    0x00100028: ('US', '3', "Subject Relative Position in Image", '', 'SubjectRelativePositionInImage'),
    0x00100030: ('DA', '1', "Patient's Birth Date", '', 'PatientBirthDate'),
    0x00100032: ('TM', '1', "Patient's Birth Time", '', 'PatientBirthTime'),
    0x00100033: ('LO', '1', "Patient's Birth Date in Alternative Calendar", '', 'PatientBirthDateInAlternativeCalendar'),
    0x00100034: ('LO', '1', "Patient's Death Date in Alternative Calendar", '', 'PatientDeathDateInAlternativeCalendar'),
    0x00100035: ('CS', '1', "Patient's Alternative Calendar", '', 'PatientAlternativeCalendar'),
    0x00100040: ('CS', '1', "Patient's Sex", '', 'PatientSex'),
    0x00100050: ('SQ', '1', "Patient's Insurance Plan Code Sequence", '', 'PatientInsurancePlanCodeSequence'),
    0x00100101: ('SQ', '1', "Patient's Primary Language Code Sequence", '', 'PatientPrimaryLanguageCodeSequence'),
    0x00100102: ('SQ', '1', "Patient's Primary Language Modifier Code Sequence", '', 'PatientPrimaryLanguageModifierCodeSequence'),
    0x00100200: ('CS', '1', "Quality Control Subject", '', 'QualityControlSubject'),
    0x00100201: ('SQ', '1', "Quality Control Subject Type Code Sequence", '', 'QualityControlSubjectTypeCodeSequence'),
    0x00100212: ('UC', '1', "Strain Description", '', 'StrainDescription'),
    0x00100213: ('LO', '1', "Strain Nomenclature", '', 'StrainNomenclature'),
    0x00100214: ('LO', '1', "Strain Stock Number", '', 'StrainStockNumber'),
    0x00100215: ('SQ', '1', "Strain Source Registry Code Sequence", '', 'StrainSourceRegistryCodeSequence'),
    0x00100216: ('SQ', '1', "Strain Stock Sequence", '', 'StrainStockSequence'),
    0x00100217: ('LO', '1', "Strain Source", '', 'StrainSource'),
    0x00100218: ('UT', '1', "Strain Additional Information", '', 'StrainAdditionalInformation'),
    0x00100219: ('SQ', '1', "Strain Code Sequence", '', 'StrainCodeSequence'),
    0x00100221: ('SQ', '1', "Genetic Modifications Sequence", '', 'GeneticModificationsSequence'),
    0x00100222: ('UC', '1', "Genetic Modifications Description", '', 'GeneticModificationsDescription'),
    0x00100223: ('LO', '1', "Genetic Modifications Nomenclature", '', 'GeneticModificationsNomenclature'),
    0x00100229: ('SQ', '1', "Genetic Modifications Code Sequence", '', 'GeneticModificationsCodeSequence'),
    0x00101000: ('LO', '1-n', "Other Patient IDs", 'Retired', 'OtherPatientIDs'),
    0x00101001: ('PN', '1-n', "Other Patient Names", '', 'OtherPatientNames'),
    0x00101002: ('SQ', '1', "Other Patient IDs Sequence", '', 'OtherPatientIDsSequence'),
    0x00101005: ('PN', '1', "Patient's Birth Name", '', 'PatientBirthName'),
    0x00101010: ('AS', '1', "Patient's Age", '', 'PatientAge'),
    0x00101020: ('DS', '1', "Patient's Size", '', 'PatientSize'),
    0x00101021: ('SQ', '1', "Patient's Size Code Sequence", '', 'PatientSizeCodeSequence'),
    0x00101022: ('DS', '1', "Patient's Body Mass Index", '', 'PatientBodyMassIndex'),
    0x00101023: ('DS', '1', "Measured AP Dimension", '', 'MeasuredAPDimension'),
    0x00101024: ('DS', '1', "Measured Lateral Dimension", '', 'MeasuredLateralDimension'),
    0x00101030: ('DS', '1', "Patient's Weight", '', 'PatientWeight'),
    0x00101040: ('LO', '1', "Patient's Address", '', 'PatientAddress'),
    0x00101050: ('LO', '1-n', "Insurance Plan Identification", 'Retired', 'InsurancePlanIdentification'),
    0x00101060: ('PN', '1', "Patient's Mother's Birth Name", '', 'PatientMotherBirthName'),
    0x00101080: ('LO', '1', "Military Rank", '', 'MilitaryRank'),
    0x00101081: ('LO', '1', "Branch of Service", '', 'BranchOfService'),
    0x00101090: ('LO', '1', "Medical Record Locator", 'Retired', 'MedicalRecordLocator'),
    0x00101100: ('SQ', '1', "Referenced Patient Photo Sequence", '', 'ReferencedPatientPhotoSequence'),
    0x00102000: ('LO', '1-n', "Medical Alerts", '', 'MedicalAlerts'),
    0x00102110: ('LO', '1-n', "Allergies", '', 'Allergies'),
    0x00102150: ('LO', '1', "Country of Residence", '', 'CountryOfResidence'),
    0x00102152: ('LO', '1', "Region of Residence", '', 'RegionOfResidence'),
    0x00102154: ('SH', '1-n', "Patient's Telephone Numbers", '', 'PatientTelephoneNumbers'),
    0x00102155: ('LT', '1', "Patient's Telecom Information", '', 'PatientTelecomInformation'),
    0x00102160: ('SH', '1', "Ethnic Group", '', 'EthnicGroup'),
    0x00102161: ('SQ', '1', "Ethnic Group Code Sequence", '', 'EthnicGroupCodeSequence'),
    0x00102180: ('SH', '1', "Occupation", '', 'Occupation'),
    0x001021A0: ('CS', '1', "Smoking Status", '', 'SmokingStatus'),
    0x001021B0: ('LT', '1', "Additional Patient History", '', 'AdditionalPatientHistory'),
    0x001021C0: ('US', '1', "Pregnancy Status", '', 'PregnancyStatus'),
    0x001021D0: ('DA', '1', "Last Menstrual Date", '', 'LastMenstrualDate'),
    0x001021F0: ('LO', '1', "Patient's Religious Preference", '', 'PatientReligiousPreference'),
    0x00102201: ('LO', '1', "Patient Species Description", '', 'PatientSpeciesDescription'),
    0x00102202: ('SQ', '1', "Patient Species Code Sequence", '', 'PatientSpeciesCodeSequence'),
    0x00102203: ('CS', '1', "Patient's Sex Neutered", '', 'PatientSexNeutered'),
    0x00102210: ('CS', '1', "Anatomical Orientation Type", '', 'AnatomicalOrientationType'),
    0x00102292: ('LO', '1', "Patient Breed Description", '', 'PatientBreedDescription'),
    0x00102293: ('SQ', '1', "Patient Breed Code Sequence", '', 'PatientBreedCodeSequence'),
    0x00102294: ('SQ', '1', "Breed Registration Sequence", '', 'BreedRegistrationSequence'),
    0x00102295: ('LO', '1', "Breed Registration Number", '', 'BreedRegistrationNumber'),
    0x00102296: ('SQ', '1', "Breed Registry Code Sequence", '', 'BreedRegistryCodeSequence'),
    0x00102297: ('PN', '1', "Responsible Person", '', 'ResponsiblePerson'),
    0x00102298: ('CS', '1', "Responsible Person Role", '', 'ResponsiblePersonRole'),
    0x00102299: ('LO', '1', "Responsible Organization", '', 'ResponsibleOrganization'),
    0x00104000: ('LT', '1', "Patient Comments", '', 'PatientComments'),
    0x00109431: ('FL', '1', "Examined Body Thickness", '', 'ExaminedBodyThickness'),
    0x00120010: ('LO', '1', "Clinical Trial Sponsor Name", '', 'ClinicalTrialSponsorName'),
    0x00120020: ('LO', '1', "Clinical Trial Protocol ID", '', 'ClinicalTrialProtocolID'),
    0x00120021: ('LO', '1', "Clinical Trial Protocol Name", '', 'ClinicalTrialProtocolName'),
    0x00120022: ('LO', '1', "Issuer of Clinical Trial Protocol ID", '', 'IssuerOfClinicalTrialProtocolID'),
    0x00120023: ('SQ', '1', "Other Clinical Trial Protocol IDs Sequence", '', 'OtherClinicalTrialProtocolIDsSequence'),
    0x00120030: ('LO', '1', "Clinical Trial Site ID", '', 'ClinicalTrialSiteID'),
    0x00120031: ('LO', '1', "Clinical Trial Site Name", '', 'ClinicalTrialSiteName'),
    0x00120032: ('LO', '1', "Issuer of Clinical Trial Site ID", '', 'IssuerOfClinicalTrialSiteID'),
    0x00120040: ('LO', '1', "Clinical Trial Subject ID", '', 'ClinicalTrialSubjectID'),
    0x00120041: ('LO', '1', "Issuer of Clinical Trial Subject ID", '', 'IssuerOfClinicalTrialSubjectID'),
    0x00120042: ('LO', '1', "Clinical Trial Subject Reading ID", '', 'ClinicalTrialSubjectReadingID'),
    0x00120043: ('LO', '1', "Issuer of Clinical Trial Subject Reading ID", '', 'IssuerOfClinicalTrialSubjectReadingID'),
    0x00120050: ('LO', '1', "Clinical Trial Time Point ID", '', 'ClinicalTrialTimePointID'),
    0x00120051: ('ST', '1', "Clinical Trial Time Point Description", '', 'ClinicalTrialTimePointDescription'),
    0x00120052: ('FD', '1', "Longitudinal Temporal Offset from Event", '', 'LongitudinalTemporalOffsetFromEvent'),
    0x00120053: ('CS', '1', "Longitudinal Temporal Event Type", '', 'LongitudinalTemporalEventType'),
    0x00120054: ('SQ', '1', "Clinical Trial Time Point Type Code Sequence", '', 'ClinicalTrialTimePointTypeCodeSequence'),
    0x00120055: ('LO', '1', "Issuer of Clinical Trial Time Point ID", '', 'IssuerOfClinicalTrialTimePointID'),
    0x00120060: ('LO', '1', "Clinical Trial Coordinating Center Name", '', 'ClinicalTrialCoordinatingCenterName'),
    0x00120062: ('CS', '1', "Patient Identity Removed", '', 'PatientIdentityRemoved'),
    0x00120063: ('LO', '1-n', "De-identification Method", '', 'DeidentificationMethod'),
    0x00120064: ('SQ', '1', "De-identification Method Code Sequence", '', 'DeidentificationMethodCodeSequence'),
    0x00120071: ('LO', '1', "Clinical Trial Series ID", '', 'ClinicalTrialSeriesID'),
    0x00120072: ('LO', '1', "Clinical Trial Series Description", '', 'ClinicalTrialSeriesDescription'),
    0x00120073: ('LO', '1', "Issuer of Clinical Trial Series ID", '', 'IssuerOfClinicalTrialSeriesID'),
    0x00120081: ('LO', '1', "Clinical Trial Protocol Ethics Committee Name", '', 'ClinicalTrialProtocolEthicsCommitteeName'),
    0x00120082: ('LO', '1', "Clinical Trial Protocol Ethics Committee Approval Number", '', 'ClinicalTrialProtocolEthicsCommitteeApprovalNumber'),
    0x00120083: ('SQ', '1', "Consent for Clinical Trial Use Sequence", '', 'ConsentForClinicalTrialUseSequence'),
    0x00120084: ('CS', '1', "Distribution Type", '', 'DistributionType'),
    0x00120085: ('CS', '1', "Consent for Distribution Flag", '', 'ConsentForDistributionFlag'),
    0x00120086: ('DA', '1', "Ethics Committee Approval Effectiveness Start Date", '', 'EthicsCommitteeApprovalEffectivenessStartDate'),
    0x00120087: ('DA', '1', "Ethics Committee Approval Effectiveness End Date", '', 'EthicsCommitteeApprovalEffectivenessEndDate'),
    0x00140023: ('ST', '1', "CAD File Format", 'Retired', 'CADFileFormat'),
    0x00140024: ('ST', '1', "Component Reference System", 'Retired', 'ComponentReferenceSystem'),
    0x00140025: ('ST', '1', "Component Manufacturing Procedure", '', 'ComponentManufacturingProcedure'),
    0x00140028: ('ST', '1', "Component Manufacturer", '', 'ComponentManufacturer'),
    0x00140030: ('DS', '1-n', "Material Thickness", '', 'MaterialThickness'),
    0x00140032: ('DS', '1-n', "Material Pipe Diameter", '', 'MaterialPipeDiameter'),
    0x00140034: ('DS', '1-n', "Material Isolation Diameter", '', 'MaterialIsolationDiameter'),
    0x00140042: ('ST', '1', "Material Grade", '', 'MaterialGrade'),
    0x00140044: ('ST', '1', "Material Properties Description", '', 'MaterialPropertiesDescription'),
    0x00140045: ('ST', '1', "Material Properties File Format (Retired)", 'Retired', 'MaterialPropertiesFileFormatRetired'),
    0x00140046: ('LT', '1', "Material Notes", '', 'MaterialNotes'),
    0x00140050: ('CS', '1', "Component Shape", '', 'ComponentShape'),
    0x00140052: ('CS', '1', "Curvature Type", '', 'CurvatureType'),
    0x00140054: ('DS', '1', "Outer Diameter", '', 'OuterDiameter'),
    0x00140056: ('DS', '1', "Inner Diameter", '', 'InnerDiameter'),
    0x00140100: ('LO', '1-n', "Component Welder IDs", '', 'ComponentWelderIDs'),
    0x00140101: ('CS', '1', "Secondary Approval Status", '', 'SecondaryApprovalStatus'),
    0x00140102: ('DA', '1', "Secondary Review Date", '', 'SecondaryReviewDate'),
    0x00140103: ('TM', '1', "Secondary Review Time", '', 'SecondaryReviewTime'),
    0x00140104: ('PN', '1', "Secondary Reviewer Name", '', 'SecondaryReviewerName'),
    0x00140105: ('ST', '1', "Repair ID", '', 'RepairID'),
    0x00140106: ('SQ', '1', "Multiple Component Approval Sequence", '', 'MultipleComponentApprovalSequence'),
    0x00140107: ('CS', '1-n', "Other Approval Status", '', 'OtherApprovalStatus'),
    0x00140108: ('CS', '1-n', "Other Secondary Approval Status", '', 'OtherSecondaryApprovalStatus'),
    0x00140200: ('SQ', '1', "Data Element Label Sequence", '', 'DataElementLabelSequence'),
    0x00140201: ('SQ', '1', "Data Element Label Item Sequence", '', 'DataElementLabelItemSequence'),
    0x00140202: ('AT', '1', "Data Element", '', 'DataElement'),
    0x00140203: ('LO', '1', "Data Element Name", '', 'DataElementName'),
    0x00140204: ('LO', '1', "Data Element Description", '', 'DataElementDescription'),
    0x00140205: ('CS', '1', "Data Element Conditionality", '', 'DataElementConditionality'),
    0x00140206: ('IS', '1', "Data Element Minimum Characters", '', 'DataElementMinimumCharacters'),
    0x00140207: ('IS', '1', "Data Element Maximum Characters", '', 'DataElementMaximumCharacters'),
    0x00141010: ('ST', '1', "Actual Environmental Conditions", '', 'ActualEnvironmentalConditions'),
    0x00141020: ('DA', '1', "Expiry Date", '', 'ExpiryDate'),
    0x00141040: ('ST', '1', "Environmental Conditions", '', 'EnvironmentalConditions'),
    0x00142002: ('SQ', '1', "Evaluator Sequence", '', 'EvaluatorSequence'),
    0x00142004: ('IS', '1', "Evaluator Number", '', 'EvaluatorNumber'),
    0x00142006: ('PN', '1', "Evaluator Name", '', 'EvaluatorName'),
    0x00142008: ('IS', '1', "Evaluation Attempt", '', 'EvaluationAttempt'),
    0x00142012: ('SQ', '1', "Indication Sequence", '', 'IndicationSequence'),
    0x00142014: ('IS', '1', "Indication Number", '', 'IndicationNumber'),
    0x00142016: ('SH', '1', "Indication Label", '', 'IndicationLabel'),
    0x00142018: ('ST', '1', "Indication Description", '', 'IndicationDescription'),
    0x0014201A: ('CS', '1-n', "Indication Type", '', 'IndicationType'),
    0x0014201C: ('CS', '1', "Indication Disposition", '', 'IndicationDisposition'),
    0x0014201E: ('SQ', '1', "Indication ROI Sequence", '', 'IndicationROISequence'),
    0x00142030: ('SQ', '1', "Indication Physical Property Sequence", '', 'IndicationPhysicalPropertySequence'),
    0x00142032: ('SH', '1', "Property Label", '', 'PropertyLabel'),
    0x00142202: ('IS', '1', "Coordinate System Number of Axes", '', 'CoordinateSystemNumberOfAxes'),
    0x00142204: ('SQ', '1', "Coordinate System Axes Sequence", '', 'CoordinateSystemAxesSequence'),
    0x00142206: ('ST', '1', "Coordinate System Axis Description", '', 'CoordinateSystemAxisDescription'),
    0x00142208: ('CS', '1', "Coordinate System Data Set Mapping", '', 'CoordinateSystemDataSetMapping'),
    0x0014220A: ('IS', '1', "Coordinate System Axis Number", '', 'CoordinateSystemAxisNumber'),
    0x0014220C: ('CS', '1', "Coordinate System Axis Type", '', 'CoordinateSystemAxisType'),
    0x0014220E: ('CS', '1', "Coordinate System Axis Units", '', 'CoordinateSystemAxisUnits'),
    0x00142210: ('OB', '1', "Coordinate System Axis Values", '', 'CoordinateSystemAxisValues'),
    0x00142220: ('SQ', '1', "Coordinate System Transform Sequence", '', 'CoordinateSystemTransformSequence'),
    0x00142222: ('ST', '1', "Transform Description", '', 'TransformDescription'),
    0x00142224: ('IS', '1', "Transform Number of Axes", '', 'TransformNumberOfAxes'),
    0x00142226: ('IS', '1-n', "Transform Order of Axes", '', 'TransformOrderOfAxes'),
    0x00142228: ('CS', '1', "Transformed Axis Units", '', 'TransformedAxisUnits'),
    0x0014222A: ('DS', '1-n', "Coordinate System Transform Rotation and Scale Matrix", '', 'CoordinateSystemTransformRotationAndScaleMatrix'),
    0x0014222C: ('DS', '1-n', "Coordinate System Transform Translation Matrix", '', 'CoordinateSystemTransformTranslationMatrix'),
    0x00143011: ('DS', '1', "Internal Detector Frame Time", '', 'InternalDetectorFrameTime'),
    0x00143012: ('DS', '1', "Number of Frames Integrated", '', 'NumberOfFramesIntegrated'),
    0x00143020: ('SQ', '1', "Detector Temperature Sequence", '', 'DetectorTemperatureSequence'),
    0x00143022: ('ST', '1', "Sensor Name", '', 'SensorName'),
    0x00143024: ('DS', '1', "Horizontal Offset of Sensor", '', 'HorizontalOffsetOfSensor'),
    0x00143026: ('DS', '1', "Vertical Offset of Sensor", '', 'VerticalOffsetOfSensor'),
    0x00143028: ('DS', '1', "Sensor Temperature", '', 'SensorTemperature'),
    0x00143040: ('SQ', '1', "Dark Current Sequence", '', 'DarkCurrentSequence'),
    0x00143050: ('OB or OW', '1', "Dark Current Counts", '', 'DarkCurrentCounts'),
    0x00143060: ('SQ', '1', "Gain Correction Reference Sequence", '', 'GainCorrectionReferenceSequence'),
    0x00143070: ('OB or OW', '1', "Air Counts", '', 'AirCounts'),
    0x00143071: ('DS', '1', "KV Used in Gain Calibration", '', 'KVUsedInGainCalibration'),
    0x00143072: ('DS', '1', "MA Used in Gain Calibration", '', 'MAUsedInGainCalibration'),
    0x00143073: ('DS', '1', "Number of Frames Used for Integration", '', 'NumberOfFramesUsedForIntegration'),
    0x00143074: ('LO', '1', "Filter Material Used in Gain Calibration", '', 'FilterMaterialUsedInGainCalibration'),
    0x00143075: ('DS', '1', "Filter Thickness Used in Gain Calibration", '', 'FilterThicknessUsedInGainCalibration'),
    0x00143076: ('DA', '1', "Date of Gain Calibration", '', 'DateOfGainCalibration'),
    0x00143077: ('TM', '1', "Time of Gain Calibration", '', 'TimeOfGainCalibration'),
    0x00143080: ('OB', '1', "Bad Pixel Image", '', 'BadPixelImage'),
    0x00143099: ('LT', '1', "Calibration Notes", '', 'CalibrationNotes'),
    0x00143100: ('LT', '1', "Linearity Correction Technique", '', 'LinearityCorrectionTechnique'),
    0x00143101: ('LT', '1', "Beam Hardening Correction Technique", '', 'BeamHardeningCorrectionTechnique'),
    0x00144002: ('SQ', '1', "Pulser Equipment Sequence", '', 'PulserEquipmentSequence'),
    0x00144004: ('CS', '1', "Pulser Type", '', 'PulserType'),
    0x00144006: ('LT', '1', "Pulser Notes", '', 'PulserNotes'),
    0x00144008: ('SQ', '1', "Receiver Equipment Sequence", '', 'ReceiverEquipmentSequence'),
    0x0014400A: ('CS', '1', "Amplifier Type", '', 'AmplifierType'),
    0x0014400C: ('LT', '1', "Receiver Notes", '', 'ReceiverNotes'),
    0x0014400E: ('SQ', '1', "Pre-Amplifier Equipment Sequence", '', 'PreAmplifierEquipmentSequence'),
    0x0014400F: ('LT', '1', "Pre-Amplifier Notes", '', 'PreAmplifierNotes'),
    0x00144010: ('SQ', '1', "Transmit Transducer Sequence", '', 'TransmitTransducerSequence'),
    0x00144011: ('SQ', '1', "Receive Transducer Sequence", '', 'ReceiveTransducerSequence'),
    0x00144012: ('US', '1', "Number of Elements", '', 'NumberOfElements'),
    0x00144013: ('CS', '1', "Element Shape", '', 'ElementShape'),
    0x00144014: ('DS', '1', "Element Dimension A", '', 'ElementDimensionA'),
    0x00144015: ('DS', '1', "Element Dimension B", '', 'ElementDimensionB'),
    0x00144016: ('DS', '1', "Element Pitch A", '', 'ElementPitchA'),
    0x00144017: ('DS', '1', "Measured Beam Dimension A", '', 'MeasuredBeamDimensionA'),
    0x00144018: ('DS', '1', "Measured Beam Dimension B", '', 'MeasuredBeamDimensionB'),
    0x00144019: ('DS', '1', "Location of Measured Beam Diameter", '', 'LocationOfMeasuredBeamDiameter'),
    0x0014401A: ('DS', '1', "Nominal Frequency", '', 'NominalFrequency'),
    0x0014401B: ('DS', '1', "Measured Center Frequency", '', 'MeasuredCenterFrequency'),
    0x0014401C: ('DS', '1', "Measured Bandwidth", '', 'MeasuredBandwidth'),
    0x0014401D: ('DS', '1', "Element Pitch B", '', 'ElementPitchB'),
    0x00144020: ('SQ', '1', "Pulser Settings Sequence", '', 'PulserSettingsSequence'),
    0x00144022: ('DS', '1', "Pulse Width", '', 'PulseWidth'),
    0x00144024: ('DS', '1', "Excitation Frequency", '', 'ExcitationFrequency'),
    0x00144026: ('CS', '1', "Modulation Type", '', 'ModulationType'),
    0x00144028: ('DS', '1', "Damping", '', 'Damping'),
    0x00144030: ('SQ', '1', "Receiver Settings Sequence", '', 'ReceiverSettingsSequence'),
    0x00144031: ('DS', '1', "Acquired Soundpath Length", '', 'AcquiredSoundpathLength'),
    0x00144032: ('CS', '1', "Acquisition Compression Type", '', 'AcquisitionCompressionType'),
    0x00144033: ('IS', '1', "Acquisition Sample Size", '', 'AcquisitionSampleSize'),
    0x00144034: ('DS', '1', "Rectifier Smoothing", '', 'RectifierSmoothing'),
    0x00144035: ('SQ', '1', "DAC Sequence", '', 'DACSequence'),
    0x00144036: ('CS', '1', "DAC Type", '', 'DACType'),
    0x00144038: ('DS', '1-n', "DAC Gain Points", '', 'DACGainPoints'),
    0x0014403A: ('DS', '1-n', "DAC Time Points", '', 'DACTimePoints'),
    0x0014403C: ('DS', '1-n', "DAC Amplitude", '', 'DACAmplitude'),
    0x00144040: ('SQ', '1', "Pre-Amplifier Settings Sequence", '', 'PreAmplifierSettingsSequence'),
    0x00144050: ('SQ', '1', "Transmit Transducer Settings Sequence", '', 'TransmitTransducerSettingsSequence'),
    0x00144051: ('SQ', '1', "Receive Transducer Settings Sequence", '', 'ReceiveTransducerSettingsSequence'),
    0x00144052: ('DS', '1', "Incident Angle", '', 'IncidentAngle'),
    0x00144054: ('ST', '1', "Coupling Technique", '', 'CouplingTechnique'),
    0x00144056: ('ST', '1', "Coupling Medium", '', 'CouplingMedium'),
    0x00144057: ('DS', '1', "Coupling Velocity", '', 'CouplingVelocity'),
    0x00144058: ('DS', '1', "Probe Center Location X", '', 'ProbeCenterLocationX'),
    0x00144059: ('DS', '1', "Probe Center Location Z", '', 'ProbeCenterLocationZ'),
    0x0014405A: ('DS', '1', "Sound Path Length", '', 'SoundPathLength'),
    0x0014405C: ('ST', '1', "Delay Law Identifier", '', 'DelayLawIdentifier'),
    0x00144060: ('SQ', '1', "Gate Settings Sequence", '', 'GateSettingsSequence'),
    0x00144062: ('DS', '1', "Gate Threshold", '', 'GateThreshold'),
    0x00144064: ('DS', '1', "Velocity of Sound", '', 'VelocityOfSound'),
    0x00144070: ('SQ', '1', "Calibration Settings Sequence", '', 'CalibrationSettingsSequence'),
    0x00144072: ('ST', '1', "Calibration Procedure", '', 'CalibrationProcedure'),
    0x00144074: ('SH', '1', "Procedure Version", '', 'ProcedureVersion'),
    0x00144076: ('DA', '1', "Procedure Creation Date", '', 'ProcedureCreationDate'),
    0x00144078: ('DA', '1', "Procedure Expiration Date", '', 'ProcedureExpirationDate'),
    0x0014407A: ('DA', '1', "Procedure Last Modified Date", '', 'ProcedureLastModifiedDate'),
    0x0014407C: ('TM', '1-n', "Calibration Time", '', 'CalibrationTime'),
    0x0014407E: ('DA', '1-n', "Calibration Date", '', 'CalibrationDate'),
    0x00144080: ('SQ', '1', "Probe Drive Equipment Sequence", '', 'ProbeDriveEquipmentSequence'),
    0x00144081: ('CS', '1', "Drive Type", '', 'DriveType'),
    0x00144082: ('LT', '1', "Probe Drive Notes", '', 'ProbeDriveNotes'),
    0x00144083: ('SQ', '1', "Drive Probe Sequence", '', 'DriveProbeSequence'),
    0x00144084: ('DS', '1', "Probe Inductance", '', 'ProbeInductance'),
    0x00144085: ('DS', '1', "Probe Resistance", '', 'ProbeResistance'),
    0x00144086: ('SQ', '1', "Receive Probe Sequence", '', 'ReceiveProbeSequence'),
    0x00144087: ('SQ', '1', "Probe Drive Settings Sequence", '', 'ProbeDriveSettingsSequence'),
    0x00144088: ('DS', '1', "Bridge Resistors", '', 'BridgeResistors'),
    0x00144089: ('DS', '1', "Probe Orientation Angle", '', 'ProbeOrientationAngle'),
    0x0014408B: ('DS', '1', "User Selected Gain Y", '', 'UserSelectedGainY'),
    0x0014408C: ('DS', '1', "User Selected Phase", '', 'UserSelectedPhase'),
    0x0014408D: ('DS', '1', "User Selected Offset X", '', 'UserSelectedOffsetX'),
    0x0014408E: ('DS', '1', "User Selected Offset Y", '', 'UserSelectedOffsetY'),
    0x00144091: ('SQ', '1', "Channel Settings Sequence", '', 'ChannelSettingsSequence'),
    0x00144092: ('DS', '1', "Channel Threshold", '', 'ChannelThreshold'),
    0x0014409A: ('SQ', '1', "Scanner Settings Sequence", '', 'ScannerSettingsSequence'),
    0x0014409B: ('ST', '1', "Scan Procedure", '', 'ScanProcedure'),
    0x0014409C: ('DS', '1', "Translation Rate X", '', 'TranslationRateX'),
    0x0014409D: ('DS', '1', "Translation Rate Y", '', 'TranslationRateY'),
    0x0014409F: ('DS', '1', "Channel Overlap", '', 'ChannelOverlap'),
    0x001440A0: ('LO', '1-n', "Image Quality Indicator Type", '', 'ImageQualityIndicatorType'),
    0x001440A1: ('LO', '1-n', "Image Quality Indicator Material", '', 'ImageQualityIndicatorMaterial'),
    0x001440A2: ('LO', '1-n', "Image Quality Indicator Size", '', 'ImageQualityIndicatorSize'),
    0x00145002: ('IS', '1', "LINAC Energy", '', 'LINACEnergy'),
    0x00145004: ('IS', '1', "LINAC Output", '', 'LINACOutput'),
    0x00145100: ('US', '1', "Active Aperture", '', 'ActiveAperture'),
    0x00145101: ('DS', '1', "Total Aperture", '', 'TotalAperture'),
    0x00145102: ('DS', '1', "Aperture Elevation", '', 'ApertureElevation'),
    0x00145103: ('DS', '1', "Main Lobe Angle", '', 'MainLobeAngle'),
    0x00145104: ('DS', '1', "Main Roof Angle", '', 'MainRoofAngle'),
    0x00145105: ('CS', '1', "Connector Type", '', 'ConnectorType'),
    0x00145106: ('SH', '1', "Wedge Model Number", '', 'WedgeModelNumber'),
    0x00145107: ('DS', '1', "Wedge Angle Float", '', 'WedgeAngleFloat'),
    0x00145108: ('DS', '1', "Wedge Roof Angle", '', 'WedgeRoofAngle'),
    0x00145109: ('CS', '1', "Wedge Element 1 Position", '', 'WedgeElement1Position'),
    0x0014510A: ('DS', '1', "Wedge Material Velocity", '', 'WedgeMaterialVelocity'),
    0x0014510B: ('SH', '1', "Wedge Material", '', 'WedgeMaterial'),
    0x0014510C: ('DS', '1', "Wedge Offset Z", '', 'WedgeOffsetZ'),
    0x0014510D: ('DS', '1', "Wedge Origin Offset X", '', 'WedgeOriginOffsetX'),
    0x0014510E: ('DS', '1', "Wedge Time Delay", '', 'WedgeTimeDelay'),
    0x0014510F: ('SH', '1', "Wedge Name", '', 'WedgeName'),
    0x00145110: ('SH', '1', "Wedge Manufacturer Name", '', 'WedgeManufacturerName'),
    0x00145111: ('LO', '1', "Wedge Description", '', 'WedgeDescription'),
    0x00145112: ('DS', '1', "Nominal Beam Angle", '', 'NominalBeamAngle'),
    0x00145113: ('DS', '1', "Wedge Offset X", '', 'WedgeOffsetX'),
    0x00145114: ('DS', '1', "Wedge Offset Y", '', 'WedgeOffsetY'),
    0x00145115: ('DS', '1', "Wedge Total Length", '', 'WedgeTotalLength'),
    0x00145116: ('DS', '1', "Wedge In Contact Length", '', 'WedgeInContactLength'),
    0x00145117: ('DS', '1', "Wedge Front Gap", '', 'WedgeFrontGap'),
    0x00145118: ('DS', '1', "Wedge Total Height", '', 'WedgeTotalHeight'),
    0x00145119: ('DS', '1', "Wedge Front Height", '', 'WedgeFrontHeight'),
    0x0014511A: ('DS', '1', "Wedge Rear Height", '', 'WedgeRearHeight'),
    0x0014511B: ('DS', '1', "Wedge Total Width", '', 'WedgeTotalWidth'),
    0x0014511C: ('DS', '1', "Wedge In Contact Width", '', 'WedgeInContactWidth'),
    0x0014511D: ('DS', '1', "Wedge Chamfer Height", '', 'WedgeChamferHeight'),
    0x0014511E: ('CS', '1', "Wedge Curve", '', 'WedgeCurve'),
    0x0014511F: ('DS', '1', "Radius Along the Wedge", '', 'RadiusAlongWedge'),
    0x00146001: ('SQ', '1', "Thermal Camera Settings Sequence", '', 'ThermalCameraSettingsSequence'),
    0x00146002: ('DS', '1', "Acquisition Frame Rate", '', 'AcquisitionFrameRate'),
    0x00146003: ('DS', '1', "Integration Time", '', 'IntegrationTime'),
    0x00146004: ('DS', '1', "Number of Calibration Frames", '', 'NumberOfCalibrationFrames'),
    0x00146005: ('DS', '1', "Number of Rows in Full Acquisition Image", '', 'NumberOfRowsInFullAcquisitionImage'),
    0x00146006: ('DS', '1', "Number Of Columns in Full Acquisition Image", '', 'NumberOfColumnsInFullAcquisitionImage'),
    0x00146007: ('SQ', '1', "Thermal Source Settings Sequence", '', 'ThermalSourceSettingsSequence'),
    0x00146008: ('DS', '1', "Source Horizontal Pitch", '', 'SourceHorizontalPitch'),
    0x00146009: ('DS', '1', "Source Vertical Pitch", '', 'SourceVerticalPitch'),
    0x0014600A: ('DS', '1', "Source Horizontal Scan Speed", '', 'SourceHorizontalScanSpeed'),
    0x0014600B: ('DS', '1', "Thermal Source Modulation Frequency", '', 'ThermalSourceModulationFrequency'),
    0x0014600C: ('SQ', '1', "Induction Source Setting Sequence", '', 'InductionSourceSettingSequence'),
    0x0014600D: ('DS', '1', "Coil Frequency", '', 'CoilFrequency'),
    0x0014600E: ('DS', '1', "Current Amplitude Across Coil", '', 'CurrentAmplitudeAcrossCoil'),
    0x0014600F: ('SQ', '1', "Flash Source Setting Sequence", '', 'FlashSourceSettingSequence'),
    0x00146010: ('DS', '1', "Flash Duration", '', 'FlashDuration'),
    0x00146011: ('DS', '1-n', "Flash Frame Number", '', 'FlashFrameNumber'),
    0x00146012: ('SQ', '1', "Laser Source Setting Sequence", '', 'LaserSourceSettingSequence'),
    0x00146013: ('DS', '1', "Horizontal Laser Spot Dimension", '', 'HorizontalLaserSpotDimension'),
    0x00146014: ('DS', '1', "Vertical Laser Spot Dimension", '', 'VerticalLaserSpotDimension'),
    0x00146015: ('DS', '1', "Laser Wavelength", '', 'LaserWavelength'),
    0x00146016: ('DS', '1', "Laser Power", '', 'LaserPower'),
    0x00146017: ('SQ', '1', "Forced Gas Setting Sequence", '', 'ForcedGasSettingSequence'),
    0x00146018: ('SQ', '1', "Vibration Source Setting Sequence", '', 'VibrationSourceSettingSequence'),
    0x00146019: ('DS', '1', "Vibration Excitation Frequency", '', 'VibrationExcitationFrequency'),
    0x0014601A: ('DS', '1', "Vibration Excitation Voltage", '', 'VibrationExcitationVoltage'),
    0x0014601B: ('CS', '1', "Thermography Data Capture Method", '', 'ThermographyDataCaptureMethod'),
    0x0014601C: ('CS', '1', "Thermal Technique", '', 'ThermalTechnique'),
    0x0014601D: ('SQ', '1', "Thermal Camera Core Sequence", '', 'ThermalCameraCoreSequence'),
    0x0014601E: ('CS', '1', "Detector Wavelength Range", '', 'DetectorWavelengthRange'),
    0x0014601F: ('CS', '1', "Thermal Camera Calibration Type", '', 'ThermalCameraCalibrationType'),
    0x00146020: ('UV', '1', "Acquisition Image Counter", '', 'AcquisitionImageCounter'),
    0x00146021: ('DS', '1', "Front Panel Temperature", '', 'FrontPanelTemperature'),
    0x00146022: ('DS', '1', "Air Gap Temperature", '', 'AirGapTemperature'),
    0x00146023: ('DS', '1', "Vertical Pixel Size", '', 'VerticalPixelSize'),
    0x00146024: ('DS', '1', "Horizontal Pixel Size", '', 'HorizontalPixelSize'),
    0x00146025: ('ST', '1-n', "Data Streaming Protocol", '', 'DataStreamingProtocol'),
    0x00146026: ('SQ', '1', "Lens Sequence", '', 'LensSequence'),
    0x00146027: ('DS', '1', "Field of View", '', 'FieldOfView'),
    0x00146028: ('LO', '1', "Lens Filter Manufacturer", '', 'LensFilterManufacturer'),
    0x00146029: ('CS', '1', "Cutoff Filter Type", '', 'CutoffFilterType'),
    0x0014602A: ('DS', '1-n', "Lens Filter Cut-Off Wavelength", '', 'LensFilterCutOffWavelength'),
    0x0014602B: ('SQ', '1', "Thermal Source Sequence", '', 'ThermalSourceSequence'),
    0x0014602C: ('CS', '1', "Thermal Source Motion State", '', 'ThermalSourceMotionState'),
    0x0014602D: ('CS', '1', "Thermal Source Motion Type", '', 'ThermalSourceMotionType'),
    0x0014602E: ('SQ', '1', "Induction Heating Sequence", '', 'InductionHeatingSequence'),
    0x0014602F: ('ST', '1', "Coil Configuration ID", '', 'CoilConfigurationID'),
    0x00146030: ('DS', '1', "Number of Turns in Coil", '', 'NumberOfTurnsInCoil'),
    0x00146031: ('CS', '1', "Shape of Individual Turn", '', 'ShapeOfIndividualTurn'),
    0x00146032: ('DS', '1-n', "Size of Individual Turn", '', 'SizeOfIndividualTurn'),
    0x00146033: ('DS', '1-n', "Distance Between Turns", '', 'DistanceBetweenTurns'),
    0x00146034: ('SQ', '1', "Flash Heating Sequence", '', 'FlashHeatingSequence'),
    0x00146035: ('DS', '1', "Number of Lamps", '', 'NumberOfLamps'),
    0x00146036: ('ST', '1', "Flash Synchronization Protocol", '', 'FlashSynchronizationProtocol'),
    0x00146037: ('CS', '1', "Flash Modification Status", '', 'FlashModificationStatus'),
    0x00146038: ('SQ', '1', "Laser Heating Sequence", '', 'LaserHeatingSequence'),
    0x00146039: ('LO', '1', "Laser Manufacturer", '', 'LaserManufacturer'),
    0x0014603A: ('LO', '1', "Laser Model Number", '', 'LaserModelNumber'),
    0x0014603B: ('ST', '1', "Laser Type Description", '', 'LaserTypeDescription'),
    0x0014603C: ('SQ', '1', "Forced Gas Heating Sequence", '', 'ForcedGasHeatingSequence'),
    0x0014603D: ('LO', '1', "Gas Used for Heating/Cooling Part", '', 'GasUsedForHeatingCoolingPart'),
    0x0014603E: ('SQ', '1', "Vibration/Sonic Heating Sequence", '', 'VibrationSonicHeatingSequence'),
    0x0014603F: ('LO', '1', "Probe Manufacturer", '', 'ProbeManufacturer'),
    0x00146040: ('LO', '1', "Probe Model Number", '', 'ProbeModelNumber'),
    0x00146041: ('DS', '1', "Aperture Size", '', 'ApertureSize'),
    0x00146042: ('DS', '1', "Probe Resonant Frequency", '', 'ProbeResonantFrequency'),
    0x00146043: ('UT', '1', "Heat Source Description", '', 'HeatSourceDescription'),
    0x00146044: ('CS', '1', "Surface Preparation with Optical Coating", '', 'SurfacePreparationWithOpticalCoating'),
    0x00146045: ('ST', '1', "Optical Coating Type", '', 'OpticalCoatingType'),
    0x00146046: ('DS', '1', "Thermal Conductivity of Exposed Surface", '', 'ThermalConductivityOfExposedSurface'),
    0x00146047: ('DS', '1', "Material Density", '', 'MaterialDensity'),
    0x00146048: ('DS', '1', "Specific Heat of Inspection Surface", '', 'SpecificHeatOfInspectionSurface'),
    0x00146049: ('DS', '1', "Emissivity of Inspection Surface", '', 'EmissivityOfInspectionSurface'),
    0x0014604A: ('CS', '1-n', "Electromagnetic Classification of Inspection Surface", '', 'ElectromagneticClassificationOfInspectionSurface'),
    0x0014604C: ('DS', '1', "Moving Window Size", '', 'MovingWindowSize'),
    0x0014604D: ('CS', '1', "Moving Window Type", '', 'MovingWindowType'),
    0x0014604E: ('DS', '1-n', "Moving Window Weights", '', 'MovingWindowWeights'),
    0x0014604F: ('DS', '1', "Moving Window Pitch", '', 'MovingWindowPitch'),
    0x00146050: ('CS', '1', "Moving Window Padding Scheme", '', 'MovingWindowPaddingScheme'),
    0x00146051: ('DS', '1', "Moving Window Padding Sength", '', 'MovingWindowPaddingLength'),
    0x00146052: ('SQ', '1', "Spatial Filtering Parameters Sequence", '', 'SpatialFilteringParametersSequence'),
    0x00146053: ('CS', '1', "Spatial Filtering Scheme", '', 'SpatialFilteringScheme'),
    0x00146056: ('DS', '1', "Horizontal Moving Window Size", '', 'HorizontalMovingWindowSize'),
    0x00146057: ('DS', '1', "Vertical Moving Window Size", '', 'VerticalMovingWindowSize'),
    0x00146059: ('SQ', '1', "Polynomial Fitting Sequence", '', 'PolynomialFittingSequence'),
    0x0014605A: ('CS', '1-n', "Fitting Data Type", '', 'FittingDataType'),
    0x0014605B: ('CS', '1', "Operation on Time Axis Before Fitting", '', 'OperationOnTimeAxisBeforeFitting'),
    0x0014605C: ('CS', '1', "Operation on Pixel Intensity Before Fitting", '', 'OperationOnPixelIntensityBeforeFitting'),
    0x0014605D: ('DS', '1', "Order of Polynomial", '', 'OrderOfPolynomial'),
    0x0014605E: ('CS', '1', "Independent Variable for Polynomial Fit", '', 'IndependentVariableForPolynomialFit'),
    0x0014605F: ('DS', '1-n', "PolynomialCoefficients", '', 'PolynomialCoefficients'),
    0x00146060: ('CS', '1', "Thermography Pixel Data Unit", '', 'ThermographyPixelDataUnit'),
    0x00160001: ('DS', '1', "White Point", '', 'WhitePoint'),
    0x00160002: ('DS', '3', "Primary Chromaticities", '', 'PrimaryChromaticities'),
    0x00160003: ('UT', '1', "Battery Level", '', 'BatteryLevel'),
    0x00160004: ('DS', '1', "Exposure Time in Seconds", '', 'ExposureTimeInSeconds'),
    0x00160005: ('DS', '1', "F-Number", '', 'FNumber'),
    0x00160006: ('IS', '1', "OECF Rows", '', 'OECFRows'),
    0x00160007: ('IS', '1', "OECF Columns", '', 'OECFColumns'),
    0x00160008: ('UC', '1-n', "OECF Column Names", '', 'OECFColumnNames'),
    0x00160009: ('DS', '1-n', "OECF Values", '', 'OECFValues'),
    0x0016000A: ('IS', '1', "Spatial Frequency Response Rows", '', 'SpatialFrequencyResponseRows'),
    0x0016000B: ('IS', '1', "Spatial Frequency Response Columns", '', 'SpatialFrequencyResponseColumns'),
    0x0016000C: ('UC', '1-n', "Spatial Frequency Response Column Names", '', 'SpatialFrequencyResponseColumnNames'),
    0x0016000D: ('DS', '1-n', "Spatial Frequency Response Values", '', 'SpatialFrequencyResponseValues'),
    0x0016000E: ('IS', '1', "Color Filter Array Pattern Rows", '', 'ColorFilterArrayPatternRows'),
    0x0016000F: ('IS', '1', "Color Filter Array Pattern Columns", '', 'ColorFilterArrayPatternColumns'),
    0x00160010: ('DS', '1-n', "Color Filter Array Pattern Values", '', 'ColorFilterArrayPatternValues'),
    0x00160011: ('US', '1', "Flash Firing Status", '', 'FlashFiringStatus'),
    0x00160012: ('US', '1', "Flash Return Status", '', 'FlashReturnStatus'),
    0x00160013: ('US', '1', "Flash Mode", '', 'FlashMode'),
    0x00160014: ('US', '1', "Flash Function Present", '', 'FlashFunctionPresent'),
    0x00160015: ('US', '1', "Flash Red Eye Mode", '', 'FlashRedEyeMode'),
    0x00160016: ('US', '1', "Exposure Program", '', 'ExposureProgram'),
    0x00160017: ('UT', '1', "Spectral Sensitivity", '', 'SpectralSensitivity'),
    0x00160018: ('IS', '1', "Photographic Sensitivity", '', 'PhotographicSensitivity'),
    0x00160019: ('IS', '1', "Self Timer Mode", '', 'SelfTimerMode'),
    0x0016001A: ('US', '1', "Sensitivity Type", '', 'SensitivityType'),
    0x0016001B: ('IS', '1', "Standard Output Sensitivity", '', 'StandardOutputSensitivity'),
    0x0016001C: ('IS', '1', "Recommended Exposure Index", '', 'RecommendedExposureIndex'),
    0x0016001D: ('IS', '1', "ISO Speed", '', 'ISOSpeed'),
    0x0016001E: ('IS', '1', "ISO Speed Latitude yyy", '', 'ISOSpeedLatitudeyyy'),
    0x0016001F: ('IS', '1', "ISO Speed Latitude zzz", '', 'ISOSpeedLatitudezzz'),
    0x00160020: ('UT', '1', "EXIF Version", '', 'EXIFVersion'),
    0x00160021: ('DS', '1', "Shutter Speed Value", '', 'ShutterSpeedValue'),
    0x00160022: ('DS', '1', "Aperture Value", '', 'ApertureValue'),
    0x00160023: ('DS', '1', "Brightness Value", '', 'BrightnessValue'),
    0x00160024: ('DS', '1', "Exposure Bias Value", '', 'ExposureBiasValue'),
    0x00160025: ('DS', '1', "Max Aperture Value", '', 'MaxApertureValue'),
    0x00160026: ('DS', '1', "Subject Distance", '', 'SubjectDistance'),
    0x00160027: ('US', '1', "Metering Mode", '', 'MeteringMode'),
    0x00160028: ('US', '1', "Light Source", '', 'LightSource'),
    0x00160029: ('DS', '1', "Focal Length", '', 'FocalLength'),
    0x0016002A: ('IS', '2-4', "Subject Area", '', 'SubjectArea'),
    0x0016002B: ('OB', '1', "Maker Note", '', 'MakerNote'),
    0x00160030: ('DS', '1', "Temperature", '', 'Temperature'),
    0x00160031: ('DS', '1', "Humidity", '', 'Humidity'),
    0x00160032: ('DS', '1', "Pressure", '', 'Pressure'),
    0x00160033: ('DS', '1', "Water Depth", '', 'WaterDepth'),
    0x00160034: ('DS', '1', "Acceleration", '', 'Acceleration'),
    0x00160035: ('DS', '1', "Camera Elevation Angle", '', 'CameraElevationAngle'),
    0x00160036: ('DS', '1-2', "Flash Energy", '', 'FlashEnergy'),
    0x00160037: ('IS', '2', "Subject Location", '', 'SubjectLocation'),
    0x00160038: ('DS', '1', "Photographic Exposure Index", '', 'PhotographicExposureIndex'),
    0x00160039: ('US', '1', "Sensing Method", '', 'SensingMethod'),
    0x0016003A: ('US', '1', "File Source", '', 'FileSource'),
    0x0016003B: ('US', '1', "Scene Type", '', 'SceneType'),
    0x00160041: ('US', '1', "Custom Rendered", '', 'CustomRendered'),
    0x00160042: ('US', '1', "Exposure Mode", '', 'ExposureMode'),
    0x00160043: ('US', '1', "White Balance", '', 'WhiteBalance'),
    0x00160044: ('DS', '1', "Digital Zoom Ratio", '', 'DigitalZoomRatio'),
    0x00160045: ('IS', '1', "Focal Length In 35mm Film", '', 'FocalLengthIn35mmFilm'),
    0x00160046: ('US', '1', "Scene Capture Type", '', 'SceneCaptureType'),
    0x00160047: ('US', '1', "Gain Control", '', 'GainControl'),
    0x00160048: ('US', '1', "Contrast", '', 'Contrast'),
    0x00160049: ('US', '1', "Saturation", '', 'Saturation'),
    0x0016004A: ('US', '1', "Sharpness", '', 'Sharpness'),
    0x0016004B: ('OB', '1', "Device Setting Description", '', 'DeviceSettingDescription'),
    0x0016004C: ('US', '1', "Subject Distance Range", '', 'SubjectDistanceRange'),
    0x0016004D: ('UT', '1', "Camera Owner Name", '', 'CameraOwnerName'),
    0x0016004E: ('DS', '4', "Lens Specification", '', 'LensSpecification'),
    0x0016004F: ('UT', '1', "Lens Make", '', 'LensMake'),
    0x00160050: ('UT', '1', "Lens Model", '', 'LensModel'),
    0x00160051: ('UT', '1', "Lens Serial Number", '', 'LensSerialNumber'),
    0x00160061: ('CS', '1', "Interoperability Index", '', 'InteroperabilityIndex'),
    0x00160062: ('OB', '1', "Interoperability Version", '', 'InteroperabilityVersion'),
    0x00160070: ('OB', '1', "GPS Version ID", '', 'GPSVersionID'),
    0x00160071: ('CS', '1', "GPS Latitude Ref", '', 'GPSLatitudeRef'),
    0x00160072: ('DS', '3', "GPS Latitude", '', 'GPSLatitude'),
    0x00160073: ('CS', '1', "GPS Longitude Ref", '', 'GPSLongitudeRef'),
    0x00160074: ('DS', '3', "GPS Longitude", '', 'GPSLongitude'),
    0x00160075: ('US', '1', "GPS Altitude Ref", '', 'GPSAltitudeRef'),
    0x00160076: ('DS', '1', "GPS Altitude", '', 'GPSAltitude'),
    0x00160077: ('DT', '1', "GPS Time Stamp", '', 'GPSTimeStamp'),
    0x00160078: ('UT', '1', "GPS Satellites", '', 'GPSSatellites'),
    0x00160079: ('CS', '1', "GPS Status", '', 'GPSStatus'),
    0x0016007A: ('CS', '1', "GPS Measure Mode", '', 'GPSMeasureMode'),
    0x0016007B: ('DS', '1', "GPS DOP", '', 'GPSDOP'),
    0x0016007C: ('CS', '1', "GPS Speed Ref", '', 'GPSSpeedRef'),
    0x0016007D: ('DS', '1', "GPS Speed", '', 'GPSSpeed'),
    0x0016007E: ('CS', '1', "GPS Track Ref", '', 'GPSTrackRef'),
    0x0016007F: ('DS', '1', "GPS Track", '', 'GPSTrack'),
    0x00160080: ('CS', '1', "GPS Img Direction Ref", '', 'GPSImgDirectionRef'),
    0x00160081: ('DS', '1', "GPS Img Direction", '', 'GPSImgDirection'),
    0x00160082: ('UT', '1', "GPS Map Datum", '', 'GPSMapDatum'),
    0x00160083: ('CS', '1', "GPS Dest Latitude Ref", '', 'GPSDestLatitudeRef'),
    0x00160084: ('DS', '3', "GPS Dest Latitude", '', 'GPSDestLatitude'),
    0x00160085: ('CS', '1', "GPS Dest Longitude Ref", '', 'GPSDestLongitudeRef'),
    0x00160086: ('DS', '3', "GPS Dest Longitude", '', 'GPSDestLongitude'),
    0x00160087: ('CS', '1', "GPS Dest Bearing Ref", '', 'GPSDestBearingRef'),
    0x00160088: ('DS', '1', "GPS Dest Bearing", '', 'GPSDestBearing'),
    0x00160089: ('CS', '1', "GPS Dest Distance Ref", '', 'GPSDestDistanceRef'),
    0x0016008A: ('DS', '1', "GPS Dest Distance", '', 'GPSDestDistance'),
    0x0016008B: ('OB', '1', "GPS Processing Method", '', 'GPSProcessingMethod'),
    0x0016008C: ('OB', '1', "GPS Area Information", '', 'GPSAreaInformation'),
    0x0016008D: ('DT', '1', "GPS Date Stamp", '', 'GPSDateStamp'),
    0x0016008E: ('IS', '1', "GPS Differential", '', 'GPSDifferential'),
    0x00161001: ('CS', '1', "Light Source Polarization", '', 'LightSourcePolarization'),
    0x00161002: ('DS', '1', "Emitter Color Temperature", '', 'EmitterColorTemperature'),
    0x00161003: ('CS', '1', "Contact Method", '', 'ContactMethod'),
    0x00161004: ('CS', '1-n', "Immersion Media", '', 'ImmersionMedia'),
    0x00161005: ('DS', '1', "Optical Magnification Factor", '', 'OpticalMagnificationFactor'),
    0x00180010: ('LO', '1', "Contrast/Bolus Agent", '', 'ContrastBolusAgent'),
    0x00180012: ('SQ', '1', "Contrast/Bolus Agent Sequence", '', 'ContrastBolusAgentSequence'),
    0x00180013: ('FL', '1', "Contrast/Bolus T1 Relaxivity", '', 'ContrastBolusT1Relaxivity'),
    0x00180014: ('SQ', '1', "Contrast/Bolus Administration Route Sequence", '', 'ContrastBolusAdministrationRouteSequence'),
    0x00180015: ('CS', '1', "Body Part Examined", '', 'BodyPartExamined'),
    0x00180020: ('CS', '1-n', "Scanning Sequence", '', 'ScanningSequence'),
    0x00180021: ('CS', '1-n', "Sequence Variant", '', 'SequenceVariant'),
    0x00180022: ('CS', '1-n', "Scan Options", '', 'ScanOptions'),
    0x00180023: ('CS', '1', "MR Acquisition Type", '', 'MRAcquisitionType'),
    0x00180024: ('SH', '1', "Sequence Name", '', 'SequenceName'),
    0x00180025: ('CS', '1', "Angio Flag", '', 'AngioFlag'),
    0x00180026: ('SQ', '1', "Intervention Drug Information Sequence", '', 'InterventionDrugInformationSequence'),
    0x00180027: ('TM', '1', "Intervention Drug Stop Time", '', 'InterventionDrugStopTime'),
    0x00180028: ('DS', '1', "Intervention Drug Dose", '', 'InterventionDrugDose'),
    0x00180029: ('SQ', '1', "Intervention Drug Code Sequence", '', 'InterventionDrugCodeSequence'),
    0x0018002A: ('SQ', '1', "Additional Drug Sequence", '', 'AdditionalDrugSequence'),
    0x00180030: ('LO', '1-n', "Radionuclide", 'Retired', 'Radionuclide'),
    0x00180031: ('LO', '1', "Radiopharmaceutical", '', 'Radiopharmaceutical'),
    0x00180032: ('DS', '1', "Energy Window Centerline", 'Retired', 'EnergyWindowCenterline'),
    0x00180033: ('DS', '1-n', "Energy Window Total Width", 'Retired', 'EnergyWindowTotalWidth'),
    0x00180034: ('LO', '1', "Intervention Drug Name", '', 'InterventionDrugName'),
    0x00180035: ('TM', '1', "Intervention Drug Start Time", '', 'InterventionDrugStartTime'),
    0x00180036: ('SQ', '1', "Intervention Sequence", '', 'InterventionSequence'),
    0x00180037: ('CS', '1', "Therapy Type", 'Retired', 'TherapyType'),
    0x00180038: ('CS', '1', "Intervention Status", '', 'InterventionStatus'),
    0x00180039: ('CS', '1', "Therapy Description", 'Retired', 'TherapyDescription'),
    0x0018003A: ('ST', '1', "Intervention Description", '', 'InterventionDescription'),
    0x00180040: ('IS', '1', "Cine Rate", '', 'CineRate'),
    0x00180042: ('CS', '1', "Initial Cine Run State", '', 'InitialCineRunState'),
    0x00180050: ('DS', '1', "Slice Thickness", '', 'SliceThickness'),
    0x00180060: ('DS', '1', "KVP", '', 'KVP'),
    0x00180061: ('DS', '1', "Retired-blank", 'Retired', ''),
    0x00180070: ('IS', '1', "Counts Accumulated", '', 'CountsAccumulated'),
    0x00180071: ('CS', '1', "Acquisition Termination Condition", '', 'AcquisitionTerminationCondition'),
    0x00180072: ('DS', '1', "Effective Duration", '', 'EffectiveDuration'),
    0x00180073: ('CS', '1', "Acquisition Start Condition", '', 'AcquisitionStartCondition'),
    0x00180074: ('IS', '1', "Acquisition Start Condition Data", '', 'AcquisitionStartConditionData'),
    0x00180075: ('IS', '1', "Acquisition Termination Condition Data", '', 'AcquisitionTerminationConditionData'),
    0x00180080: ('DS', '1', "Repetition Time", '', 'RepetitionTime'),
    0x00180081: ('DS', '1', "Echo Time", '', 'EchoTime'),
    0x00180082: ('DS', '1', "Inversion Time", '', 'InversionTime'),
    0x00180083: ('DS', '1', "Number of Averages", '', 'NumberOfAverages'),
    0x00180084: ('DS', '1', "Imaging Frequency", '', 'ImagingFrequency'),
    0x00180085: ('SH', '1', "Imaged Nucleus", '', 'ImagedNucleus'),
    0x00180086: ('IS', '1-n', "Echo Number(s)", '', 'EchoNumbers'),
    0x00180087: ('DS', '1', "Magnetic Field Strength", '', 'MagneticFieldStrength'),
    0x00180088: ('DS', '1', "Spacing Between Slices", '', 'SpacingBetweenSlices'),
    0x00180089: ('IS', '1', "Number of Phase Encoding Steps", '', 'NumberOfPhaseEncodingSteps'),
    0x00180090: ('DS', '1', "Data Collection Diameter", '', 'DataCollectionDiameter'),
    0x00180091: ('IS', '1', "Echo Train Length", '', 'EchoTrainLength'),
    0x00180093: ('DS', '1', "Percent Sampling", '', 'PercentSampling'),
    0x00180094: ('DS', '1', "Percent Phase Field of View", '', 'PercentPhaseFieldOfView'),
    0x00180095: ('DS', '1', "Pixel Bandwidth", '', 'PixelBandwidth'),
    0x00181000: ('LO', '1', "Device Serial Number", '', 'DeviceSerialNumber'),
    0x00181002: ('UI', '1', "Device UID", '', 'DeviceUID'),
    0x00181003: ('LO', '1', "Device ID", '', 'DeviceID'),
    0x00181004: ('LO', '1', "Plate ID", '', 'PlateID'),
    0x00181005: ('LO', '1', "Generator ID", '', 'GeneratorID'),
    0x00181006: ('LO', '1', "Grid ID", '', 'GridID'),
    0x00181007: ('LO', '1', "Cassette ID", '', 'CassetteID'),
    0x00181008: ('LO', '1', "Gantry ID", '', 'GantryID'),
    0x00181009: ('UT', '1', "Unique Device Identifier", '', 'UniqueDeviceIdentifier'),
    0x0018100A: ('SQ', '1', "UDI Sequence", '', 'UDISequence'),
    0x0018100B: ('UI', '1-n', "Manufacturer's Device Class UID", '', 'ManufacturerDeviceClassUID'),
    0x00181010: ('LO', '1', "Secondary Capture Device ID", '', 'SecondaryCaptureDeviceID'),
    0x00181011: ('LO', '1', "Hardcopy Creation Device ID", 'Retired', 'HardcopyCreationDeviceID'),
    0x00181012: ('DA', '1', "Date of Secondary Capture", '', 'DateOfSecondaryCapture'),
    0x00181014: ('TM', '1', "Time of Secondary Capture", '', 'TimeOfSecondaryCapture'),
    0x00181016: ('LO', '1', "Secondary Capture Device Manufacturer", '', 'SecondaryCaptureDeviceManufacturer'),
    0x00181017: ('LO', '1', "Hardcopy Device Manufacturer", 'Retired', 'HardcopyDeviceManufacturer'),
    0x00181018: ('LO', '1', "Secondary Capture Device Manufacturer's Model Name", '', 'SecondaryCaptureDeviceManufacturerModelName'),
    0x00181019: ('LO', '1-n', "Secondary Capture Device Software Versions", '', 'SecondaryCaptureDeviceSoftwareVersions'),
    0x0018101A: ('LO', '1-n', "Hardcopy Device Software Version", 'Retired', 'HardcopyDeviceSoftwareVersion'),
    0x0018101B: ('LO', '1', "Hardcopy Device Manufacturer's Model Name", 'Retired', 'HardcopyDeviceManufacturerModelName'),
    0x00181020: ('LO', '1-n', "Software Versions", '', 'SoftwareVersions'),
    0x00181022: ('SH', '1', "Video Image Format Acquired", '', 'VideoImageFormatAcquired'),
    0x00181023: ('LO', '1', "Digital Image Format Acquired", '', 'DigitalImageFormatAcquired'),
    0x00181030: ('LO', '1', "Protocol Name", '', 'ProtocolName'),
    0x00181040: ('LO', '1', "Contrast/Bolus Route", '', 'ContrastBolusRoute'),
    0x00181041: ('DS', '1', "Contrast/Bolus Volume", '', 'ContrastBolusVolume'),
    0x00181042: ('TM', '1', "Contrast/Bolus Start Time", '', 'ContrastBolusStartTime'),
    0x00181043: ('TM', '1', "Contrast/Bolus Stop Time", '', 'ContrastBolusStopTime'),
    0x00181044: ('DS', '1', "Contrast/Bolus Total Dose", '', 'ContrastBolusTotalDose'),
    0x00181045: ('IS', '1', "Syringe Counts", '', 'SyringeCounts'),
    0x00181046: ('DS', '1-n', "Contrast Flow Rate", '', 'ContrastFlowRate'),
    0x00181047: ('DS', '1-n', "Contrast Flow Duration", '', 'ContrastFlowDuration'),
    0x00181048: ('CS', '1', "Contrast/Bolus Ingredient", '', 'ContrastBolusIngredient'),
    0x00181049: ('DS', '1', "Contrast/Bolus Ingredient Concentration", '', 'ContrastBolusIngredientConcentration'),
    0x00181050: ('DS', '1', "Spatial Resolution", '', 'SpatialResolution'),
    0x00181060: ('DS', '1', "Trigger Time", '', 'TriggerTime'),
    0x00181061: ('LO', '1', "Trigger Source or Type", '', 'TriggerSourceOrType'),
    0x00181062: ('IS', '1', "Nominal Interval", '', 'NominalInterval'),
    0x00181063: ('DS', '1', "Frame Time", '', 'FrameTime'),
    0x00181064: ('LO', '1', "Cardiac Framing Type", '', 'CardiacFramingType'),
    0x00181065: ('DS', '1-n', "Frame Time Vector", '', 'FrameTimeVector'),
    0x00181066: ('DS', '1', "Frame Delay", '', 'FrameDelay'),
    0x00181067: ('DS', '1', "Image Trigger Delay", '', 'ImageTriggerDelay'),
    0x00181068: ('DS', '1', "Multiplex Group Time Offset", '', 'MultiplexGroupTimeOffset'),
    0x00181069: ('DS', '1', "Trigger Time Offset", '', 'TriggerTimeOffset'),
    0x0018106A: ('CS', '1', "Synchronization Trigger", '', 'SynchronizationTrigger'),
    0x0018106C: ('US', '2', "Synchronization Channel", '', 'SynchronizationChannel'),
    0x0018106E: ('UL', '1', "Trigger Sample Position", '', 'TriggerSamplePosition'),
    0x00181070: ('LO', '1', "Radiopharmaceutical Route", '', 'RadiopharmaceuticalRoute'),
    0x00181071: ('DS', '1', "Radiopharmaceutical Volume", '', 'RadiopharmaceuticalVolume'),
    0x00181072: ('TM', '1', "Radiopharmaceutical Start Time", '', 'RadiopharmaceuticalStartTime'),
    0x00181073: ('TM', '1', "Radiopharmaceutical Stop Time", '', 'RadiopharmaceuticalStopTime'),
    0x00181074: ('DS', '1', "Radionuclide Total Dose", '', 'RadionuclideTotalDose'),
    0x00181075: ('DS', '1', "Radionuclide Half Life", '', 'RadionuclideHalfLife'),
    0x00181076: ('DS', '1', "Radionuclide Positron Fraction", '', 'RadionuclidePositronFraction'),
    0x00181077: ('DS', '1', "Radiopharmaceutical Specific Activity", '', 'RadiopharmaceuticalSpecificActivity'),
    0x00181078: ('DT', '1', "Radiopharmaceutical Start DateTime", '', 'RadiopharmaceuticalStartDateTime'),
    0x00181079: ('DT', '1', "Radiopharmaceutical Stop DateTime", '', 'RadiopharmaceuticalStopDateTime'),
    0x00181080: ('CS', '1', "Beat Rejection Flag", '', 'BeatRejectionFlag'),
    0x00181081: ('IS', '1', "Low R-R Value", '', 'LowRRValue'),
    0x00181082: ('IS', '1', "High R-R Value", '', 'HighRRValue'),
    0x00181083: ('IS', '1', "Intervals Acquired", '', 'IntervalsAcquired'),
    0x00181084: ('IS', '1', "Intervals Rejected", '', 'IntervalsRejected'),
    0x00181085: ('LO', '1', "PVC Rejection", '', 'PVCRejection'),
    0x00181086: ('IS', '1', "Skip Beats", '', 'SkipBeats'),
    0x00181088: ('IS', '1', "Heart Rate", '', 'HeartRate'),
    0x00181090: ('IS', '1', "Cardiac Number of Images", '', 'CardiacNumberOfImages'),
    0x00181094: ('IS', '1', "Trigger Window", '', 'TriggerWindow'),
    0x00181100: ('DS', '1', "Reconstruction Diameter", '', 'ReconstructionDiameter'),
    0x00181110: ('DS', '1', "Distance Source to Detector", '', 'DistanceSourceToDetector'),
    0x00181111: ('DS', '1', "Distance Source to Patient", '', 'DistanceSourceToPatient'),
    0x00181114: ('DS', '1', "Estimated Radiographic Magnification Factor", '', 'EstimatedRadiographicMagnificationFactor'),
    0x00181120: ('DS', '1', "Gantry/Detector Tilt", '', 'GantryDetectorTilt'),
    0x00181121: ('DS', '1', "Gantry/Detector Slew", '', 'GantryDetectorSlew'),
    0x00181130: ('DS', '1', "Table Height", '', 'TableHeight'),
    0x00181131: ('DS', '1', "Table Traverse", '', 'TableTraverse'),
    0x00181134: ('CS', '1', "Table Motion", '', 'TableMotion'),
    0x00181135: ('DS', '1-n', "Table Vertical Increment", '', 'TableVerticalIncrement'),
    0x00181136: ('DS', '1-n', "Table Lateral Increment", '', 'TableLateralIncrement'),
    0x00181137: ('DS', '1-n', "Table Longitudinal Increment", '', 'TableLongitudinalIncrement'),
    0x00181138: ('DS', '1', "Table Angle", '', 'TableAngle'),
    0x0018113A: ('CS', '1', "Table Type", '', 'TableType'),
    0x00181140: ('CS', '1', "Rotation Direction", '', 'RotationDirection'),
    0x00181141: ('DS', '1', "Angular Position", 'Retired', 'AngularPosition'),
    0x00181142: ('DS', '1-n', "Radial Position", '', 'RadialPosition'),
    0x00181143: ('DS', '1', "Scan Arc", '', 'ScanArc'),
    0x00181144: ('DS', '1', "Angular Step", '', 'AngularStep'),
    0x00181145: ('DS', '1', "Center of Rotation Offset", '', 'CenterOfRotationOffset'),
    0x00181146: ('DS', '1-n', "Rotation Offset", 'Retired', 'RotationOffset'),
    0x00181147: ('CS', '1', "Field of View Shape", '', 'FieldOfViewShape'),
    0x00181149: ('IS', '1-2', "Field of View Dimension(s)", '', 'FieldOfViewDimensions'),
    0x00181150: ('IS', '1', "Exposure Time", '', 'ExposureTime'),
    0x00181151: ('IS', '1', "X-Ray Tube Current", '', 'XRayTubeCurrent'),
    0x00181152: ('IS', '1', "Exposure", '', 'Exposure'),
    0x00181153: ('IS', '1', "Exposure in uAs", '', 'ExposureInuAs'),
    0x00181154: ('DS', '1', "Average Pulse Width", '', 'AveragePulseWidth'),
    0x00181155: ('CS', '1', "Radiation Setting", '', 'RadiationSetting'),
    0x00181156: ('CS', '1', "Rectification Type", '', 'RectificationType'),
    0x0018115A: ('CS', '1', "Radiation Mode", '', 'RadiationMode'),
    0x0018115E: ('DS', '1', "Image and Fluoroscopy Area Dose Product", '', 'ImageAndFluoroscopyAreaDoseProduct'),
    0x00181160: ('SH', '1', "Filter Type", '', 'FilterType'),
    0x00181161: ('LO', '1-n', "Type of Filters", '', 'TypeOfFilters'),
    0x00181162: ('DS', '1', "Intensifier Size", '', 'IntensifierSize'),
    0x00181164: ('DS', '2', "Imager Pixel Spacing", '', 'ImagerPixelSpacing'),
    0x00181166: ('CS', '1-n', "Grid", '', 'Grid'),
    0x00181170: ('IS', '1', "Generator Power", '', 'GeneratorPower'),
    0x00181180: ('SH', '1', "Collimator/grid Name", '', 'CollimatorGridName'),
    0x00181181: ('CS', '1', "Collimator Type", '', 'CollimatorType'),
    0x00181182: ('IS', '1-2', "Focal Distance", '', 'FocalDistance'),
    0x00181183: ('DS', '1-2', "X Focus Center", '', 'XFocusCenter'),
    0x00181184: ('DS', '1-2', "Y Focus Center", '', 'YFocusCenter'),
    0x00181190: ('DS', '1-n', "Focal Spot(s)", '', 'FocalSpots'),
    0x00181191: ('CS', '1', "Anode Target Material", '', 'AnodeTargetMaterial'),
    0x001811A0: ('DS', '1', "Body Part Thickness", '', 'BodyPartThickness'),
    0x001811A2: ('DS', '1', "Compression Force", '', 'CompressionForce'),
    0x001811A3: ('DS', '1', "Compression Pressure", '', 'CompressionPressure'),
    0x001811A4: ('LO', '1', "Paddle Description", '', 'PaddleDescription'),
    0x001811A5: ('DS', '1', "Compression Contact Area", '', 'CompressionContactArea'),
    0x001811B0: ('LO', '1', "Acquisition Mode", '', 'AcquisitionMode'),
    0x001811B1: ('LO', '1', "Dose Mode Name", '', 'DoseModeName'),
    0x001811B2: ('CS', '1', "Acquired Subtraction Mask Flag", '', 'AcquiredSubtractionMaskFlag'),
    0x001811B3: ('CS', '1', "Fluoroscopy Persistence Flag", '', 'FluoroscopyPersistenceFlag'),
    0x001811B4: ('CS', '1', "Fluoroscopy Last Image Hold Persistence Flag", '', 'FluoroscopyLastImageHoldPersistenceFlag'),
    0x001811B5: ('IS', '1', "Upper Limit Number Of Persistent Fluoroscopy Frames", '', 'UpperLimitNumberOfPersistentFluoroscopyFrames'),
    0x001811B6: ('CS', '1', "Contrast/Bolus Auto Injection Trigger Flag", '', 'ContrastBolusAutoInjectionTriggerFlag'),
    0x001811B7: ('FD', '1', "Contrast/Bolus Injection Delay", '', 'ContrastBolusInjectionDelay'),
    0x001811B8: ('SQ', '1', "XA Acquisition Phase Details Sequence", '', 'XAAcquisitionPhaseDetailsSequence'),
    0x001811B9: ('FD', '1', "XA Acquisition Frame Rate", '', 'XAAcquisitionFrameRate'),
    0x001811BA: ('SQ', '1', "XA Plane Details Sequence", '', 'XAPlaneDetailsSequence'),
    0x001811BB: ('LO', '1', "Acquisition Field of View Label", '', 'AcquisitionFieldOfViewLabel'),
    0x001811BC: ('SQ', '1', "X-Ray Filter Details Sequence", '', 'XRayFilterDetailsSequence'),
    0x001811BD: ('FD', '1', "XA Acquisition Duration", '', 'XAAcquisitionDuration'),
    0x001811BE: ('CS', '1', "Reconstruction Pipeline Type", '', 'ReconstructionPipelineType'),
    0x001811BF: ('SQ', '1', "Image Filter Details Sequence", '', 'ImageFilterDetailsSequence'),
    0x001811C0: ('CS', '1', "Applied Mask Subtraction Flag", '', 'AppliedMaskSubtractionFlag'),
    0x001811C1: ('SQ', '1', "Requested Series Description Code Sequence", '', 'RequestedSeriesDescriptionCodeSequence'),
    0x00181200: ('DA', '1-n', "Date of Last Calibration", '', 'DateOfLastCalibration'),
    0x00181201: ('TM', '1-n', "Time of Last Calibration", '', 'TimeOfLastCalibration'),
    0x00181202: ('DT', '1', "DateTime of Last Calibration", '', 'DateTimeOfLastCalibration'),
    0x00181203: ('DT', '1', "Calibration DateTime", '', 'CalibrationDateTime'),
    0x00181204: ('DA', '1', "Date of Manufacture", '', 'DateOfManufacture'),
    0x00181205: ('DA', '1', "Date of Installation", '', 'DateOfInstallation'),
    0x00181210: ('SH', '1-n', "Convolution Kernel", '', 'ConvolutionKernel'),
    0x00181240: ('IS', '1-n', "Upper/Lower Pixel Values", 'Retired', 'UpperLowerPixelValues'),
    0x00181242: ('IS', '1', "Actual Frame Duration", '', 'ActualFrameDuration'),
    0x00181243: ('IS', '1', "Count Rate", '', 'CountRate'),
    0x00181244: ('US', '1', "Preferred Playback Sequencing", '', 'PreferredPlaybackSequencing'),
    0x00181250: ('SH', '1', "Receive Coil Name", '', 'ReceiveCoilName'),
    0x00181251: ('SH', '1', "Transmit Coil Name", '', 'TransmitCoilName'),
    0x00181260: ('SH', '1', "Plate Type", '', 'PlateType'),
    0x00181261: ('LO', '1', "Phosphor Type", '', 'PhosphorType'),
    0x00181271: ('FD', '1', "Water Equivalent Diameter", '', 'WaterEquivalentDiameter'),
    0x00181272: ('SQ', '1', "Water Equivalent Diameter Calculation Method Code Sequence", '', 'WaterEquivalentDiameterCalculationMethodCodeSequence'),
    0x00181300: ('DS', '1', "Scan Velocity", '', 'ScanVelocity'),
    0x00181301: ('CS', '1-n', "Whole Body Technique", '', 'WholeBodyTechnique'),
    0x00181302: ('IS', '1', "Scan Length", '', 'ScanLength'),
    0x00181310: ('US', '4', "Acquisition Matrix", '', 'AcquisitionMatrix'),
    0x00181312: ('CS', '1', "In-plane Phase Encoding Direction", '', 'InPlanePhaseEncodingDirection'),
    0x00181314: ('DS', '1', "Flip Angle", '', 'FlipAngle'),
    0x00181315: ('CS', '1', "Variable Flip Angle Flag", '', 'VariableFlipAngleFlag'),
    0x00181316: ('DS', '1', "SAR", '', 'SAR'),
    0x00181318: ('DS', '1', "dB/dt", '', 'dBdt'),
    0x00181320: ('FL', '1', "B1rms", '', 'B1rms'),
    0x00181400: ('LO', '1', "Acquisition Device Processing Description", '', 'AcquisitionDeviceProcessingDescription'),
    0x00181401: ('LO', '1', "Acquisition Device Processing Code", '', 'AcquisitionDeviceProcessingCode'),
    0x00181402: ('CS', '1', "Cassette Orientation", '', 'CassetteOrientation'),
    0x00181403: ('CS', '1', "Cassette Size", '', 'CassetteSize'),
    0x00181404: ('US', '1', "Exposures on Plate", '', 'ExposuresOnPlate'),
    0x00181405: ('IS', '1', "Relative X-Ray Exposure", '', 'RelativeXRayExposure'),
    0x00181411: ('DS', '1', "Exposure Index", '', 'ExposureIndex'),
    0x00181412: ('DS', '1', "Target Exposure Index", '', 'TargetExposureIndex'),
    0x00181413: ('DS', '1', "Deviation Index", '', 'DeviationIndex'),
    0x00181450: ('DS', '1', "Column Angulation", '', 'ColumnAngulation'),
    0x00181460: ('DS', '1', "Tomo Layer Height", '', 'TomoLayerHeight'),
    0x00181470: ('DS', '1', "Tomo Angle", '', 'TomoAngle'),
    0x00181480: ('DS', '1', "Tomo Time", '', 'TomoTime'),
    0x00181490: ('CS', '1', "Tomo Type", '', 'TomoType'),
    0x00181491: ('CS', '1', "Tomo Class", '', 'TomoClass'),
    0x00181495: ('IS', '1', "Number of Tomosynthesis Source Images", '', 'NumberOfTomosynthesisSourceImages'),
    0x00181500: ('CS', '1', "Positioner Motion", '', 'PositionerMotion'),
    0x00181508: ('CS', '1', "Positioner Type", '', 'PositionerType'),
    0x00181510: ('DS', '1', "Positioner Primary Angle", '', 'PositionerPrimaryAngle'),
    0x00181511: ('DS', '1', "Positioner Secondary Angle", '', 'PositionerSecondaryAngle'),
    0x00181520: ('DS', '1-n', "Positioner Primary Angle Increment", '', 'PositionerPrimaryAngleIncrement'),
    0x00181521: ('DS', '1-n', "Positioner Secondary Angle Increment", '', 'PositionerSecondaryAngleIncrement'),
    0x00181530: ('DS', '1', "Detector Primary Angle", '', 'DetectorPrimaryAngle'),
    0x00181531: ('DS', '1', "Detector Secondary Angle", '', 'DetectorSecondaryAngle'),
    0x00181600: ('CS', '1-3', "Shutter Shape", '', 'ShutterShape'),
    0x00181602: ('IS', '1', "Shutter Left Vertical Edge", '', 'ShutterLeftVerticalEdge'),
    0x00181604: ('IS', '1', "Shutter Right Vertical Edge", '', 'ShutterRightVerticalEdge'),
    0x00181606: ('IS', '1', "Shutter Upper Horizontal Edge", '', 'ShutterUpperHorizontalEdge'),
    0x00181608: ('IS', '1', "Shutter Lower Horizontal Edge", '', 'ShutterLowerHorizontalEdge'),
    0x00181610: ('IS', '2', "Center of Circular Shutter", '', 'CenterOfCircularShutter'),
    0x00181612: ('IS', '1', "Radius of Circular Shutter", '', 'RadiusOfCircularShutter'),
    0x00181620: ('IS', '2-2n', "Vertices of the Polygonal Shutter", '', 'VerticesOfThePolygonalShutter'),
    0x00181622: ('US', '1', "Shutter Presentation Value", '', 'ShutterPresentationValue'),
    0x00181623: ('US', '1', "Shutter Overlay Group", '', 'ShutterOverlayGroup'),
    0x00181624: ('US', '3', "Shutter Presentation Color CIELab Value", '', 'ShutterPresentationColorCIELabValue'),
    0x00181630: ('CS', '1', "Outline Shape Type", '', 'OutlineShapeType'),
    0x00181631: ('FD', '1', "Outline Left Vertical Edge", '', 'OutlineLeftVerticalEdge'),
    0x00181632: ('FD', '1', "Outline Right Vertical Edge", '', 'OutlineRightVerticalEdge'),
    0x00181633: ('FD', '1', "Outline Upper Horizontal Edge", '', 'OutlineUpperHorizontalEdge'),
    0x00181634: ('FD', '1', "Outline Lower Horizontal Edge", '', 'OutlineLowerHorizontalEdge'),
    0x00181635: ('FD', '2', "Center of Circular Outline", '', 'CenterOfCircularOutline'),
    0x00181636: ('FD', '1', "Diameter of Circular Outline", '', 'DiameterOfCircularOutline'),
    0x00181637: ('UL', '1', "Number of Polygonal Vertices", '', 'NumberOfPolygonalVertices'),
    0x00181638: ('OF', '1', "Vertices of the Polygonal Outline", '', 'VerticesOfThePolygonalOutline'),
    0x00181700: ('CS', '1-3', "Collimator Shape", '', 'CollimatorShape'),
    0x00181702: ('IS', '1', "Collimator Left Vertical Edge", '', 'CollimatorLeftVerticalEdge'),
    0x00181704: ('IS', '1', "Collimator Right Vertical Edge", '', 'CollimatorRightVerticalEdge'),
    0x00181706: ('IS', '1', "Collimator Upper Horizontal Edge", '', 'CollimatorUpperHorizontalEdge'),
    0x00181708: ('IS', '1', "Collimator Lower Horizontal Edge", '', 'CollimatorLowerHorizontalEdge'),
    0x00181710: ('IS', '2', "Center of Circular Collimator", '', 'CenterOfCircularCollimator'),
    0x00181712: ('IS', '1', "Radius of Circular Collimator", '', 'RadiusOfCircularCollimator'),
    0x00181720: ('IS', '2-2n', "Vertices of the Polygonal Collimator", '', '********************************'),
    0x00181800: ('CS', '1', "Acquisition Time Synchronized", '', 'AcquisitionTimeSynchronized'),
    0x00181801: ('SH', '1', "Time Source", '', 'TimeSource'),
    0x00181802: ('CS', '1', "Time Distribution Protocol", '', 'TimeDistributionProtocol'),
    0x00181803: ('LO', '1', "NTP Source Address", '', 'NTPSourceAddress'),
    0x00182001: ('IS', '1-n', "Page Number Vector", '', 'PageNumberVector'),
    0x00182002: ('SH', '1-n', "Frame Label Vector", '', 'FrameLabelVector'),
    0x00182003: ('DS', '1-n', "Frame Primary Angle Vector", '', 'FramePrimaryAngleVector'),
    0x00182004: ('DS', '1-n', "Frame Secondary Angle Vector", '', 'FrameSecondaryAngleVector'),
    0x00182005: ('DS', '1-n', "Slice Location Vector", '', 'SliceLocationVector'),
    0x00182006: ('SH', '1-n', "Display Window Label Vector", '', 'DisplayWindowLabelVector'),
    0x00182010: ('DS', '2', "Nominal Scanned Pixel Spacing", '', 'NominalScannedPixelSpacing'),
    0x00182020: ('CS', '1', "Digitizing Device Transport Direction", '', 'DigitizingDeviceTransportDirection'),
    0x00182030: ('DS', '1', "Rotation of Scanned Film", '', 'RotationOfScannedFilm'),
    0x00182041: ('SQ', '1', "Biopsy Target Sequence", '', 'BiopsyTargetSequence'),
    0x00182042: ('UI', '1', "Target UID", '', 'TargetUID'),
    0x00182043: ('FL', '2', "Localizing Cursor Position", '', 'LocalizingCursorPosition'),
    0x00182044: ('FL', '3', "Calculated Target Position", '', 'CalculatedTargetPosition'),
    0x00182045: ('SH', '1', "Target Label", '', 'TargetLabel'),
    0x00182046: ('FL', '1', "Displayed Z Value", '', 'DisplayedZValue'),
    0x00183100: ('CS', '1', "IVUS Acquisition", '', 'IVUSAcquisition'),
    0x00183101: ('DS', '1', "IVUS Pullback Rate", '', 'IVUSPullbackRate'),
    0x00183102: ('DS', '1', "IVUS Gated Rate", '', 'IVUSGatedRate'),
    0x00183103: ('IS', '1', "IVUS Pullback Start Frame Number", '', 'IVUSPullbackStartFrameNumber'),
    0x00183104: ('IS', '1', "IVUS Pullback Stop Frame Number", '', 'IVUSPullbackStopFrameNumber'),
    0x00183105: ('IS', '1-n', "Lesion Number", '', 'LesionNumber'),
    0x00184000: ('LT', '1', "Acquisition Comments", 'Retired', 'AcquisitionComments'),
    0x00185000: ('SH', '1-n', "Output Power", '', 'OutputPower'),
    0x00185010: ('LO', '1-n', "Transducer Data", '', 'TransducerData'),
    0x00185011: ('SQ', '1', "Transducer Identification Sequence", '', 'TransducerIdentificationSequence'),
    0x00185012: ('DS', '1', "Focus Depth", '', 'FocusDepth'),
    0x00185020: ('LO', '1', "Processing Function", '', 'ProcessingFunction'),
    0x00185021: ('LO', '1', "Postprocessing Function", 'Retired', 'PostprocessingFunction'),
    0x00185022: ('DS', '1', "Mechanical Index", '', 'MechanicalIndex'),
    0x00185024: ('DS', '1', "Bone Thermal Index", '', 'BoneThermalIndex'),
    0x00185026: ('DS', '1', "Cranial Thermal Index", '', 'CranialThermalIndex'),
    0x00185027: ('DS', '1', "Soft Tissue Thermal Index", '', 'SoftTissueThermalIndex'),
    0x00185028: ('DS', '1', "Soft Tissue-focus Thermal Index", '', 'SoftTissueFocusThermalIndex'),
    0x00185029: ('DS', '1', "Soft Tissue-surface Thermal Index", '', 'SoftTissueSurfaceThermalIndex'),
    0x00185030: ('DS', '1', "Dynamic Range", 'Retired', 'DynamicRange'),
    0x00185040: ('DS', '1', "Total Gain", 'Retired', 'TotalGain'),
    0x00185050: ('IS', '1', "Depth of Scan Field", '', 'DepthOfScanField'),
    0x00185100: ('CS', '1', "Patient Position", '', 'PatientPosition'),
    0x00185101: ('CS', '1', "View Position", '', 'ViewPosition'),
    0x00185104: ('SQ', '1', "Projection Eponymous Name Code Sequence", '', 'ProjectionEponymousNameCodeSequence'),
    0x00185210: ('DS', '6', "Image Transformation Matrix", 'Retired', 'ImageTransformationMatrix'),
    0x00185212: ('DS', '3', "Image Translation Vector", 'Retired', 'ImageTranslationVector'),
    0x00186000: ('DS', '1', "Sensitivity", '', 'Sensitivity'),
    0x00186011: ('SQ', '1', "Sequence of Ultrasound Regions", '', 'SequenceOfUltrasoundRegions'),
    0x00186012: ('US', '1', "Region Spatial Format", '', 'RegionSpatialFormat'),
    0x00186014: ('US', '1', "Region Data Type", '', 'RegionDataType'),
    0x00186016: ('UL', '1', "Region Flags", '', 'RegionFlags'),
    0x00186018: ('UL', '1', "Region Location Min X0", '', 'RegionLocationMinX0'),
    0x0018601A: ('UL', '1', "Region Location Min Y0", '', 'RegionLocationMinY0'),
    0x0018601C: ('UL', '1', "Region Location Max X1", '', 'RegionLocationMaxX1'),
    0x0018601E: ('UL', '1', "Region Location Max Y1", '', 'RegionLocationMaxY1'),
    0x00186020: ('SL', '1', "Reference Pixel X0", '', 'ReferencePixelX0'),
    0x00186022: ('SL', '1', "Reference Pixel Y0", '', 'ReferencePixelY0'),
    0x00186024: ('US', '1', "Physical Units X Direction", '', 'PhysicalUnitsXDirection'),
    0x00186026: ('US', '1', "Physical Units Y Direction", '', 'PhysicalUnitsYDirection'),
    0x00186028: ('FD', '1', "Reference Pixel Physical Value X", '', 'ReferencePixelPhysicalValueX'),
    0x0018602A: ('FD', '1', "Reference Pixel Physical Value Y", '', 'ReferencePixelPhysicalValueY'),
    0x0018602C: ('FD', '1', "Physical Delta X", '', 'PhysicalDeltaX'),
    0x0018602E: ('FD', '1', "Physical Delta Y", '', 'PhysicalDeltaY'),
    0x00186030: ('UL', '1', "Transducer Frequency", '', 'TransducerFrequency'),
    0x00186031: ('CS', '1', "Transducer Type", '', 'TransducerType'),
    0x00186032: ('UL', '1', "Pulse Repetition Frequency", '', 'PulseRepetitionFrequency'),
    0x00186034: ('FD', '1', "Doppler Correction Angle", '', 'DopplerCorrectionAngle'),
    0x00186036: ('FD', '1', "Steering Angle", '', 'SteeringAngle'),
    0x00186038: ('UL', '1', "Doppler Sample Volume X Position (Retired)", 'Retired', 'DopplerSampleVolumeXPositionRetired'),
    0x00186039: ('SL', '1', "Doppler Sample Volume X Position", '', 'DopplerSampleVolumeXPosition'),
    0x0018603A: ('UL', '1', "Doppler Sample Volume Y Position (Retired)", 'Retired', 'DopplerSampleVolumeYPositionRetired'),
    0x0018603B: ('SL', '1', "Doppler Sample Volume Y Position", '', 'DopplerSampleVolumeYPosition'),
    0x0018603C: ('UL', '1', "TM-Line Position X0 (Retired)", 'Retired', 'TMLinePositionX0Retired'),
    0x0018603D: ('SL', '1', "TM-Line Position X0", '', 'TMLinePositionX0'),
    0x0018603E: ('UL', '1', "TM-Line Position Y0 (Retired)", 'Retired', 'TMLinePositionY0Retired'),
    0x0018603F: ('SL', '1', "TM-Line Position Y0", '', 'TMLinePositionY0'),
    0x00186040: ('UL', '1', "TM-Line Position X1 (Retired)", 'Retired', 'TMLinePositionX1Retired'),
    0x00186041: ('SL', '1', "TM-Line Position X1", '', 'TMLinePositionX1'),
    0x00186042: ('UL', '1', "TM-Line Position Y1 (Retired)", 'Retired', 'TMLinePositionY1Retired'),
    0x00186043: ('SL', '1', "TM-Line Position Y1", '', 'TMLinePositionY1'),
    0x00186044: ('US', '1', "Pixel Component Organization", '', 'PixelComponentOrganization'),
    0x00186046: ('UL', '1', "Pixel Component Mask", '', 'PixelComponentMask'),
    0x00186048: ('UL', '1', "Pixel Component Range Start", '', 'PixelComponentRangeStart'),
    0x0018604A: ('UL', '1', "Pixel Component Range Stop", '', 'PixelComponentRangeStop'),
    0x0018604C: ('US', '1', "Pixel Component Physical Units", '', 'PixelComponentPhysicalUnits'),
    0x0018604E: ('US', '1', "Pixel Component Data Type", '', 'PixelComponentDataType'),
    0x00186050: ('UL', '1', "Number of Table Break Points", '', 'NumberOfTableBreakPoints'),
    0x00186052: ('UL', '1-n', "Table of X Break Points", '', 'TableOfXBreakPoints'),
    0x00186054: ('FD', '1-n', "Table of Y Break Points", '', 'TableOfYBreakPoints'),
    0x00186056: ('UL', '1', "Number of Table Entries", '', 'NumberOfTableEntries'),
    0x00186058: ('UL', '1-n', "Table of Pixel Values", '', 'TableOfPixelValues'),
    0x0018605A: ('FL', '1-n', "Table of Parameter Values", '', 'TableOfParameterValues'),
    0x00186060: ('FL', '1-n', "R Wave Time Vector", '', 'RWaveTimeVector'),
    0x00186070: ('US', '1', "Active Image Area Overlay Group", '', 'ActiveImageAreaOverlayGroup'),
    0x00187000: ('CS', '1', "Detector Conditions Nominal Flag", '', 'DetectorConditionsNominalFlag'),
    0x00187001: ('DS', '1', "Detector Temperature", '', 'DetectorTemperature'),
    0x00187004: ('CS', '1', "Detector Type", '', 'DetectorType'),
    0x00187005: ('CS', '1', "Detector Configuration", '', 'DetectorConfiguration'),
    0x00187006: ('LT', '1', "Detector Description", '', 'DetectorDescription'),
    0x00187008: ('LT', '1', "Detector Mode", '', 'DetectorMode'),
    0x0018700A: ('SH', '1', "Detector ID", '', 'DetectorID'),
    0x0018700C: ('DA', '1', "Date of Last Detector Calibration", '', 'DateOfLastDetectorCalibration'),
    0x0018700E: ('TM', '1', "Time of Last Detector Calibration", '', 'TimeOfLastDetectorCalibration'),
    0x00187010: ('IS', '1', "Exposures on Detector Since Last Calibration", '', 'ExposuresOnDetectorSinceLastCalibration'),
    0x00187011: ('IS', '1', "Exposures on Detector Since Manufactured", '', 'ExposuresOnDetectorSinceManufactured'),
    0x00187012: ('DS', '1', "Detector Time Since Last Exposure", '', 'DetectorTimeSinceLastExposure'),
    0x00187014: ('DS', '1', "Detector Active Time", '', 'DetectorActiveTime'),
    0x00187016: ('DS', '1', "Detector Activation Offset From Exposure", '', 'DetectorActivationOffsetFromExposure'),
    0x0018701A: ('DS', '2', "Detector Binning", '', 'DetectorBinning'),
    0x00187020: ('DS', '2', "Detector Element Physical Size", '', 'DetectorElementPhysicalSize'),
    0x00187022: ('DS', '2', "Detector Element Spacing", '', 'DetectorElementSpacing'),
    0x00187024: ('CS', '1', "Detector Active Shape", '', 'DetectorActiveShape'),
    0x00187026: ('DS', '1-2', "Detector Active Dimension(s)", '', 'DetectorActiveDimensions'),
    0x00187028: ('DS', '2', "Detector Active Origin", '', 'DetectorActiveOrigin'),
    0x0018702A: ('LO', '1', "Detector Manufacturer Name", '', 'DetectorManufacturerName'),
    0x0018702B: ('LO', '1', "Detector Manufacturer's Model Name", '', 'DetectorManufacturerModelName'),
    0x00187030: ('DS', '2', "Field of View Origin", '', 'FieldOfViewOrigin'),
    0x00187032: ('DS', '1', "Field of View Rotation", '', 'FieldOfViewRotation'),
    0x00187034: ('CS', '1', "Field of View Horizontal Flip", '', 'FieldOfViewHorizontalFlip'),
    0x00187036: ('FL', '2', "Pixel Data Area Origin Relative To FOV", '', 'PixelDataAreaOriginRelativeToFOV'),
    0x00187038: ('FL', '1', "Pixel Data Area Rotation Angle Relative To FOV", '', 'PixelDataAreaRotationAngleRelativeToFOV'),
    0x00187040: ('LT', '1', "Grid Absorbing Material", '', 'GridAbsorbingMaterial'),
    0x00187041: ('LT', '1', "Grid Spacing Material", '', 'GridSpacingMaterial'),
    0x00187042: ('DS', '1', "Grid Thickness", '', 'GridThickness'),
    0x00187044: ('DS', '1', "Grid Pitch", '', 'GridPitch'),
    0x00187046: ('IS', '2', "Grid Aspect Ratio", '', 'GridAspectRatio'),
    0x00187048: ('DS', '1', "Grid Period", '', 'GridPeriod'),
    0x0018704C: ('DS', '1', "Grid Focal Distance", '', 'GridFocalDistance'),
    0x00187050: ('CS', '1-n', "Filter Material", '', 'FilterMaterial'),
    0x00187052: ('DS', '1-n', "Filter Thickness Minimum", '', 'FilterThicknessMinimum'),
    0x00187054: ('DS', '1-n', "Filter Thickness Maximum", '', 'FilterThicknessMaximum'),
    0x00187056: ('FL', '1-n', "Filter Beam Path Length Minimum", '', 'FilterBeamPathLengthMinimum'),
    0x00187058: ('FL', '1-n', "Filter Beam Path Length Maximum", '', 'FilterBeamPathLengthMaximum'),
    0x00187060: ('CS', '1', "Exposure Control Mode", '', 'ExposureControlMode'),
    0x00187062: ('LT', '1', "Exposure Control Mode Description", '', 'ExposureControlModeDescription'),
    0x00187064: ('CS', '1', "Exposure Status", '', 'ExposureStatus'),
    0x00187065: ('DS', '1', "Phototimer Setting", '', 'PhototimerSetting'),
    0x00188150: ('DS', '1', "Exposure Time in uS", '', 'ExposureTimeInuS'),
    0x00188151: ('DS', '1', "X-Ray Tube Current in uA", '', 'XRayTubeCurrentInuA'),
    0x00189004: ('CS', '1', "Content Qualification", '', 'ContentQualification'),
    0x00189005: ('SH', '1', "Pulse Sequence Name", '', 'PulseSequenceName'),
    0x00189006: ('SQ', '1', "MR Imaging Modifier Sequence", '', 'MRImagingModifierSequence'),
    0x00189008: ('CS', '1', "Echo Pulse Sequence", '', 'EchoPulseSequence'),
    0x00189009: ('CS', '1', "Inversion Recovery", '', 'InversionRecovery'),
    0x00189010: ('CS', '1', "Flow Compensation", '', 'FlowCompensation'),
    0x00189011: ('CS', '1', "Multiple Spin Echo", '', 'MultipleSpinEcho'),
    0x00189012: ('CS', '1', "Multi-planar Excitation", '', 'MultiPlanarExcitation'),
    0x00189014: ('CS', '1', "Phase Contrast", '', 'PhaseContrast'),
    0x00189015: ('CS', '1', "Time of Flight Contrast", '', 'TimeOfFlightContrast'),
    0x00189016: ('CS', '1', "Spoiling", '', 'Spoiling'),
    0x00189017: ('CS', '1', "Steady State Pulse Sequence", '', 'SteadyStatePulseSequence'),
    0x00189018: ('CS', '1', "Echo Planar Pulse Sequence", '', 'EchoPlanarPulseSequence'),
    0x00189019: ('FD', '1', "Tag Angle First Axis", '', 'TagAngleFirstAxis'),
    0x00189020: ('CS', '1', "Magnetization Transfer", '', 'MagnetizationTransfer'),
    0x00189021: ('CS', '1', "T2 Preparation", '', 'T2Preparation'),
    0x00189022: ('CS', '1', "Blood Signal Nulling", '', 'BloodSignalNulling'),
    0x00189024: ('CS', '1', "Saturation Recovery", '', 'SaturationRecovery'),
    0x00189025: ('CS', '1', "Spectrally Selected Suppression", '', 'SpectrallySelectedSuppression'),
    0x00189026: ('CS', '1', "Spectrally Selected Excitation", '', 'SpectrallySelectedExcitation'),
    0x00189027: ('CS', '1', "Spatial Pre-saturation", '', 'SpatialPresaturation'),
    0x00189028: ('CS', '1', "Tagging", '', 'Tagging'),
    0x00189029: ('CS', '1', "Oversampling Phase", '', 'OversamplingPhase'),
    0x00189030: ('FD', '1', "Tag Spacing First Dimension", '', 'TagSpacingFirstDimension'),
    0x00189032: ('CS', '1', "Geometry of k-Space Traversal", '', 'GeometryOfKSpaceTraversal'),
    0x00189033: ('CS', '1', "Segmented k-Space Traversal", '', 'SegmentedKSpaceTraversal'),
    0x00189034: ('CS', '1', "Rectilinear Phase Encode Reordering", '', 'RectilinearPhaseEncodeReordering'),
    0x00189035: ('FD', '1', "Tag Thickness", '', 'TagThickness'),
    0x00189036: ('CS', '1', "Partial Fourier Direction", '', 'PartialFourierDirection'),
    0x00189037: ('CS', '1', "Cardiac Synchronization Technique", '', 'CardiacSynchronizationTechnique'),
    0x00189041: ('LO', '1', "Receive Coil Manufacturer Name", '', 'ReceiveCoilManufacturerName'),
    0x00189042: ('SQ', '1', "MR Receive Coil Sequence", '', 'MRReceiveCoilSequence'),
    0x00189043: ('CS', '1', "Receive Coil Type", '', 'ReceiveCoilType'),
    0x00189044: ('CS', '1', "Quadrature Receive Coil", '', 'QuadratureReceiveCoil'),
    0x00189045: ('SQ', '1', "Multi-Coil Definition Sequence", '', 'MultiCoilDefinitionSequence'),
    0x00189046: ('LO', '1', "Multi-Coil Configuration", '', 'MultiCoilConfiguration'),
    0x00189047: ('SH', '1', "Multi-Coil Element Name", '', 'MultiCoilElementName'),
    0x00189048: ('CS', '1', "Multi-Coil Element Used", '', 'MultiCoilElementUsed'),
    0x00189049: ('SQ', '1', "MR Transmit Coil Sequence", '', 'MRTransmitCoilSequence'),
    0x00189050: ('LO', '1', "Transmit Coil Manufacturer Name", '', 'TransmitCoilManufacturerName'),
    0x00189051: ('CS', '1', "Transmit Coil Type", '', 'TransmitCoilType'),
    0x00189052: ('FD', '1-2', "Spectral Width", '', 'SpectralWidth'),
    0x00189053: ('FD', '1-2', "Chemical Shift Reference", '', 'ChemicalShiftReference'),
    0x00189054: ('CS', '1', "Volume Localization Technique", '', 'VolumeLocalizationTechnique'),
    0x00189058: ('US', '1', "MR Acquisition Frequency Encoding Steps", '', 'MRAcquisitionFrequencyEncodingSteps'),
    0x00189059: ('CS', '1', "De-coupling", '', 'Decoupling'),
    0x00189060: ('CS', '1-2', "De-coupled Nucleus", '', 'DecoupledNucleus'),
    0x00189061: ('FD', '1-2', "De-coupling Frequency", '', 'DecouplingFrequency'),
    0x00189062: ('CS', '1', "De-coupling Method", '', 'DecouplingMethod'),
    0x00189063: ('FD', '1-2', "De-coupling Chemical Shift Reference", '', 'DecouplingChemicalShiftReference'),
    0x00189064: ('CS', '1', "k-space Filtering", '', 'KSpaceFiltering'),
    0x00189065: ('CS', '1-2', "Time Domain Filtering", '', 'TimeDomainFiltering'),
    0x00189066: ('US', '1-2', "Number of Zero Fills", '', 'NumberOfZeroFills'),
    0x00189067: ('CS', '1', "Baseline Correction", '', 'BaselineCorrection'),
    0x00189069: ('FD', '1', "Parallel Reduction Factor In-plane", '', 'ParallelReductionFactorInPlane'),
    0x00189070: ('FD', '1', "Cardiac R-R Interval Specified", '', 'CardiacRRIntervalSpecified'),
    0x00189073: ('FD', '1', "Acquisition Duration", '', 'AcquisitionDuration'),
    0x00189074: ('DT', '1', "Frame Acquisition DateTime", '', 'FrameAcquisitionDateTime'),
    0x00189075: ('CS', '1', "Diffusion Directionality", '', 'DiffusionDirectionality'),
    0x00189076: ('SQ', '1', "Diffusion Gradient Direction Sequence", '', 'DiffusionGradientDirectionSequence'),
    0x00189077: ('CS', '1', "Parallel Acquisition", '', 'ParallelAcquisition'),
    0x00189078: ('CS', '1', "Parallel Acquisition Technique", '', 'ParallelAcquisitionTechnique'),
    0x00189079: ('FD', '1-n', "Inversion Times", '', 'InversionTimes'),
    0x00189080: ('ST', '1', "Metabolite Map Description", '', 'MetaboliteMapDescription'),
    0x00189081: ('CS', '1', "Partial Fourier", '', 'PartialFourier'),
    0x00189082: ('FD', '1', "Effective Echo Time", '', 'EffectiveEchoTime'),
    0x00189083: ('SQ', '1', "Metabolite Map Code Sequence", '', 'MetaboliteMapCodeSequence'),
    0x00189084: ('SQ', '1', "Chemical Shift Sequence", '', 'ChemicalShiftSequence'),
    0x00189085: ('CS', '1', "Cardiac Signal Source", '', 'CardiacSignalSource'),
    0x00189087: ('FD', '1', "Diffusion b-value", '', 'DiffusionBValue'),
    0x00189089: ('FD', '3', "Diffusion Gradient Orientation", '', 'DiffusionGradientOrientation'),
    0x00189090: ('FD', '3', "Velocity Encoding Direction", '', 'VelocityEncodingDirection'),
    0x00189091: ('FD', '1', "Velocity Encoding Minimum Value", '', 'VelocityEncodingMinimumValue'),
    0x00189092: ('SQ', '1', "Velocity Encoding Acquisition Sequence", '', 'VelocityEncodingAcquisitionSequence'),
    0x00189093: ('US', '1', "Number of k-Space Trajectories", '', 'NumberOfKSpaceTrajectories'),
    0x00189094: ('CS', '1', "Coverage of k-Space", '', 'CoverageOfKSpace'),
    0x00189095: ('UL', '1', "Spectroscopy Acquisition Phase Rows", '', 'SpectroscopyAcquisitionPhaseRows'),
    0x00189096: ('FD', '1', "Parallel Reduction Factor In-plane (Retired)", 'Retired', 'ParallelReductionFactorInPlaneRetired'),
    0x00189098: ('FD', '1-2', "Transmitter Frequency", '', 'TransmitterFrequency'),
    0x00189100: ('CS', '1-2', "Resonant Nucleus", '', 'ResonantNucleus'),
    0x00189101: ('CS', '1', "Frequency Correction", '', 'FrequencyCorrection'),
    0x00189103: ('SQ', '1', "MR Spectroscopy FOV/Geometry Sequence", '', 'MRSpectroscopyFOVGeometrySequence'),
    0x00189104: ('FD', '1', "Slab Thickness", '', 'SlabThickness'),
    0x00189105: ('FD', '3', "Slab Orientation", '', 'SlabOrientation'),
    0x00189106: ('FD', '3', "Mid Slab Position", '', 'MidSlabPosition'),
    0x00189107: ('SQ', '1', "MR Spatial Saturation Sequence", '', 'MRSpatialSaturationSequence'),
    0x00189112: ('SQ', '1', "MR Timing and Related Parameters Sequence", '', 'MRTimingAndRelatedParametersSequence'),
    0x00189114: ('SQ', '1', "MR Echo Sequence", '', 'MREchoSequence'),
    0x00189115: ('SQ', '1', "MR Modifier Sequence", '', 'MRModifierSequence'),
    0x00189117: ('SQ', '1', "MR Diffusion Sequence", '', 'MRDiffusionSequence'),
    0x00189118: ('SQ', '1', "Cardiac Synchronization Sequence", '', 'CardiacSynchronizationSequence'),
    0x00189119: ('SQ', '1', "MR Averages Sequence", '', 'MRAveragesSequence'),
    0x00189125: ('SQ', '1', "MR FOV/Geometry Sequence", '', 'MRFOVGeometrySequence'),
    0x00189126: ('SQ', '1', "Volume Localization Sequence", '', 'VolumeLocalizationSequence'),
    0x00189127: ('UL', '1', "Spectroscopy Acquisition Data Columns", '', 'SpectroscopyAcquisitionDataColumns'),
    0x00189147: ('CS', '1', "Diffusion Anisotropy Type", '', 'DiffusionAnisotropyType'),
    0x00189151: ('DT', '1', "Frame Reference DateTime", '', 'FrameReferenceDateTime'),
    0x00189152: ('SQ', '1', "MR Metabolite Map Sequence", '', 'MRMetaboliteMapSequence'),
    0x00189155: ('FD', '1', "Parallel Reduction Factor out-of-plane", '', 'ParallelReductionFactorOutOfPlane'),
    0x00189159: ('UL', '1', "Spectroscopy Acquisition Out-of-plane Phase Steps", '', 'SpectroscopyAcquisitionOutOfPlanePhaseSteps'),
    0x00189166: ('CS', '1', "Bulk Motion Status", 'Retired', 'BulkMotionStatus'),
    0x00189168: ('FD', '1', "Parallel Reduction Factor Second In-plane", '', 'ParallelReductionFactorSecondInPlane'),
    0x00189169: ('CS', '1', "Cardiac Beat Rejection Technique", '', 'CardiacBeatRejectionTechnique'),
    0x00189170: ('CS', '1', "Respiratory Motion Compensation Technique", '', 'RespiratoryMotionCompensationTechnique'),
    0x00189171: ('CS', '1', "Respiratory Signal Source", '', 'RespiratorySignalSource'),
    0x00189172: ('CS', '1', "Bulk Motion Compensation Technique", '', 'BulkMotionCompensationTechnique'),
    0x00189173: ('CS', '1', "Bulk Motion Signal Source", '', 'BulkMotionSignalSource'),
    0x00189174: ('CS', '1', "Applicable Safety Standard Agency", '', 'ApplicableSafetyStandardAgency'),
    0x00189175: ('LO', '1', "Applicable Safety Standard Description", '', 'ApplicableSafetyStandardDescription'),
    0x00189176: ('SQ', '1', "Operating Mode Sequence", '', 'OperatingModeSequence'),
    0x00189177: ('CS', '1', "Operating Mode Type", '', 'OperatingModeType'),
    0x00189178: ('CS', '1', "Operating Mode", '', 'OperatingMode'),
    0x00189179: ('CS', '1', "Specific Absorption Rate Definition", '', 'SpecificAbsorptionRateDefinition'),
    0x00189180: ('CS', '1', "Gradient Output Type", '', 'GradientOutputType'),
    0x00189181: ('FD', '1', "Specific Absorption Rate Value", '', 'SpecificAbsorptionRateValue'),
    0x00189182: ('FD', '1', "Gradient Output", '', 'GradientOutput'),
    0x00189183: ('CS', '1', "Flow Compensation Direction", '', 'FlowCompensationDirection'),
    0x00189184: ('FD', '1', "Tagging Delay", '', 'TaggingDelay'),
    0x00189185: ('ST', '1', "Respiratory Motion Compensation Technique Description", '', 'RespiratoryMotionCompensationTechniqueDescription'),
    0x00189186: ('SH', '1', "Respiratory Signal Source ID", '', 'RespiratorySignalSourceID'),
    0x00189195: ('FD', '1', "Chemical Shift Minimum Integration Limit in Hz", 'Retired', 'ChemicalShiftMinimumIntegrationLimitInHz'),
    0x00189196: ('FD', '1', "Chemical Shift Maximum Integration Limit in Hz", 'Retired', 'ChemicalShiftMaximumIntegrationLimitInHz'),
    0x00189197: ('SQ', '1', "MR Velocity Encoding Sequence", '', 'MRVelocityEncodingSequence'),
    0x00189198: ('CS', '1', "First Order Phase Correction", '', 'FirstOrderPhaseCorrection'),
    0x00189199: ('CS', '1', "Water Referenced Phase Correction", '', 'WaterReferencedPhaseCorrection'),
    0x00189200: ('CS', '1', "MR Spectroscopy Acquisition Type", '', 'MRSpectroscopyAcquisitionType'),
    0x00189214: ('CS', '1', "Respiratory Cycle Position", '', 'RespiratoryCyclePosition'),
    0x00189217: ('FD', '1', "Velocity Encoding Maximum Value", '', 'VelocityEncodingMaximumValue'),
    0x00189218: ('FD', '1', "Tag Spacing Second Dimension", '', 'TagSpacingSecondDimension'),
    0x00189219: ('SS', '1', "Tag Angle Second Axis", '', 'TagAngleSecondAxis'),
    0x00189220: ('FD', '1', "Frame Acquisition Duration", '', 'FrameAcquisitionDuration'),
    0x00189226: ('SQ', '1', "MR Image Frame Type Sequence", '', 'MRImageFrameTypeSequence'),
    0x00189227: ('SQ', '1', "MR Spectroscopy Frame Type Sequence", '', 'MRSpectroscopyFrameTypeSequence'),
    0x00189231: ('US', '1', "MR Acquisition Phase Encoding Steps in-plane", '', 'MRAcquisitionPhaseEncodingStepsInPlane'),
    0x00189232: ('US', '1', "MR Acquisition Phase Encoding Steps out-of-plane", '', 'MRAcquisitionPhaseEncodingStepsOutOfPlane'),
    0x00189234: ('UL', '1', "Spectroscopy Acquisition Phase Columns", '', 'SpectroscopyAcquisitionPhaseColumns'),
    0x00189236: ('CS', '1', "Cardiac Cycle Position", '', 'CardiacCyclePosition'),
    0x00189239: ('SQ', '1', "Specific Absorption Rate Sequence", '', 'SpecificAbsorptionRateSequence'),
    0x00189240: ('US', '1', "RF Echo Train Length", '', 'RFEchoTrainLength'),
    0x00189241: ('US', '1', "Gradient Echo Train Length", '', 'GradientEchoTrainLength'),
    0x00189250: ('CS', '1', "Arterial Spin Labeling Contrast", '', 'ArterialSpinLabelingContrast'),
    0x00189251: ('SQ', '1', "MR Arterial Spin Labeling Sequence", '', 'MRArterialSpinLabelingSequence'),
    0x00189252: ('LO', '1', "ASL Technique Description", '', 'ASLTechniqueDescription'),
    0x00189253: ('US', '1', "ASL Slab Number", '', 'ASLSlabNumber'),
    0x00189254: ('FD', '1', "ASL Slab Thickness", '', 'ASLSlabThickness'),
    0x00189255: ('FD', '3', "ASL Slab Orientation", '', 'ASLSlabOrientation'),
    0x00189256: ('FD', '3', "ASL Mid Slab Position", '', 'ASLMidSlabPosition'),
    0x00189257: ('CS', '1', "ASL Context", '', 'ASLContext'),
    0x00189258: ('UL', '1', "ASL Pulse Train Duration", '', 'ASLPulseTrainDuration'),
    0x00189259: ('CS', '1', "ASL Crusher Flag", '', 'ASLCrusherFlag'),
    0x0018925A: ('FD', '1', "ASL Crusher Flow Limit", '', 'ASLCrusherFlowLimit'),
    0x0018925B: ('LO', '1', "ASL Crusher Description", '', 'ASLCrusherDescription'),
    0x0018925C: ('CS', '1', "ASL Bolus Cut-off Flag", '', 'ASLBolusCutoffFlag'),
    0x0018925D: ('SQ', '1', "ASL Bolus Cut-off Timing Sequence", '', 'ASLBolusCutoffTimingSequence'),
    0x0018925E: ('LO', '1', "ASL Bolus Cut-off Technique", '', 'ASLBolusCutoffTechnique'),
    0x0018925F: ('UL', '1', "ASL Bolus Cut-off Delay Time", '', 'ASLBolusCutoffDelayTime'),
    0x00189260: ('SQ', '1', "ASL Slab Sequence", '', 'ASLSlabSequence'),
    0x00189295: ('FD', '1', "Chemical Shift Minimum Integration Limit in ppm", '', 'ChemicalShiftMinimumIntegrationLimitInppm'),
    0x00189296: ('FD', '1', "Chemical Shift Maximum Integration Limit in ppm", '', 'ChemicalShiftMaximumIntegrationLimitInppm'),
    0x00189297: ('CS', '1', "Water Reference Acquisition", '', 'WaterReferenceAcquisition'),
    0x00189298: ('IS', '1', "Echo Peak Position", '', 'EchoPeakPosition'),
    0x00189301: ('SQ', '1', "CT Acquisition Type Sequence", '', 'CTAcquisitionTypeSequence'),
    0x00189302: ('CS', '1', "Acquisition Type", '', 'AcquisitionType'),
    0x00189303: ('FD', '1', "Tube Angle", '', 'TubeAngle'),
    0x00189304: ('SQ', '1', "CT Acquisition Details Sequence", '', 'CTAcquisitionDetailsSequence'),
    0x00189305: ('FD', '1', "Revolution Time", '', 'RevolutionTime'),
    0x00189306: ('FD', '1', "Single Collimation Width", '', 'SingleCollimationWidth'),
    0x00189307: ('FD', '1', "Total Collimation Width", '', 'TotalCollimationWidth'),
    0x00189308: ('SQ', '1', "CT Table Dynamics Sequence", '', 'CTTableDynamicsSequence'),
    0x00189309: ('FD', '1', "Table Speed", '', 'TableSpeed'),
    0x00189310: ('FD', '1', "Table Feed per Rotation", '', 'TableFeedPerRotation'),
    0x00189311: ('FD', '1', "Spiral Pitch Factor", '', 'SpiralPitchFactor'),
    0x00189312: ('SQ', '1', "CT Geometry Sequence", '', 'CTGeometrySequence'),
    0x00189313: ('FD', '3', "Data Collection Center (Patient)", '', 'DataCollectionCenterPatient'),
    0x00189314: ('SQ', '1', "CT Reconstruction Sequence", '', 'CTReconstructionSequence'),
    0x00189315: ('CS', '1', "Reconstruction Algorithm", '', 'ReconstructionAlgorithm'),
    0x00189316: ('CS', '1', "Convolution Kernel Group", '', 'ConvolutionKernelGroup'),
    0x00189317: ('FD', '2', "Reconstruction Field of View", '', 'ReconstructionFieldOfView'),
    0x00189318: ('FD', '3', "Reconstruction Target Center (Patient)", '', 'ReconstructionTargetCenterPatient'),
    0x00189319: ('FD', '1', "Reconstruction Angle", '', 'ReconstructionAngle'),
    0x00189320: ('SH', '1', "Image Filter", '', 'ImageFilter'),
    0x00189321: ('SQ', '1', "CT Exposure Sequence", '', 'CTExposureSequence'),
    0x00189322: ('FD', '2', "Reconstruction Pixel Spacing", '', 'ReconstructionPixelSpacing'),
    0x00189323: ('CS', '1-n', "Exposure Modulation Type", '', 'ExposureModulationType'),
    0x00189324: ('FD', '1', "Estimated Dose Saving", 'Retired', 'EstimatedDoseSaving'),
    0x00189325: ('SQ', '1', "CT X-Ray Details Sequence", '', 'CTXRayDetailsSequence'),
    0x00189326: ('SQ', '1', "CT Position Sequence", '', 'CTPositionSequence'),
    0x00189327: ('FD', '1', "Table Position", '', 'TablePosition'),
    0x00189328: ('FD', '1', "Exposure Time in ms", '', 'ExposureTimeInms'),
    0x00189329: ('SQ', '1', "CT Image Frame Type Sequence", '', 'CTImageFrameTypeSequence'),
    0x00189330: ('FD', '1', "X-Ray Tube Current in mA", '', 'XRayTubeCurrentInmA'),
    0x00189332: ('FD', '1', "Exposure in mAs", '', 'ExposureInmAs'),
    0x00189333: ('CS', '1', "Constant Volume Flag", '', 'ConstantVolumeFlag'),
    0x00189334: ('CS', '1', "Fluoroscopy Flag", '', 'FluoroscopyFlag'),
    0x00189335: ('FD', '1', "Distance Source to Data Collection Center", '', 'DistanceSourceToDataCollectionCenter'),
    0x00189337: ('US', '1', "Contrast/Bolus Agent Number", '', 'ContrastBolusAgentNumber'),
    0x00189338: ('SQ', '1', "Contrast/Bolus Ingredient Code Sequence", '', 'ContrastBolusIngredientCodeSequence'),
    0x00189340: ('SQ', '1', "Contrast Administration Profile Sequence", '', 'ContrastAdministrationProfileSequence'),
    0x00189341: ('SQ', '1', "Contrast/Bolus Usage Sequence", '', 'ContrastBolusUsageSequence'),
    0x00189342: ('CS', '1', "Contrast/Bolus Agent Administered", '', 'ContrastBolusAgentAdministered'),
    0x00189343: ('CS', '1', "Contrast/Bolus Agent Detected", '', 'ContrastBolusAgentDetected'),
    0x00189344: ('CS', '1', "Contrast/Bolus Agent Phase", '', 'ContrastBolusAgentPhase'),
    0x00189345: ('FD', '1', "CTDIvol", '', 'CTDIvol'),
    0x00189346: ('SQ', '1', "CTDI Phantom Type Code Sequence", '', 'CTDIPhantomTypeCodeSequence'),
    0x00189351: ('FL', '1', "Calcium Scoring Mass Factor Patient", '', 'CalciumScoringMassFactorPatient'),
    0x00189352: ('FL', '3', "Calcium Scoring Mass Factor Device", '', 'CalciumScoringMassFactorDevice'),
    0x00189353: ('FL', '1', "Energy Weighting Factor", '', 'EnergyWeightingFactor'),
    0x00189360: ('SQ', '1', "CT Additional X-Ray Source Sequence", '', 'CTAdditionalXRaySourceSequence'),
    0x00189361: ('CS', '1', "Multi-energy CT Acquisition", '', 'MultienergyCTAcquisition'),
    0x00189362: ('SQ', '1', "Multi-energy CT Acquisition Sequence", '', 'MultienergyCTAcquisitionSequence'),
    0x00189363: ('SQ', '1', "Multi-energy CT Processing Sequence", '', 'MultienergyCTProcessingSequence'),
    0x00189364: ('SQ', '1', "Multi-energy CT Characteristics Sequence", '', 'MultienergyCTCharacteristicsSequence'),
    0x00189365: ('SQ', '1', "Multi-energy CT X-Ray Source Sequence", '', 'MultienergyCTXRaySourceSequence'),
    0x00189366: ('US', '1', "X-Ray Source Index", '', 'XRaySourceIndex'),
    0x00189367: ('UC', '1', "X-Ray Source ID", '', 'XRaySourceID'),
    0x00189368: ('CS', '1', "Multi-energy Source Technique", '', 'MultienergySourceTechnique'),
    0x00189369: ('DT', '1', "Source Start DateTime", '', 'SourceStartDateTime'),
    0x0018936A: ('DT', '1', "Source End DateTime", '', 'SourceEndDateTime'),
    0x0018936B: ('US', '1', "Switching Phase Number", '', 'SwitchingPhaseNumber'),
    0x0018936C: ('DS', '1', "Switching Phase Nominal Duration", '', 'SwitchingPhaseNominalDuration'),
    0x0018936D: ('DS', '1', "Switching Phase Transition Duration", '', 'SwitchingPhaseTransitionDuration'),
    0x0018936E: ('DS', '1', "Effective Bin Energy", '', 'EffectiveBinEnergy'),
    0x0018936F: ('SQ', '1', "Multi-energy CT X-Ray Detector Sequence", '', 'MultienergyCTXRayDetectorSequence'),
    0x00189370: ('US', '1', "X-Ray Detector Index", '', 'XRayDetectorIndex'),
    0x00189371: ('UC', '1', "X-Ray Detector ID", '', 'XRayDetectorID'),
    0x00189372: ('CS', '1', "Multi-energy Detector Type", '', 'MultienergyDetectorType'),
    0x00189373: ('ST', '1', "X-Ray Detector Label", '', 'XRayDetectorLabel'),
    0x00189374: ('DS', '1', "Nominal Max Energy", '', 'NominalMaxEnergy'),
    0x00189375: ('DS', '1', "Nominal Min Energy", '', 'NominalMinEnergy'),
    0x00189376: ('US', '1-n', "Referenced X-Ray Detector Index", '', 'ReferencedXRayDetectorIndex'),
    0x00189377: ('US', '1-n', "Referenced X-Ray Source Index", '', 'ReferencedXRaySourceIndex'),
    0x00189378: ('US', '1-n', "Referenced Path Index", '', 'ReferencedPathIndex'),
    0x00189379: ('SQ', '1', "Multi-energy CT Path Sequence", '', 'MultienergyCTPathSequence'),
    0x0018937A: ('US', '1', "Multi-energy CT Path Index", '', 'MultienergyCTPathIndex'),
    0x0018937B: ('UT', '1', "Multi-energy Acquisition Description", '', 'MultienergyAcquisitionDescription'),
    0x0018937C: ('FD', '1', "Monoenergetic Energy Equivalent", '', 'MonoenergeticEnergyEquivalent'),
    0x0018937D: ('SQ', '1', "Material Code Sequence", '', 'MaterialCodeSequence'),
    0x0018937E: ('CS', '1', "Decomposition Method", '', 'DecompositionMethod'),
    0x0018937F: ('UT', '1', "Decomposition Description", '', 'DecompositionDescription'),
    0x00189380: ('SQ', '1', "Decomposition Algorithm Identification Sequence", '', 'DecompositionAlgorithmIdentificationSequence'),
    0x00189381: ('SQ', '1', "Decomposition Material Sequence", '', 'DecompositionMaterialSequence'),
    0x00189382: ('SQ', '1', "Material Attenuation Sequence", '', 'MaterialAttenuationSequence'),
    0x00189383: ('DS', '1', "Photon Energy", '', 'PhotonEnergy'),
    0x00189384: ('DS', '1', "X-Ray Mass Attenuation Coefficient", '', 'XRayMassAttenuationCoefficient'),
    0x00189401: ('SQ', '1', "Projection Pixel Calibration Sequence", '', 'ProjectionPixelCalibrationSequence'),
    0x00189402: ('FL', '1', "Distance Source to Isocenter", '', 'DistanceSourceToIsocenter'),
    0x00189403: ('FL', '1', "Distance Object to Table Top", '', 'DistanceObjectToTableTop'),
    0x00189404: ('FL', '2', "Object Pixel Spacing in Center of Beam", '', 'ObjectPixelSpacingInCenterOfBeam'),
    0x00189405: ('SQ', '1', "Positioner Position Sequence", '', 'PositionerPositionSequence'),
    0x00189406: ('SQ', '1', "Table Position Sequence", '', 'TablePositionSequence'),
    0x00189407: ('SQ', '1', "Collimator Shape Sequence", '', 'CollimatorShapeSequence'),
    0x00189410: ('CS', '1', "Planes in Acquisition", '', 'PlanesInAcquisition'),
    0x00189412: ('SQ', '1', "XA/XRF Frame Characteristics Sequence", '', 'XAXRFFrameCharacteristicsSequence'),
    0x00189417: ('SQ', '1', "Frame Acquisition Sequence", '', 'FrameAcquisitionSequence'),
    0x00189420: ('CS', '1', "X-Ray Receptor Type", '', 'XRayReceptorType'),
    0x00189423: ('LO', '1', "Acquisition Protocol Name", '', 'AcquisitionProtocolName'),
    0x00189424: ('LT', '1', "Acquisition Protocol Description", '', 'AcquisitionProtocolDescription'),
    0x00189425: ('CS', '1', "Contrast/Bolus Ingredient Opaque", '', 'ContrastBolusIngredientOpaque'),
    0x00189426: ('FL', '1', "Distance Receptor Plane to Detector Housing", '', 'DistanceReceptorPlaneToDetectorHousing'),
    0x00189427: ('CS', '1', "Intensifier Active Shape", '', 'IntensifierActiveShape'),
    0x00189428: ('FL', '1-2', "Intensifier Active Dimension(s)", '', 'IntensifierActiveDimensions'),
    0x00189429: ('FL', '2', "Physical Detector Size", '', 'PhysicalDetectorSize'),
    0x00189430: ('FL', '2', "Position of Isocenter Projection", '', 'PositionOfIsocenterProjection'),
    0x00189432: ('SQ', '1', "Field of View Sequence", '', 'FieldOfViewSequence'),
    0x00189433: ('LO', '1', "Field of View Description", '', 'FieldOfViewDescription'),
    0x00189434: ('SQ', '1', "Exposure Control Sensing Regions Sequence", '', 'ExposureControlSensingRegionsSequence'),
    0x00189435: ('CS', '1', "Exposure Control Sensing Region Shape", '', 'ExposureControlSensingRegionShape'),
    0x00189436: ('SS', '1', "Exposure Control Sensing Region Left Vertical Edge", '', 'ExposureControlSensingRegionLeftVerticalEdge'),
    0x00189437: ('SS', '1', "Exposure Control Sensing Region Right Vertical Edge", '', 'ExposureControlSensingRegionRightVerticalEdge'),
    0x00189438: ('SS', '1', "Exposure Control Sensing Region Upper Horizontal Edge", '', 'ExposureControlSensingRegionUpperHorizontalEdge'),
    0x00189439: ('SS', '1', "Exposure Control Sensing Region Lower Horizontal Edge", '', 'ExposureControlSensingRegionLowerHorizontalEdge'),
    0x00189440: ('SS', '2', "Center of Circular Exposure Control Sensing Region", '', 'CenterOfCircularExposureControlSensingRegion'),
    0x00189441: ('US', '1', "Radius of Circular Exposure Control Sensing Region", '', 'RadiusOfCircularExposureControlSensingRegion'),
    0x00189442: ('SS', '2-n', "Vertices of the Polygonal Exposure Control Sensing Region", '', 'VerticesOfThePolygonalExposureControlSensingRegion'),
    0x00189445: ('OB', '1', "Retired-blank", 'Retired', ''),
    0x00189447: ('FL', '1', "Column Angulation (Patient)", '', 'ColumnAngulationPatient'),
    0x00189449: ('FL', '1', "Beam Angle", '', 'BeamAngle'),
    0x00189451: ('SQ', '1', "Frame Detector Parameters Sequence", '', 'FrameDetectorParametersSequence'),
    0x00189452: ('FL', '1', "Calculated Anatomy Thickness", '', 'CalculatedAnatomyThickness'),
    0x00189455: ('SQ', '1', "Calibration Sequence", '', 'CalibrationSequence'),
    0x00189456: ('SQ', '1', "Object Thickness Sequence", '', 'ObjectThicknessSequence'),
    0x00189457: ('CS', '1', "Plane Identification", '', 'PlaneIdentification'),
    0x00189461: ('FL', '1-2', "Field of View Dimension(s) in Float", '', 'FieldOfViewDimensionsInFloat'),
    0x00189462: ('SQ', '1', "Isocenter Reference System Sequence", '', 'IsocenterReferenceSystemSequence'),
    0x00189463: ('FL', '1', "Positioner Isocenter Primary Angle", '', 'PositionerIsocenterPrimaryAngle'),
    0x00189464: ('FL', '1', "Positioner Isocenter Secondary Angle", '', 'PositionerIsocenterSecondaryAngle'),
    0x00189465: ('FL', '1', "Positioner Isocenter Detector Rotation Angle", '', 'PositionerIsocenterDetectorRotationAngle'),
    0x00189466: ('FL', '1', "Table X Position to Isocenter", '', 'TableXPositionToIsocenter'),
    0x00189467: ('FL', '1', "Table Y Position to Isocenter", '', 'TableYPositionToIsocenter'),
    0x00189468: ('FL', '1', "Table Z Position to Isocenter", '', 'TableZPositionToIsocenter'),
    0x00189469: ('FL', '1', "Table Horizontal Rotation Angle", '', 'TableHorizontalRotationAngle'),
    0x00189470: ('FL', '1', "Table Head Tilt Angle", '', 'TableHeadTiltAngle'),
    0x00189471: ('FL', '1', "Table Cradle Tilt Angle", '', 'TableCradleTiltAngle'),
    0x00189472: ('SQ', '1', "Frame Display Shutter Sequence", '', 'FrameDisplayShutterSequence'),
    0x00189473: ('FL', '1', "Acquired Image Area Dose Product", '', 'AcquiredImageAreaDoseProduct'),
    0x00189474: ('CS', '1', "C-arm Positioner Tabletop Relationship", '', 'CArmPositionerTabletopRelationship'),
    0x00189476: ('SQ', '1', "X-Ray Geometry Sequence", '', 'XRayGeometrySequence'),
    0x00189477: ('SQ', '1', "Irradiation Event Identification Sequence", '', 'IrradiationEventIdentificationSequence'),
    0x00189504: ('SQ', '1', "X-Ray 3D Frame Type Sequence", '', 'XRay3DFrameTypeSequence'),
    0x00189506: ('SQ', '1', "Contributing Sources Sequence", '', 'ContributingSourcesSequence'),
    0x00189507: ('SQ', '1', "X-Ray 3D Acquisition Sequence", '', 'XRay3DAcquisitionSequence'),
    0x00189508: ('FL', '1', "Primary Positioner Scan Arc", '', 'PrimaryPositionerScanArc'),
    0x00189509: ('FL', '1', "Secondary Positioner Scan Arc", '', 'SecondaryPositionerScanArc'),
    0x00189510: ('FL', '1', "Primary Positioner Scan Start Angle", '', 'PrimaryPositionerScanStartAngle'),
    0x00189511: ('FL', '1', "Secondary Positioner Scan Start Angle", '', 'SecondaryPositionerScanStartAngle'),
    0x00189514: ('FL', '1', "Primary Positioner Increment", '', 'PrimaryPositionerIncrement'),
    0x00189515: ('FL', '1', "Secondary Positioner Increment", '', 'SecondaryPositionerIncrement'),
    0x00189516: ('DT', '1', "Start Acquisition DateTime", '', 'StartAcquisitionDateTime'),
    0x00189517: ('DT', '1', "End Acquisition DateTime", '', 'EndAcquisitionDateTime'),
    0x00189518: ('SS', '1', "Primary Positioner Increment Sign", '', 'PrimaryPositionerIncrementSign'),
    0x00189519: ('SS', '1', "Secondary Positioner Increment Sign", '', 'SecondaryPositionerIncrementSign'),
    0x00189524: ('LO', '1', "Application Name", '', 'ApplicationName'),
    0x00189525: ('LO', '1', "Application Version", '', 'ApplicationVersion'),
    0x00189526: ('LO', '1', "Application Manufacturer", '', 'ApplicationManufacturer'),
    0x00189527: ('CS', '1', "Algorithm Type", '', 'AlgorithmType'),
    0x00189528: ('LO', '1', "Algorithm Description", '', 'AlgorithmDescription'),
    0x00189530: ('SQ', '1', "X-Ray 3D Reconstruction Sequence", '', 'XRay3DReconstructionSequence'),
    0x00189531: ('LO', '1', "Reconstruction Description", '', 'ReconstructionDescription'),
    0x00189538: ('SQ', '1', "Per Projection Acquisition Sequence", '', 'PerProjectionAcquisitionSequence'),
    0x00189541: ('SQ', '1', "Detector Position Sequence", '', 'DetectorPositionSequence'),
    0x00189542: ('SQ', '1', "X-Ray Acquisition Dose Sequence", '', 'XRayAcquisitionDoseSequence'),
    0x00189543: ('FD', '1', "X-Ray Source Isocenter Primary Angle", '', 'XRaySourceIsocenterPrimaryAngle'),
    0x00189544: ('FD', '1', "X-Ray Source Isocenter Secondary Angle", '', 'XRaySourceIsocenterSecondaryAngle'),
    0x00189545: ('FD', '1', "Breast Support Isocenter Primary Angle", '', 'BreastSupportIsocenterPrimaryAngle'),
    0x00189546: ('FD', '1', "Breast Support Isocenter Secondary Angle", '', 'BreastSupportIsocenterSecondaryAngle'),
    0x00189547: ('FD', '1', "Breast Support X Position to Isocenter", '', 'BreastSupportXPositionToIsocenter'),
    0x00189548: ('FD', '1', "Breast Support Y Position to Isocenter", '', 'BreastSupportYPositionToIsocenter'),
    0x00189549: ('FD', '1', "Breast Support Z Position to Isocenter", '', 'BreastSupportZPositionToIsocenter'),
    0x00189550: ('FD', '1', "Detector Isocenter Primary Angle", '', 'DetectorIsocenterPrimaryAngle'),
    0x00189551: ('FD', '1', "Detector Isocenter Secondary Angle", '', 'DetectorIsocenterSecondaryAngle'),
    0x00189552: ('FD', '1', "Detector X Position to Isocenter", '', 'DetectorXPositionToIsocenter'),
    0x00189553: ('FD', '1', "Detector Y Position to Isocenter", '', 'DetectorYPositionToIsocenter'),
    0x00189554: ('FD', '1', "Detector Z Position to Isocenter", '', 'DetectorZPositionToIsocenter'),
    0x00189555: ('SQ', '1', "X-Ray Grid Sequence", '', 'XRayGridSequence'),
    0x00189556: ('SQ', '1', "X-Ray Filter Sequence", '', 'XRayFilterSequence'),
    0x00189557: ('FD', '3', "Detector Active Area TLHC Position", '', 'DetectorActiveAreaTLHCPosition'),
    0x00189558: ('FD', '6', "Detector Active Area Orientation", '', 'DetectorActiveAreaOrientation'),
    0x00189559: ('CS', '1', "Positioner Primary Angle Direction", '', 'PositionerPrimaryAngleDirection'),
    0x00189601: ('SQ', '1', "Diffusion b-matrix Sequence", '', 'DiffusionBMatrixSequence'),
    0x00189602: ('FD', '1', "Diffusion b-value XX", '', 'DiffusionBValueXX'),
    0x00189603: ('FD', '1', "Diffusion b-value XY", '', 'DiffusionBValueXY'),
    0x00189604: ('FD', '1', "Diffusion b-value XZ", '', 'DiffusionBValueXZ'),
    0x00189605: ('FD', '1', "Diffusion b-value YY", '', 'DiffusionBValueYY'),
    0x00189606: ('FD', '1', "Diffusion b-value YZ", '', 'DiffusionBValueYZ'),
    0x00189607: ('FD', '1', "Diffusion b-value ZZ", '', 'DiffusionBValueZZ'),
    0x00189621: ('SQ', '1', "Functional MR Sequence", '', 'FunctionalMRSequence'),
    0x00189622: ('CS', '1', "Functional Settling Phase Frames Present", '', 'FunctionalSettlingPhaseFramesPresent'),
    0x00189623: ('DT', '1', "Functional Sync Pulse", '', 'FunctionalSyncPulse'),
    0x00189624: ('CS', '1', "Settling Phase Frame", '', 'SettlingPhaseFrame'),
    0x00189701: ('DT', '1', "Decay Correction DateTime", '', 'DecayCorrectionDateTime'),
    0x00189715: ('FD', '1', "Start Density Threshold", '', 'StartDensityThreshold'),
    0x00189716: ('FD', '1', "Start Relative Density Difference Threshold", '', 'StartRelativeDensityDifferenceThreshold'),
    0x00189717: ('FD', '1', "Start Cardiac Trigger Count Threshold", '', 'StartCardiacTriggerCountThreshold'),
    0x00189718: ('FD', '1', "Start Respiratory Trigger Count Threshold", '', 'StartRespiratoryTriggerCountThreshold'),
    0x00189719: ('FD', '1', "Termination Counts Threshold", '', 'TerminationCountsThreshold'),
    0x00189720: ('FD', '1', "Termination Density Threshold", '', 'TerminationDensityThreshold'),
    0x00189721: ('FD', '1', "Termination Relative Density Threshold", '', 'TerminationRelativeDensityThreshold'),
    0x00189722: ('FD', '1', "Termination Time Threshold", '', 'TerminationTimeThreshold'),
    0x00189723: ('FD', '1', "Termination Cardiac Trigger Count Threshold", '', 'TerminationCardiacTriggerCountThreshold'),
    0x00189724: ('FD', '1', "Termination Respiratory Trigger Count Threshold", '', 'TerminationRespiratoryTriggerCountThreshold'),
    0x00189725: ('CS', '1', "Detector Geometry", '', 'DetectorGeometry'),
    0x00189726: ('FD', '1', "Transverse Detector Separation", '', 'TransverseDetectorSeparation'),
    0x00189727: ('FD', '1', "Axial Detector Dimension", '', 'AxialDetectorDimension'),
    0x00189729: ('US', '1', "Radiopharmaceutical Agent Number", '', 'RadiopharmaceuticalAgentNumber'),
    0x00189732: ('SQ', '1', "PET Frame Acquisition Sequence", '', 'PETFrameAcquisitionSequence'),
    0x00189733: ('SQ', '1', "PET Detector Motion Details Sequence", '', 'PETDetectorMotionDetailsSequence'),
    0x00189734: ('SQ', '1', "PET Table Dynamics Sequence", '', 'PETTableDynamicsSequence'),
    0x00189735: ('SQ', '1', "PET Position Sequence", '', 'PETPositionSequence'),
    0x00189736: ('SQ', '1', "PET Frame Correction Factors Sequence", '', 'PETFrameCorrectionFactorsSequence'),
    0x00189737: ('SQ', '1', "Radiopharmaceutical Usage Sequence", '', 'RadiopharmaceuticalUsageSequence'),
    0x00189738: ('CS', '1', "Attenuation Correction Source", '', 'AttenuationCorrectionSource'),
    0x00189739: ('US', '1', "Number of Iterations", '', 'NumberOfIterations'),
    0x00189740: ('US', '1', "Number of Subsets", '', 'NumberOfSubsets'),
    0x00189749: ('SQ', '1', "PET Reconstruction Sequence", '', 'PETReconstructionSequence'),
    0x00189751: ('SQ', '1', "PET Frame Type Sequence", '', 'PETFrameTypeSequence'),
    0x00189755: ('CS', '1', "Time of Flight Information Used", '', 'TimeOfFlightInformationUsed'),
    0x00189756: ('CS', '1', "Reconstruction Type", '', 'ReconstructionType'),
    0x00189758: ('CS', '1', "Decay Corrected", '', 'DecayCorrected'),
    0x00189759: ('CS', '1', "Attenuation Corrected", '', 'AttenuationCorrected'),
    0x00189760: ('CS', '1', "Scatter Corrected", '', 'ScatterCorrected'),
    0x00189761: ('CS', '1', "Dead Time Corrected", '', 'DeadTimeCorrected'),
    0x00189762: ('CS', '1', "Gantry Motion Corrected", '', 'GantryMotionCorrected'),
    0x00189763: ('CS', '1', "Patient Motion Corrected", '', 'PatientMotionCorrected'),
    0x00189764: ('CS', '1', "Count Loss Normalization Corrected", '', 'CountLossNormalizationCorrected'),
    0x00189765: ('CS', '1', "Randoms Corrected", '', 'RandomsCorrected'),
    0x00189766: ('CS', '1', "Non-uniform Radial Sampling Corrected", '', 'NonUniformRadialSamplingCorrected'),
    0x00189767: ('CS', '1', "Sensitivity Calibrated", '', 'SensitivityCalibrated'),
    0x00189768: ('CS', '1', "Detector Normalization Correction", '', 'DetectorNormalizationCorrection'),
    0x00189769: ('CS', '1', "Iterative Reconstruction Method", '', 'IterativeReconstructionMethod'),
    0x00189770: ('CS', '1', "Attenuation Correction Temporal Relationship", '', 'AttenuationCorrectionTemporalRelationship'),
    0x00189771: ('SQ', '1', "Patient Physiological State Sequence", '', 'PatientPhysiologicalStateSequence'),
    0x00189772: ('SQ', '1', "Patient Physiological State Code Sequence", '', 'PatientPhysiologicalStateCodeSequence'),
    0x00189801: ('FD', '1-n', "Depth(s) of Focus", '', 'DepthsOfFocus'),
    0x00189803: ('SQ', '1', "Excluded Intervals Sequence", '', 'ExcludedIntervalsSequence'),
    0x00189804: ('DT', '1', "Exclusion Start DateTime", '', 'ExclusionStartDateTime'),
    0x00189805: ('FD', '1', "Exclusion Duration", '', 'ExclusionDuration'),
    0x00189806: ('SQ', '1', "US Image Description Sequence", '', 'USImageDescriptionSequence'),
    0x00189807: ('SQ', '1', "Image Data Type Sequence", '', 'ImageDataTypeSequence'),
    0x00189808: ('CS', '1', "Data Type", '', 'DataType'),
    0x00189809: ('SQ', '1', "Transducer Scan Pattern Code Sequence", '', 'TransducerScanPatternCodeSequence'),
    0x0018980B: ('CS', '1', "Aliased Data Type", '', 'AliasedDataType'),
    0x0018980C: ('CS', '1', "Position Measuring Device Used", '', 'PositionMeasuringDeviceUsed'),
    0x0018980D: ('SQ', '1', "Transducer Geometry Code Sequence", '', 'TransducerGeometryCodeSequence'),
    0x0018980E: ('SQ', '1', "Transducer Beam Steering Code Sequence", '', 'TransducerBeamSteeringCodeSequence'),
    0x0018980F: ('SQ', '1', "Transducer Application Code Sequence", '', 'TransducerApplicationCodeSequence'),
    0x00189810: ('US or SS', '1', "Zero Velocity Pixel Value", '', 'ZeroVelocityPixelValue'),
    0x00189821: ('SQ', '1', "Photoacoustic Excitation Characteristics Sequence", '', 'PhotoacousticExcitationCharacteristicsSequence'),
    0x00189822: ('FD', '1', "Excitation Spectral Width", '', 'ExcitationSpectralWidth'),
    0x00189823: ('FD', '1', "Excitation Energy", '', 'ExcitationEnergy'),
    0x00189824: ('FD', '1', "Excitation Pulse Duration", '', 'ExcitationPulseDuration'),
    0x00189825: ('SQ', '1', "Excitation Wavelength Sequence", '', 'ExcitationWavelengthSequence'),
    0x00189826: ('FD', '1', "Excitation Wavelength", '', 'ExcitationWavelength'),
    0x00189828: ('CS', '1', "Illumination Translation Flag", '', 'IlluminationTranslationFlag'),
    0x00189829: ('CS', '1', "Acoustic Coupling Medium Flag", '', 'AcousticCouplingMediumFlag'),
    0x0018982A: ('SQ', '1', "Acoustic Coupling Medium Code Sequence", '', 'AcousticCouplingMediumCodeSequence'),
    0x0018982B: ('FD', '1', "Acoustic Coupling Medium Temperature", '', 'AcousticCouplingMediumTemperature'),
    0x0018982C: ('SQ', '1', "Transducer Response Sequence", '', 'TransducerResponseSequence'),
    0x0018982D: ('FD', '1', "Center Frequency", '', 'CenterFrequency'),
    0x0018982E: ('FD', '1', "Fractional Bandwidth", '', 'FractionalBandwidth'),
    0x0018982F: ('FD', '1', "Lower Cutoff Frequency", '', 'LowerCutoffFrequency'),
    0x00189830: ('FD', '1', "Upper Cutoff Frequency", '', 'UpperCutoffFrequency'),
    0x00189831: ('SQ', '1', "Transducer Technology Sequence", '', 'TransducerTechnologySequence'),
    0x00189832: ('SQ', '1', "Sound Speed Correction Mechanism Code Sequence", '', 'SoundSpeedCorrectionMechanismCodeSequence'),
    0x00189833: ('FD', '1', "Object Sound Speed", '', 'ObjectSoundSpeed'),
    0x00189834: ('FD', '1', "Acoustic Coupling Medium Sound Speed", '', 'AcousticCouplingMediumSoundSpeed'),
    0x00189835: ('SQ', '1', "Photoacoustic Image Frame Type Sequence", '', 'PhotoacousticImageFrameTypeSequence'),
    0x00189836: ('SQ', '1', "Image Data Type Code Sequence", '', 'ImageDataTypeCodeSequence'),
    0x00189900: ('LO', '1', "Reference Location Label", '', 'ReferenceLocationLabel'),
    0x00189901: ('UT', '1', "Reference Location Description", '', 'ReferenceLocationDescription'),
    0x00189902: ('SQ', '1', "Reference Basis Code Sequence", '', 'ReferenceBasisCodeSequence'),
    0x00189903: ('SQ', '1', "Reference Geometry Code Sequence", '', 'ReferenceGeometryCodeSequence'),
    0x00189904: ('DS', '1', "Offset Distance", '', 'OffsetDistance'),
    0x00189905: ('CS', '1', "Offset Direction", '', 'OffsetDirection'),
    0x00189906: ('SQ', '1', "Potential Scheduled Protocol Code Sequence", '', 'PotentialScheduledProtocolCodeSequence'),
    0x00189907: ('SQ', '1', "Potential Requested Procedure Code Sequence", '', 'PotentialRequestedProcedureCodeSequence'),
    0x00189908: ('UC', '1-n', "Potential Reasons for Procedure", '', 'PotentialReasonsForProcedure'),
    0x00189909: ('SQ', '1', "Potential Reasons for Procedure Code Sequence", '', 'PotentialReasonsForProcedureCodeSequence'),
    0x0018990A: ('UC', '1-n', "Potential Diagnostic Tasks", '', 'PotentialDiagnosticTasks'),
    0x0018990B: ('SQ', '1', "Contraindications Code Sequence", '', 'ContraindicationsCodeSequence'),
    0x0018990C: ('SQ', '1', "Referenced Defined Protocol Sequence", '', 'ReferencedDefinedProtocolSequence'),
    0x0018990D: ('SQ', '1', "Referenced Performed Protocol Sequence", '', 'ReferencedPerformedProtocolSequence'),
    0x0018990E: ('SQ', '1', "Predecessor Protocol Sequence", '', 'PredecessorProtocolSequence'),
    0x0018990F: ('UT', '1', "Protocol Planning Information", '', 'ProtocolPlanningInformation'),
    0x00189910: ('UT', '1', "Protocol Design Rationale", '', 'ProtocolDesignRationale'),
    0x00189911: ('SQ', '1', "Patient Specification Sequence", '', 'PatientSpecificationSequence'),
    0x00189912: ('SQ', '1', "Model Specification Sequence", '', 'ModelSpecificationSequence'),
    0x00189913: ('SQ', '1', "Parameters Specification Sequence", '', 'ParametersSpecificationSequence'),
    0x00189914: ('SQ', '1', "Instruction Sequence", '', 'InstructionSequence'),
    0x00189915: ('US', '1', "Instruction Index", '', 'InstructionIndex'),
    0x00189916: ('LO', '1', "Instruction Text", '', 'InstructionText'),
    0x00189917: ('UT', '1', "Instruction Description", '', 'InstructionDescription'),
    0x00189918: ('CS', '1', "Instruction Performed Flag", '', 'InstructionPerformedFlag'),
    0x00189919: ('DT', '1', "Instruction Performed DateTime", '', 'InstructionPerformedDateTime'),
    0x0018991A: ('UT', '1', "Instruction Performance Comment", '', 'InstructionPerformanceComment'),
    0x0018991B: ('SQ', '1', "Patient Positioning Instruction Sequence", '', 'PatientPositioningInstructionSequence'),
    0x0018991C: ('SQ', '1', "Positioning Method Code Sequence", '', 'PositioningMethodCodeSequence'),
    0x0018991D: ('SQ', '1', "Positioning Landmark Sequence", '', 'PositioningLandmarkSequence'),
    0x0018991E: ('UI', '1', "Target Frame of Reference UID", '', 'TargetFrameOfReferenceUID'),
    0x0018991F: ('SQ', '1', "Acquisition Protocol Element Specification Sequence", '', 'AcquisitionProtocolElementSpecificationSequence'),
    0x00189920: ('SQ', '1', "Acquisition Protocol Element Sequence", '', 'AcquisitionProtocolElementSequence'),
    0x00189921: ('US', '1', "Protocol Element Number", '', 'ProtocolElementNumber'),
    0x00189922: ('LO', '1', "Protocol Element Name", '', 'ProtocolElementName'),
    0x00189923: ('UT', '1', "Protocol Element Characteristics Summary", '', 'ProtocolElementCharacteristicsSummary'),
    0x00189924: ('UT', '1', "Protocol Element Purpose", '', 'ProtocolElementPurpose'),
    0x00189930: ('CS', '1', "Acquisition Motion", '', 'AcquisitionMotion'),
    0x00189931: ('SQ', '1', "Acquisition Start Location Sequence", '', 'AcquisitionStartLocationSequence'),
    0x00189932: ('SQ', '1', "Acquisition End Location Sequence", '', 'AcquisitionEndLocationSequence'),
    0x00189933: ('SQ', '1', "Reconstruction Protocol Element Specification Sequence", '', 'ReconstructionProtocolElementSpecificationSequence'),
    0x00189934: ('SQ', '1', "Reconstruction Protocol Element Sequence", '', 'ReconstructionProtocolElementSequence'),
    0x00189935: ('SQ', '1', "Storage Protocol Element Specification Sequence", '', 'StorageProtocolElementSpecificationSequence'),
    0x00189936: ('SQ', '1', "Storage Protocol Element Sequence", '', 'StorageProtocolElementSequence'),
    0x00189937: ('LO', '1', "Requested Series Description", '', 'RequestedSeriesDescription'),
    0x00189938: ('US', '1-n', "Source Acquisition Protocol Element Number", '', 'SourceAcquisitionProtocolElementNumber'),
    0x00189939: ('US', '1-n', "Source Acquisition Beam Number", '', 'SourceAcquisitionBeamNumber'),
    0x0018993A: ('US', '1-n', "Source Reconstruction Protocol Element Number", '', 'SourceReconstructionProtocolElementNumber'),
    0x0018993B: ('SQ', '1', "Reconstruction Start Location Sequence", '', 'ReconstructionStartLocationSequence'),
    0x0018993C: ('SQ', '1', "Reconstruction End Location Sequence", '', 'ReconstructionEndLocationSequence'),
    0x0018993D: ('SQ', '1', "Reconstruction Algorithm Sequence", '', 'ReconstructionAlgorithmSequence'),
    0x0018993E: ('SQ', '1', "Reconstruction Target Center Location Sequence", '', 'ReconstructionTargetCenterLocationSequence'),
    0x00189941: ('UT', '1', "Image Filter Description", '', 'ImageFilterDescription'),
    0x00189942: ('FD', '1', "CTDIvol Notification Trigger", '', 'CTDIvolNotificationTrigger'),
    0x00189943: ('FD', '1', "DLP Notification Trigger", '', 'DLPNotificationTrigger'),
    0x00189944: ('CS', '1', "Auto KVP Selection Type", '', 'AutoKVPSelectionType'),
    0x00189945: ('FD', '1', "Auto KVP Upper Bound", '', 'AutoKVPUpperBound'),
    0x00189946: ('FD', '1', "Auto KVP Lower Bound", '', 'AutoKVPLowerBound'),
    0x00189947: ('CS', '1', "Protocol Defined Patient Position", '', 'ProtocolDefinedPatientPosition'),
    0x0018A001: ('SQ', '1', "Contributing Equipment Sequence", '', 'ContributingEquipmentSequence'),
    0x0018A002: ('DT', '1', "Contribution DateTime", '', 'ContributionDateTime'),
    0x0018A003: ('ST', '1', "Contribution Description", '', 'ContributionDescription'),
    0x0020000D: ('UI', '1', "Study Instance UID", '', 'StudyInstanceUID'),
    0x0020000E: ('UI', '1', "Series Instance UID", '', 'SeriesInstanceUID'),
    0x00200010: ('SH', '1', "Study ID", '', 'StudyID'),
    0x00200011: ('IS', '1', "Series Number", '', 'SeriesNumber'),
    0x00200012: ('IS', '1', "Acquisition Number", '', 'AcquisitionNumber'),
    0x00200013: ('IS', '1', "Instance Number", '', 'InstanceNumber'),
    0x00200014: ('IS', '1', "Isotope Number", 'Retired', 'IsotopeNumber'),
    0x00200015: ('IS', '1', "Phase Number", 'Retired', 'PhaseNumber'),
    0x00200016: ('IS', '1', "Interval Number", 'Retired', 'IntervalNumber'),
    0x00200017: ('IS', '1', "Time Slot Number", 'Retired', 'TimeSlotNumber'),
    0x00200018: ('IS', '1', "Angle Number", 'Retired', 'AngleNumber'),
    0x00200019: ('IS', '1', "Item Number", '', 'ItemNumber'),
    0x00200020: ('CS', '2', "Patient Orientation", '', 'PatientOrientation'),
    0x00200022: ('IS', '1', "Overlay Number", 'Retired', 'OverlayNumber'),
    0x00200024: ('IS', '1', "Curve Number", 'Retired', 'CurveNumber'),
    0x00200026: ('IS', '1', "LUT Number", 'Retired', 'LUTNumber'),
    0x00200027: ('LO', '1', "Pyramid Label", '', 'PyramidLabel'),
    0x00200030: ('DS', '3', "Image Position", 'Retired', 'ImagePosition'),
    0x00200032: ('DS', '3', "Image Position (Patient)", '', 'ImagePositionPatient'),
    0x00200035: ('DS', '6', "Image Orientation", 'Retired', 'ImageOrientation'),
    0x00200037: ('DS', '6', "Image Orientation (Patient)", '', 'ImageOrientationPatient'),
    0x00200050: ('DS', '1', "Location", 'Retired', 'Location'),
    0x00200052: ('UI', '1', "Frame of Reference UID", '', 'FrameOfReferenceUID'),
    0x00200060: ('CS', '1', "Laterality", '', 'Laterality'),
    0x00200062: ('CS', '1', "Image Laterality", '', 'ImageLaterality'),
    0x00200070: ('LO', '1', "Image Geometry Type", 'Retired', 'ImageGeometryType'),
    0x00200080: ('CS', '1-n', "Masking Image", 'Retired', 'MaskingImage'),
    0x002000AA: ('IS', '1', "Report Number", 'Retired', 'ReportNumber'),
    0x00200100: ('IS', '1', "Temporal Position Identifier", '', 'TemporalPositionIdentifier'),
    0x00200105: ('IS', '1', "Number of Temporal Positions", '', 'NumberOfTemporalPositions'),
    0x00200110: ('DS', '1', "Temporal Resolution", '', 'TemporalResolution'),
    0x00200200: ('UI', '1', "Synchronization Frame of Reference UID", '', 'SynchronizationFrameOfReferenceUID'),
    0x00200242: ('UI', '1', "SOP Instance UID of Concatenation Source", '', 'SOPInstanceUIDOfConcatenationSource'),
    0x00201000: ('IS', '1', "Series in Study", 'Retired', 'SeriesInStudy'),
    0x00201001: ('IS', '1', "Acquisitions in Series", 'Retired', 'AcquisitionsInSeries'),
    0x00201002: ('IS', '1', "Images in Acquisition", '', 'ImagesInAcquisition'),
    0x00201003: ('IS', '1', "Images in Series", 'Retired', 'ImagesInSeries'),
    0x00201004: ('IS', '1', "Acquisitions in Study", 'Retired', 'AcquisitionsInStudy'),
    0x00201005: ('IS', '1', "Images in Study", 'Retired', 'ImagesInStudy'),
    0x00201020: ('LO', '1-n', "Reference", 'Retired', 'Reference'),
    0x0020103F: ('LO', '1', "Target Position Reference Indicator", '', 'TargetPositionReferenceIndicator'),
    0x00201040: ('LO', '1', "Position Reference Indicator", '', 'PositionReferenceIndicator'),
    0x00201041: ('DS', '1', "Slice Location", '', 'SliceLocation'),
    0x00201070: ('IS', '1-n', "Other Study Numbers", 'Retired', 'OtherStudyNumbers'),
    0x00201200: ('IS', '1', "Number of Patient Related Studies", '', 'NumberOfPatientRelatedStudies'),
    0x00201202: ('IS', '1', "Number of Patient Related Series", '', 'NumberOfPatientRelatedSeries'),
    0x00201204: ('IS', '1', "Number of Patient Related Instances", '', 'NumberOfPatientRelatedInstances'),
    0x00201206: ('IS', '1', "Number of Study Related Series", '', 'NumberOfStudyRelatedSeries'),
    0x00201208: ('IS', '1', "Number of Study Related Instances", '', 'NumberOfStudyRelatedInstances'),
    0x00201209: ('IS', '1', "Number of Series Related Instances", '', 'NumberOfSeriesRelatedInstances'),
    0x00203401: ('CS', '1', "Modifying Device ID", 'Retired', 'ModifyingDeviceID'),
    0x00203402: ('CS', '1', "Modified Image ID", 'Retired', 'ModifiedImageID'),
    0x00203403: ('DA', '1', "Modified Image Date", 'Retired', 'ModifiedImageDate'),
    0x00203404: ('LO', '1', "Modifying Device Manufacturer", 'Retired', 'ModifyingDeviceManufacturer'),
    0x00203405: ('TM', '1', "Modified Image Time", 'Retired', 'ModifiedImageTime'),
    0x00203406: ('LO', '1', "Modified Image Description", 'Retired', 'ModifiedImageDescription'),
    0x00204000: ('LT', '1', "Image Comments", '', 'ImageComments'),
    0x00205000: ('AT', '1-n', "Original Image Identification", 'Retired', 'OriginalImageIdentification'),
    0x00205002: ('LO', '1-n', "Original Image Identification Nomenclature", 'Retired', 'OriginalImageIdentificationNomenclature'),
    0x00209056: ('SH', '1', "Stack ID", '', 'StackID'),
    0x00209057: ('UL', '1', "In-Stack Position Number", '', 'InStackPositionNumber'),
    0x00209071: ('SQ', '1', "Frame Anatomy Sequence", '', 'FrameAnatomySequence'),
    0x00209072: ('CS', '1', "Frame Laterality", '', 'FrameLaterality'),
    0x00209111: ('SQ', '1', "Frame Content Sequence", '', 'FrameContentSequence'),
    0x00209113: ('SQ', '1', "Plane Position Sequence", '', 'PlanePositionSequence'),
    0x00209116: ('SQ', '1', "Plane Orientation Sequence", '', 'PlaneOrientationSequence'),
    0x00209128: ('UL', '1', "Temporal Position Index", '', 'TemporalPositionIndex'),
    0x00209153: ('FD', '1', "Nominal Cardiac Trigger Delay Time", '', 'NominalCardiacTriggerDelayTime'),
    0x00209154: ('FL', '1', "Nominal Cardiac Trigger Time Prior To R-Peak", '', 'NominalCardiacTriggerTimePriorToRPeak'),
    0x00209155: ('FL', '1', "Actual Cardiac Trigger Time Prior To R-Peak", '', 'ActualCardiacTriggerTimePriorToRPeak'),
    0x00209156: ('US', '1', "Frame Acquisition Number", '', 'FrameAcquisitionNumber'),
    0x00209157: ('UL', '1-n', "Dimension Index Values", '', 'DimensionIndexValues'),
    0x00209158: ('LT', '1', "Frame Comments", '', 'FrameComments'),
    0x00209161: ('UI', '1', "Concatenation UID", '', 'ConcatenationUID'),
    0x00209162: ('US', '1', "In-concatenation Number", '', 'InConcatenationNumber'),
    0x00209163: ('US', '1', "In-concatenation Total Number", '', 'InConcatenationTotalNumber'),
    0x00209164: ('UI', '1', "Dimension Organization UID", '', 'DimensionOrganizationUID'),
    0x00209165: ('AT', '1', "Dimension Index Pointer", '', 'DimensionIndexPointer'),
    0x00209167: ('AT', '1', "Functional Group Pointer", '', 'FunctionalGroupPointer'),
    0x00209170: ('SQ', '1', "Unassigned Shared Converted Attributes Sequence", '', 'UnassignedSharedConvertedAttributesSequence'),
    0x00209171: ('SQ', '1', "Unassigned Per-Frame Converted Attributes Sequence", '', 'UnassignedPerFrameConvertedAttributesSequence'),
    0x00209172: ('SQ', '1', "Conversion Source Attributes Sequence", '', 'ConversionSourceAttributesSequence'),
    0x00209213: ('LO', '1', "Dimension Index Private Creator", '', 'DimensionIndexPrivateCreator'),
    0x00209221: ('SQ', '1', "Dimension Organization Sequence", '', 'DimensionOrganizationSequence'),
    0x00209222: ('SQ', '1', "Dimension Index Sequence", '', 'DimensionIndexSequence'),
    0x00209228: ('UL', '1', "Concatenation Frame Offset Number", '', 'ConcatenationFrameOffsetNumber'),
    0x00209238: ('LO', '1', "Functional Group Private Creator", '', 'FunctionalGroupPrivateCreator'),
    0x00209241: ('FL', '1', "Nominal Percentage of Cardiac Phase", '', 'NominalPercentageOfCardiacPhase'),
    0x00209245: ('FL', '1', "Nominal Percentage of Respiratory Phase", '', 'NominalPercentageOfRespiratoryPhase'),
    0x00209246: ('FL', '1', "Starting Respiratory Amplitude", '', 'StartingRespiratoryAmplitude'),
    0x00209247: ('CS', '1', "Starting Respiratory Phase", '', 'StartingRespiratoryPhase'),
    0x00209248: ('FL', '1', "Ending Respiratory Amplitude", '', 'EndingRespiratoryAmplitude'),
    0x00209249: ('CS', '1', "Ending Respiratory Phase", '', 'EndingRespiratoryPhase'),
    0x00209250: ('CS', '1', "Respiratory Trigger Type", '', 'RespiratoryTriggerType'),
    0x00209251: ('FD', '1', "R-R Interval Time Nominal", '', 'RRIntervalTimeNominal'),
    0x00209252: ('FD', '1', "Actual Cardiac Trigger Delay Time", '', 'ActualCardiacTriggerDelayTime'),
    0x00209253: ('SQ', '1', "Respiratory Synchronization Sequence", '', 'RespiratorySynchronizationSequence'),
    0x00209254: ('FD', '1', "Respiratory Interval Time", '', 'RespiratoryIntervalTime'),
    0x00209255: ('FD', '1', "Nominal Respiratory Trigger Delay Time", '', 'NominalRespiratoryTriggerDelayTime'),
    0x00209256: ('FD', '1', "Respiratory Trigger Delay Threshold", '', 'RespiratoryTriggerDelayThreshold'),
    0x00209257: ('FD', '1', "Actual Respiratory Trigger Delay Time", '', 'ActualRespiratoryTriggerDelayTime'),
    0x00209301: ('FD', '3', "Image Position (Volume)", '', 'ImagePositionVolume'),
    0x00209302: ('FD', '6', "Image Orientation (Volume)", '', 'ImageOrientationVolume'),
    0x00209307: ('CS', '1', "Ultrasound Acquisition Geometry", '', 'UltrasoundAcquisitionGeometry'),
    0x00209308: ('FD', '3', "Apex Position", '', 'ApexPosition'),
    0x00209309: ('FD', '16', "Volume to Transducer Mapping Matrix", '', 'VolumeToTransducerMappingMatrix'),
    0x0020930A: ('FD', '16', "Volume to Table Mapping Matrix", '', 'VolumeToTableMappingMatrix'),
    0x0020930B: ('CS', '1', "Volume to Transducer Relationship", '', 'VolumeToTransducerRelationship'),
    0x0020930C: ('CS', '1', "Patient Frame of Reference Source", '', 'PatientFrameOfReferenceSource'),
    0x0020930D: ('FD', '1', "Temporal Position Time Offset", '', 'TemporalPositionTimeOffset'),
    0x0020930E: ('SQ', '1', "Plane Position (Volume) Sequence", '', 'PlanePositionVolumeSequence'),
    0x0020930F: ('SQ', '1', "Plane Orientation (Volume) Sequence", '', 'PlaneOrientationVolumeSequence'),
    0x00209310: ('SQ', '1', "Temporal Position Sequence", '', 'TemporalPositionSequence'),
    0x00209311: ('CS', '1', "Dimension Organization Type", '', 'DimensionOrganizationType'),
    0x00209312: ('UI', '1', "Volume Frame of Reference UID", '', 'VolumeFrameOfReferenceUID'),
    0x00209313: ('UI', '1', "Table Frame of Reference UID", '', 'TableFrameOfReferenceUID'),
    0x00209421: ('LO', '1', "Dimension Description Label", '', 'DimensionDescriptionLabel'),
    0x00209450: ('SQ', '1', "Patient Orientation in Frame Sequence", '', 'PatientOrientationInFrameSequence'),
    0x00209453: ('LO', '1', "Frame Label", '', 'FrameLabel'),
    0x00209518: ('US', '1-n', "Acquisition Index", '', 'AcquisitionIndex'),
    0x00209529: ('SQ', '1', "Contributing SOP Instances Reference Sequence", '', 'ContributingSOPInstancesReferenceSequence'),
    0x00209536: ('US', '1', "Reconstruction Index", '', 'ReconstructionIndex'),
    0x00220001: ('US', '1', "Light Path Filter Pass-Through Wavelength", '', 'LightPathFilterPassThroughWavelength'),
    0x00220002: ('US', '2', "Light Path Filter Pass Band", '', 'LightPathFilterPassBand'),
    0x00220003: ('US', '1', "Image Path Filter Pass-Through Wavelength", '', 'ImagePathFilterPassThroughWavelength'),
    0x00220004: ('US', '2', "Image Path Filter Pass Band", '', 'ImagePathFilterPassBand'),
    0x00220005: ('CS', '1', "Patient Eye Movement Commanded", '', 'PatientEyeMovementCommanded'),
    0x00220006: ('SQ', '1', "Patient Eye Movement Command Code Sequence", '', 'PatientEyeMovementCommandCodeSequence'),
    0x00220007: ('FL', '1', "Spherical Lens Power", '', 'SphericalLensPower'),
    0x00220008: ('FL', '1', "Cylinder Lens Power", '', 'CylinderLensPower'),
    0x00220009: ('FL', '1', "Cylinder Axis", '', 'CylinderAxis'),
    0x0022000A: ('FL', '1', "Emmetropic Magnification", '', 'EmmetropicMagnification'),
    0x0022000B: ('FL', '1', "Intra Ocular Pressure", '', 'IntraOcularPressure'),
    0x0022000C: ('FL', '1', "Horizontal Field of View", '', 'HorizontalFieldOfView'),
    0x0022000D: ('CS', '1', "Pupil Dilated", '', 'PupilDilated'),
    0x0022000E: ('FL', '1', "Degree of Dilation", '', 'DegreeOfDilation'),
    0x0022000F: ('FD', '1', "Vertex Distance", '', 'VertexDistance'),
    0x00220010: ('FL', '1', "Stereo Baseline Angle", '', 'StereoBaselineAngle'),
    0x00220011: ('FL', '1', "Stereo Baseline Displacement", '', 'StereoBaselineDisplacement'),
    0x00220012: ('FL', '1', "Stereo Horizontal Pixel Offset", '', 'StereoHorizontalPixelOffset'),
    0x00220013: ('FL', '1', "Stereo Vertical Pixel Offset", '', 'StereoVerticalPixelOffset'),
    0x00220014: ('FL', '1', "Stereo Rotation", '', 'StereoRotation'),
    0x00220015: ('SQ', '1', "Acquisition Device Type Code Sequence", '', 'AcquisitionDeviceTypeCodeSequence'),
    0x00220016: ('SQ', '1', "Illumination Type Code Sequence", '', 'IlluminationTypeCodeSequence'),
    0x00220017: ('SQ', '1', "Light Path Filter Type Stack Code Sequence", '', 'LightPathFilterTypeStackCodeSequence'),
    0x00220018: ('SQ', '1', "Image Path Filter Type Stack Code Sequence", '', 'ImagePathFilterTypeStackCodeSequence'),
    0x00220019: ('SQ', '1', "Lenses Code Sequence", '', 'LensesCodeSequence'),
    0x0022001A: ('SQ', '1', "Channel Description Code Sequence", '', 'ChannelDescriptionCodeSequence'),
    0x0022001B: ('SQ', '1', "Refractive State Sequence", '', 'RefractiveStateSequence'),
    0x0022001C: ('SQ', '1', "Mydriatic Agent Code Sequence", '', 'MydriaticAgentCodeSequence'),
    0x0022001D: ('SQ', '1', "Relative Image Position Code Sequence", '', 'RelativeImagePositionCodeSequence'),
    0x0022001E: ('FL', '1', "Camera Angle of View", '', 'CameraAngleOfView'),
    0x00220020: ('SQ', '1', "Stereo Pairs Sequence", '', 'StereoPairsSequence'),
    0x00220021: ('SQ', '1', "Left Image Sequence", '', 'LeftImageSequence'),
    0x00220022: ('SQ', '1', "Right Image Sequence", '', 'RightImageSequence'),
    0x00220028: ('CS', '1', "Stereo Pairs Present", '', 'StereoPairsPresent'),
    0x00220030: ('FL', '1', "Axial Length of the Eye", '', 'AxialLengthOfTheEye'),
    0x00220031: ('SQ', '1', "Ophthalmic Frame Location Sequence", '', 'OphthalmicFrameLocationSequence'),
    0x00220032: ('FL', '2-2n', "Reference Coordinates", '', 'ReferenceCoordinates'),
    0x00220035: ('FL', '1', "Depth Spatial Resolution", '', 'DepthSpatialResolution'),
    0x00220036: ('FL', '1', "Maximum Depth Distortion", '', 'MaximumDepthDistortion'),
    0x00220037: ('FL', '1', "Along-scan Spatial Resolution", '', 'AlongScanSpatialResolution'),
    0x00220038: ('FL', '1', "Maximum Along-scan Distortion", '', 'MaximumAlongScanDistortion'),
    0x00220039: ('CS', '1', "Ophthalmic Image Orientation", '', 'OphthalmicImageOrientation'),
    0x00220041: ('FL', '1', "Depth of Transverse Image", '', 'DepthOfTransverseImage'),
    0x00220042: ('SQ', '1', "Mydriatic Agent Concentration Units Sequence", '', 'MydriaticAgentConcentrationUnitsSequence'),
    0x00220048: ('FL', '1', "Across-scan Spatial Resolution", '', 'AcrossScanSpatialResolution'),
    0x00220049: ('FL', '1', "Maximum Across-scan Distortion", '', 'MaximumAcrossScanDistortion'),
    0x0022004E: ('DS', '1', "Mydriatic Agent Concentration", '', 'MydriaticAgentConcentration'),
    0x00220055: ('FL', '1', "Illumination Wave Length", '', 'IlluminationWaveLength'),
    0x00220056: ('FL', '1', "Illumination Power", '', 'IlluminationPower'),
    0x00220057: ('FL', '1', "Illumination Bandwidth", '', 'IlluminationBandwidth'),
    0x00220058: ('SQ', '1', "Mydriatic Agent Sequence", '', 'MydriaticAgentSequence'),
    0x00221007: ('SQ', '1', "Ophthalmic Axial Measurements Right Eye Sequence", '', 'OphthalmicAxialMeasurementsRightEyeSequence'),
    0x00221008: ('SQ', '1', "Ophthalmic Axial Measurements Left Eye Sequence", '', 'OphthalmicAxialMeasurementsLeftEyeSequence'),
    0x00221009: ('CS', '1', "Ophthalmic Axial Measurements Device Type", '', 'OphthalmicAxialMeasurementsDeviceType'),
    0x00221010: ('CS', '1', "Ophthalmic Axial Length Measurements Type", '', 'OphthalmicAxialLengthMeasurementsType'),
    0x00221012: ('SQ', '1', "Ophthalmic Axial Length Sequence", '', 'OphthalmicAxialLengthSequence'),
    0x00221019: ('FL', '1', "Ophthalmic Axial Length", '', 'OphthalmicAxialLength'),
    0x00221024: ('SQ', '1', "Lens Status Code Sequence", '', 'LensStatusCodeSequence'),
    0x00221025: ('SQ', '1', "Vitreous Status Code Sequence", '', 'VitreousStatusCodeSequence'),
    0x00221028: ('SQ', '1', "IOL Formula Code Sequence", '', 'IOLFormulaCodeSequence'),
    0x00221029: ('LO', '1', "IOL Formula Detail", '', 'IOLFormulaDetail'),
    0x00221033: ('FL', '1', "Keratometer Index", '', 'KeratometerIndex'),
    0x00221035: ('SQ', '1', "Source of Ophthalmic Axial Length Code Sequence", '', 'SourceOfOphthalmicAxialLengthCodeSequence'),
    0x00221036: ('SQ', '1', "Source of Corneal Size Data Code Sequence", '', 'SourceOfCornealSizeDataCodeSequence'),
    0x00221037: ('FL', '1', "Target Refraction", '', 'TargetRefraction'),
    0x00221039: ('CS', '1', "Refractive Procedure Occurred", '', 'RefractiveProcedureOccurred'),
    0x00221040: ('SQ', '1', "Refractive Surgery Type Code Sequence", '', 'RefractiveSurgeryTypeCodeSequence'),
    0x00221044: ('SQ', '1', "Ophthalmic Ultrasound Method Code Sequence", '', 'OphthalmicUltrasoundMethodCodeSequence'),
    0x00221045: ('SQ', '1', "Surgically Induced Astigmatism Sequence", '', 'SurgicallyInducedAstigmatismSequence'),
    0x00221046: ('CS', '1', "Type of Optical Correction", '', 'TypeOfOpticalCorrection'),
    0x00221047: ('SQ', '1', "Toric IOL Power Sequence", '', 'ToricIOLPowerSequence'),
    0x00221048: ('SQ', '1', "Predicted Toric Error Sequence", '', 'PredictedToricErrorSequence'),
    0x00221049: ('CS', '1', "Pre-Selected for Implantation", '', 'PreSelectedForImplantation'),
    0x0022104A: ('SQ', '1', "Toric IOL Power for Exact Emmetropia Sequence", '', 'ToricIOLPowerForExactEmmetropiaSequence'),
    0x0022104B: ('SQ', '1', "Toric IOL Power for Exact Target Refraction Sequence", '', 'ToricIOLPowerForExactTargetRefractionSequence'),
    0x00221050: ('SQ', '1', "Ophthalmic Axial Length Measurements Sequence", '', 'OphthalmicAxialLengthMeasurementsSequence'),
    0x00221053: ('FL', '1', "IOL Power", '', 'IOLPower'),
    0x00221054: ('FL', '1', "Predicted Refractive Error", '', 'PredictedRefractiveError'),
    0x00221059: ('FL', '1', "Ophthalmic Axial Length Velocity", '', 'OphthalmicAxialLengthVelocity'),
    0x00221065: ('LO', '1', "Lens Status Description", '', 'LensStatusDescription'),
    0x00221066: ('LO', '1', "Vitreous Status Description", '', 'VitreousStatusDescription'),
    0x00221090: ('SQ', '1', "IOL Power Sequence", '', 'IOLPowerSequence'),
    0x00221092: ('SQ', '1', "Lens Constant Sequence", '', 'LensConstantSequence'),
    0x00221093: ('LO', '1', "IOL Manufacturer", '', 'IOLManufacturer'),
    0x00221094: ('LO', '1', "Lens Constant Description", 'Retired', 'LensConstantDescription'),
    0x00221095: ('LO', '1', "Implant Name", '', 'ImplantName'),
    0x00221096: ('SQ', '1', "Keratometry Measurement Type Code Sequence", '', 'KeratometryMeasurementTypeCodeSequence'),
    0x00221097: ('LO', '1', "Implant Part Number", '', 'ImplantPartNumber'),
    0x00221100: ('SQ', '1', "Referenced Ophthalmic Axial Measurements Sequence", '', 'ReferencedOphthalmicAxialMeasurementsSequence'),
    0x00221101: ('SQ', '1', "Ophthalmic Axial Length Measurements Segment Name Code Sequence", '', 'OphthalmicAxialLengthMeasurementsSegmentNameCodeSequence'),
    0x00221103: ('SQ', '1', "Refractive Error Before Refractive Surgery Code Sequence", '', 'RefractiveErrorBeforeRefractiveSurgeryCodeSequence'),
    0x00221121: ('FL', '1', "IOL Power For Exact Emmetropia", '', 'IOLPowerForExactEmmetropia'),
    0x00221122: ('FL', '1', "IOL Power For Exact Target Refraction", '', 'IOLPowerForExactTargetRefraction'),
    0x00221125: ('SQ', '1', "Anterior Chamber Depth Definition Code Sequence", '', 'AnteriorChamberDepthDefinitionCodeSequence'),
    0x00221127: ('SQ', '1', "Lens Thickness Sequence", '', 'LensThicknessSequence'),
    0x00221128: ('SQ', '1', "Anterior Chamber Depth Sequence", '', 'AnteriorChamberDepthSequence'),
    0x0022112A: ('SQ', '1', "Calculation Comment Sequence", '', 'CalculationCommentSequence'),
    0x0022112B: ('CS', '1', "Calculation Comment Type", '', 'CalculationCommentType'),
    0x0022112C: ('LT', '1', "Calculation Comment", '', 'CalculationComment'),
    0x00221130: ('FL', '1', "Lens Thickness", '', 'LensThickness'),
    0x00221131: ('FL', '1', "Anterior Chamber Depth", '', 'AnteriorChamberDepth'),
    0x00221132: ('SQ', '1', "Source of Lens Thickness Data Code Sequence", '', 'SourceOfLensThicknessDataCodeSequence'),
    0x00221133: ('SQ', '1', "Source of Anterior Chamber Depth Data Code Sequence", '', 'SourceOfAnteriorChamberDepthDataCodeSequence'),
    0x00221134: ('SQ', '1', "Source of Refractive Measurements Sequence", '', 'SourceOfRefractiveMeasurementsSequence'),
    0x00221135: ('SQ', '1', "Source of Refractive Measurements Code Sequence", '', 'SourceOfRefractiveMeasurementsCodeSequence'),
    0x00221140: ('CS', '1', "Ophthalmic Axial Length Measurement Modified", '', 'OphthalmicAxialLengthMeasurementModified'),
    0x00221150: ('SQ', '1', "Ophthalmic Axial Length Data Source Code Sequence", '', 'OphthalmicAxialLengthDataSourceCodeSequence'),
    0x00221153: ('SQ', '1', "Ophthalmic Axial Length Acquisition Method Code Sequence", 'Retired', 'OphthalmicAxialLengthAcquisitionMethodCodeSequence'),
    0x00221155: ('FL', '1', "Signal to Noise Ratio", '', 'SignalToNoiseRatio'),
    0x00221159: ('LO', '1', "Ophthalmic Axial Length Data Source Description", '', 'OphthalmicAxialLengthDataSourceDescription'),
    0x00221210: ('SQ', '1', "Ophthalmic Axial Length Measurements Total Length Sequence", '', 'OphthalmicAxialLengthMeasurementsTotalLengthSequence'),
    0x00221211: ('SQ', '1', "Ophthalmic Axial Length Measurements Segmental Length Sequence", '', 'OphthalmicAxialLengthMeasurementsSegmentalLengthSequence'),
    0x00221212: ('SQ', '1', "Ophthalmic Axial Length Measurements Length Summation Sequence", '', 'OphthalmicAxialLengthMeasurementsLengthSummationSequence'),
    0x00221220: ('SQ', '1', "Ultrasound Ophthalmic Axial Length Measurements Sequence", '', 'UltrasoundOphthalmicAxialLengthMeasurementsSequence'),
    0x00221225: ('SQ', '1', "Optical Ophthalmic Axial Length Measurements Sequence", '', 'OpticalOphthalmicAxialLengthMeasurementsSequence'),
    0x00221230: ('SQ', '1', "Ultrasound Selected Ophthalmic Axial Length Sequence", '', 'UltrasoundSelectedOphthalmicAxialLengthSequence'),
    0x00221250: ('SQ', '1', "Ophthalmic Axial Length Selection Method Code Sequence", '', 'OphthalmicAxialLengthSelectionMethodCodeSequence'),
    0x00221255: ('SQ', '1', "Optical Selected Ophthalmic Axial Length Sequence", '', 'OpticalSelectedOphthalmicAxialLengthSequence'),
    0x00221257: ('SQ', '1', "Selected Segmental Ophthalmic Axial Length Sequence", '', 'SelectedSegmentalOphthalmicAxialLengthSequence'),
    0x00221260: ('SQ', '1', "Selected Total Ophthalmic Axial Length Sequence", '', 'SelectedTotalOphthalmicAxialLengthSequence'),
    0x00221262: ('SQ', '1', "Ophthalmic Axial Length Quality Metric Sequence", '', 'OphthalmicAxialLengthQualityMetricSequence'),
    0x00221265: ('SQ', '1', "Ophthalmic Axial Length Quality Metric Type Code Sequence", 'Retired', 'OphthalmicAxialLengthQualityMetricTypeCodeSequence'),
    0x00221273: ('LO', '1', "Ophthalmic Axial Length Quality Metric Type Description", 'Retired', 'OphthalmicAxialLengthQualityMetricTypeDescription'),
    0x00221300: ('SQ', '1', "Intraocular Lens Calculations Right Eye Sequence", '', 'IntraocularLensCalculationsRightEyeSequence'),
    0x00221310: ('SQ', '1', "Intraocular Lens Calculations Left Eye Sequence", '', 'IntraocularLensCalculationsLeftEyeSequence'),
    0x00221330: ('SQ', '1', "Referenced Ophthalmic Axial Length Measurement QC Image Sequence", '', 'ReferencedOphthalmicAxialLengthMeasurementQCImageSequence'),
    0x00221415: ('CS', '1', "Ophthalmic Mapping Device Type", '', 'OphthalmicMappingDeviceType'),
    0x00221420: ('SQ', '1', "Acquisition Method Code Sequence", '', 'AcquisitionMethodCodeSequence'),
    0x00221423: ('SQ', '1', "Acquisition Method Algorithm Sequence", '', 'AcquisitionMethodAlgorithmSequence'),
    0x00221436: ('SQ', '1', "Ophthalmic Thickness Map Type Code Sequence", '', 'OphthalmicThicknessMapTypeCodeSequence'),
    0x00221443: ('SQ', '1', "Ophthalmic Thickness Mapping Normals Sequence", '', 'OphthalmicThicknessMappingNormalsSequence'),
    0x00221445: ('SQ', '1', "Retinal Thickness Definition Code Sequence", '', 'RetinalThicknessDefinitionCodeSequence'),
    0x00221450: ('SQ', '1', "Pixel Value Mapping to Coded Concept Sequence", '', 'PixelValueMappingToCodedConceptSequence'),
    0x00221452: ('US or SS', '1', "Mapped Pixel Value", '', 'MappedPixelValue'),
    0x00221454: ('LO', '1', "Pixel Value Mapping Explanation", '', 'PixelValueMappingExplanation'),
    0x00221458: ('SQ', '1', "Ophthalmic Thickness Map Quality Threshold Sequence", '', 'OphthalmicThicknessMapQualityThresholdSequence'),
    0x00221460: ('FL', '1', "Ophthalmic Thickness Map Threshold Quality Rating", '', 'OphthalmicThicknessMapThresholdQualityRating'),
    0x00221463: ('FL', '2', "Anatomic Structure Reference Point", '', 'AnatomicStructureReferencePoint'),
    0x00221465: ('SQ', '1', "Registration to Localizer Sequence", '', 'RegistrationToLocalizerSequence'),
    0x00221466: ('CS', '1', "Registered Localizer Units", '', 'RegisteredLocalizerUnits'),
    0x00221467: ('FL', '2', "Registered Localizer Top Left Hand Corner", '', 'RegisteredLocalizerTopLeftHandCorner'),
    0x00221468: ('FL', '2', "Registered Localizer Bottom Right Hand Corner", '', 'RegisteredLocalizerBottomRightHandCorner'),
    0x00221470: ('SQ', '1', "Ophthalmic Thickness Map Quality Rating Sequence", '', 'OphthalmicThicknessMapQualityRatingSequence'),
    0x00221472: ('SQ', '1', "Relevant OPT Attributes Sequence", '', 'RelevantOPTAttributesSequence'),
    0x00221512: ('SQ', '1', "Transformation Method Code Sequence", '', 'TransformationMethodCodeSequence'),
    0x00221513: ('SQ', '1', "Transformation Algorithm Sequence", '', 'TransformationAlgorithmSequence'),
    0x00221515: ('CS', '1', "Ophthalmic Axial Length Method", '', 'OphthalmicAxialLengthMethod'),
    0x00221517: ('FL', '1', "Ophthalmic FOV", '', 'OphthalmicFOV'),
    0x00221518: ('SQ', '1', "Two Dimensional to Three Dimensional Map Sequence", '', 'TwoDimensionalToThreeDimensionalMapSequence'),
    0x00221525: ('SQ', '1', "Wide Field Ophthalmic Photography Quality Rating Sequence", '', 'WideFieldOphthalmicPhotographyQualityRatingSequence'),
    0x00221526: ('SQ', '1', "Wide Field Ophthalmic Photography Quality Threshold Sequence", '', 'WideFieldOphthalmicPhotographyQualityThresholdSequence'),
    0x00221527: ('FL', '1', "Wide Field Ophthalmic Photography Threshold Quality Rating", '', 'WideFieldOphthalmicPhotographyThresholdQualityRating'),
    0x00221528: ('FL', '1', "X Coordinates Center Pixel View Angle", '', 'XCoordinatesCenterPixelViewAngle'),
    0x00221529: ('FL', '1', "Y Coordinates Center Pixel View Angle", '', 'YCoordinatesCenterPixelViewAngle'),
    0x00221530: ('UL', '1', "Number of Map Points", '', 'NumberOfMapPoints'),
    0x00221531: ('OF', '1', "Two Dimensional to Three Dimensional Map Data", '', 'TwoDimensionalToThreeDimensionalMapData'),
    0x00221612: ('SQ', '1', "Derivation Algorithm Sequence", '', 'DerivationAlgorithmSequence'),
    0x00221615: ('SQ', '1', "Ophthalmic Image Type Code Sequence", '', 'OphthalmicImageTypeCodeSequence'),
    0x00221616: ('LO', '1', "Ophthalmic Image Type Description", '', 'OphthalmicImageTypeDescription'),
    0x00221618: ('SQ', '1', "Scan Pattern Type Code Sequence", '', 'ScanPatternTypeCodeSequence'),
    0x00221620: ('SQ', '1', "Referenced Surface Mesh Identification Sequence", '', 'ReferencedSurfaceMeshIdentificationSequence'),
    0x00221622: ('CS', '1', "Ophthalmic Volumetric Properties Flag", '', 'OphthalmicVolumetricPropertiesFlag'),
    0x00221624: ('FL', '1', "Ophthalmic Anatomic Reference Point X-Coordinate", '', 'OphthalmicAnatomicReferencePointXCoordinate'),
    0x00221626: ('FL', '1', "Ophthalmic Anatomic Reference Point Y-Coordinate", '', 'OphthalmicAnatomicReferencePointYCoordinate'),
    0x00221627: ('SQ', '1', "Ophthalmic En Face Volume Descriptor Sequence", '', 'OphthalmicEnFaceVolumeDescriptorSequence'),
    0x00221628: ('SQ', '1', "Ophthalmic En Face Image Quality Rating Sequence", '', 'OphthalmicEnFaceImageQualityRatingSequence'),
    0x00221629: ('CS', '1', "Ophthalmic En Face Volume Descriptor Scope", '', 'OphthalmicEnFaceVolumeDescriptorScope'),
    0x00221630: ('DS', '1', "Quality Threshold", '', 'QualityThreshold'),
    0x00221640: ('SQ', '1', "OCT B-scan Analysis Acquisition Parameters Sequence", '', 'OCTBscanAnalysisAcquisitionParametersSequence'),
    0x00221642: ('UL', '1', "Number of B-scans Per Frame", '', 'NumberOfBscansPerFrame'),
    0x00221643: ('FL', '1', "B-scan Slab Thickness", '', 'BscanSlabThickness'),
    0x00221644: ('FL', '1', "Distance Between B-scan Slabs", '', 'DistanceBetweenBscanSlabs'),
    0x00221645: ('FL', '1', "B-scan Cycle Time", '', 'BscanCycleTime'),
    0x00221646: ('FL', '1-n', "B-scan Cycle Time Vector", '', 'BscanCycleTimeVector'),
    0x00221649: ('FL', '1', "A-scan Rate", '', 'AscanRate'),
    0x00221650: ('FL', '1', "B-scan Rate", '', 'BscanRate'),
    0x00221658: ('UL', '1', "Surface Mesh Z-Pixel Offset", '', 'SurfaceMeshZPixelOffset'),
    0x00240010: ('FL', '1', "Visual Field Horizontal Extent", '', 'VisualFieldHorizontalExtent'),
    0x00240011: ('FL', '1', "Visual Field Vertical Extent", '', 'VisualFieldVerticalExtent'),
    0x00240012: ('CS', '1', "Visual Field Shape", '', 'VisualFieldShape'),
    0x00240016: ('SQ', '1', "Screening Test Mode Code Sequence", '', 'ScreeningTestModeCodeSequence'),
    0x00240018: ('FL', '1', "Maximum Stimulus Luminance", '', 'MaximumStimulusLuminance'),
    0x00240020: ('FL', '1', "Background Luminance", '', 'BackgroundLuminance'),
    0x00240021: ('SQ', '1', "Stimulus Color Code Sequence", '', 'StimulusColorCodeSequence'),
    0x00240024: ('SQ', '1', "Background Illumination Color Code Sequence", '', 'BackgroundIlluminationColorCodeSequence'),
    0x00240025: ('FL', '1', "Stimulus Area", '', 'StimulusArea'),
    0x00240028: ('FL', '1', "Stimulus Presentation Time", '', 'StimulusPresentationTime'),
    0x00240032: ('SQ', '1', "Fixation Sequence", '', 'FixationSequence'),
    0x00240033: ('SQ', '1', "Fixation Monitoring Code Sequence", '', 'FixationMonitoringCodeSequence'),
    0x00240034: ('SQ', '1', "Visual Field Catch Trial Sequence", '', 'VisualFieldCatchTrialSequence'),
    0x00240035: ('US', '1', "Fixation Checked Quantity", '', 'FixationCheckedQuantity'),
    0x00240036: ('US', '1', "Patient Not Properly Fixated Quantity", '', 'PatientNotProperlyFixatedQuantity'),
    0x00240037: ('CS', '1', "Presented Visual Stimuli Data Flag", '', 'PresentedVisualStimuliDataFlag'),
    0x00240038: ('US', '1', "Number of Visual Stimuli", '', 'NumberOfVisualStimuli'),
    0x00240039: ('CS', '1', "Excessive Fixation Losses Data Flag", '', 'ExcessiveFixationLossesDataFlag'),
    0x00240040: ('CS', '1', "Excessive Fixation Losses", '', 'ExcessiveFixationLosses'),
    0x00240042: ('US', '1', "Stimuli Retesting Quantity", '', 'StimuliRetestingQuantity'),
    0x00240044: ('LT', '1', "Comments on Patient's Performance of Visual Field", '', 'CommentsOnPatientPerformanceOfVisualField'),
    0x00240045: ('CS', '1', "False Negatives Estimate Flag", '', 'FalseNegativesEstimateFlag'),
    0x00240046: ('FL', '1', "False Negatives Estimate", '', 'FalseNegativesEstimate'),
    0x00240048: ('US', '1', "Negative Catch Trials Quantity", '', 'NegativeCatchTrialsQuantity'),
    0x00240050: ('US', '1', "False Negatives Quantity", '', 'FalseNegativesQuantity'),
    0x00240051: ('CS', '1', "Excessive False Negatives Data Flag", '', 'ExcessiveFalseNegativesDataFlag'),
    0x00240052: ('CS', '1', "Excessive False Negatives", '', 'ExcessiveFalseNegatives'),
    0x00240053: ('CS', '1', "False Positives Estimate Flag", '', 'FalsePositivesEstimateFlag'),
    0x00240054: ('FL', '1', "False Positives Estimate", '', 'FalsePositivesEstimate'),
    0x00240055: ('CS', '1', "Catch Trials Data Flag", '', 'CatchTrialsDataFlag'),
    0x00240056: ('US', '1', "Positive Catch Trials Quantity", '', 'PositiveCatchTrialsQuantity'),
    0x00240057: ('CS', '1', "Test Point Normals Data Flag", '', 'TestPointNormalsDataFlag'),
    0x00240058: ('SQ', '1', "Test Point Normals Sequence", '', 'TestPointNormalsSequence'),
    0x00240059: ('CS', '1', "Global Deviation Probability Normals Flag", '', 'GlobalDeviationProbabilityNormalsFlag'),
    0x00240060: ('US', '1', "False Positives Quantity", '', 'FalsePositivesQuantity'),
    0x00240061: ('CS', '1', "Excessive False Positives Data Flag", '', 'ExcessiveFalsePositivesDataFlag'),
    0x00240062: ('CS', '1', "Excessive False Positives", '', 'ExcessiveFalsePositives'),
    0x00240063: ('CS', '1', "Visual Field Test Normals Flag", '', 'VisualFieldTestNormalsFlag'),
    0x00240064: ('SQ', '1', "Results Normals Sequence", '', 'ResultsNormalsSequence'),
    0x00240065: ('SQ', '1', "Age Corrected Sensitivity Deviation Algorithm Sequence", '', 'AgeCorrectedSensitivityDeviationAlgorithmSequence'),
    0x00240066: ('FL', '1', "Global Deviation From Normal", '', 'GlobalDeviationFromNormal'),
    0x00240067: ('SQ', '1', "Generalized Defect Sensitivity Deviation Algorithm Sequence", '', 'GeneralizedDefectSensitivityDeviationAlgorithmSequence'),
    0x00240068: ('FL', '1', "Localized Deviation From Normal", '', 'LocalizedDeviationFromNormal'),
    0x00240069: ('LO', '1', "Patient Reliability Indicator", '', 'PatientReliabilityIndicator'),
    0x00240070: ('FL', '1', "Visual Field Mean Sensitivity", '', 'VisualFieldMeanSensitivity'),
    0x00240071: ('FL', '1', "Global Deviation Probability", '', 'GlobalDeviationProbability'),
    0x00240072: ('CS', '1', "Local Deviation Probability Normals Flag", '', 'LocalDeviationProbabilityNormalsFlag'),
    0x00240073: ('FL', '1', "Localized Deviation Probability", '', 'LocalizedDeviationProbability'),
    0x00240074: ('CS', '1', "Short Term Fluctuation Calculated", '', 'ShortTermFluctuationCalculated'),
    0x00240075: ('FL', '1', "Short Term Fluctuation", '', 'ShortTermFluctuation'),
    0x00240076: ('CS', '1', "Short Term Fluctuation Probability Calculated", '', 'ShortTermFluctuationProbabilityCalculated'),
    0x00240077: ('FL', '1', "Short Term Fluctuation Probability", '', 'ShortTermFluctuationProbability'),
    0x00240078: ('CS', '1', "Corrected Localized Deviation From Normal Calculated", '', 'CorrectedLocalizedDeviationFromNormalCalculated'),
    0x00240079: ('FL', '1', "Corrected Localized Deviation From Normal", '', 'CorrectedLocalizedDeviationFromNormal'),
    0x00240080: ('CS', '1', "Corrected Localized Deviation From Normal Probability Calculated", '', 'CorrectedLocalizedDeviationFromNormalProbabilityCalculated'),
    0x00240081: ('FL', '1', "Corrected Localized Deviation From Normal Probability", '', 'CorrectedLocalizedDeviationFromNormalProbability'),
    0x00240083: ('SQ', '1', "Global Deviation Probability Sequence", '', 'GlobalDeviationProbabilitySequence'),
    0x00240085: ('SQ', '1', "Localized Deviation Probability Sequence", '', 'LocalizedDeviationProbabilitySequence'),
    0x00240086: ('CS', '1', "Foveal Sensitivity Measured", '', 'FovealSensitivityMeasured'),
    0x00240087: ('FL', '1', "Foveal Sensitivity", '', 'FovealSensitivity'),
    0x00240088: ('FL', '1', "Visual Field Test Duration", '', 'VisualFieldTestDuration'),
    0x00240089: ('SQ', '1', "Visual Field Test Point Sequence", '', 'VisualFieldTestPointSequence'),
    0x00240090: ('FL', '1', "Visual Field Test Point X-Coordinate", '', 'VisualFieldTestPointXCoordinate'),
    0x00240091: ('FL', '1', "Visual Field Test Point Y-Coordinate", '', 'VisualFieldTestPointYCoordinate'),
    0x00240092: ('FL', '1', "Age Corrected Sensitivity Deviation Value", '', 'AgeCorrectedSensitivityDeviationValue'),
    0x00240093: ('CS', '1', "Stimulus Results", '', 'StimulusResults'),
    0x00240094: ('FL', '1', "Sensitivity Value", '', 'SensitivityValue'),
    0x00240095: ('CS', '1', "Retest Stimulus Seen", '', 'RetestStimulusSeen'),
    0x00240096: ('FL', '1', "Retest Sensitivity Value", '', 'RetestSensitivityValue'),
    0x00240097: ('SQ', '1', "Visual Field Test Point Normals Sequence", '', 'VisualFieldTestPointNormalsSequence'),
    0x00240098: ('FL', '1', "Quantified Defect", '', 'QuantifiedDefect'),
    0x00240100: ('FL', '1', "Age Corrected Sensitivity Deviation Probability Value", '', 'AgeCorrectedSensitivityDeviationProbabilityValue'),
    0x00240102: ('CS', '1', "Generalized Defect Corrected Sensitivity Deviation Flag", '', 'GeneralizedDefectCorrectedSensitivityDeviationFlag'),
    0x00240103: ('FL', '1', "Generalized Defect Corrected Sensitivity Deviation Value", '', 'GeneralizedDefectCorrectedSensitivityDeviationValue'),
    0x00240104: ('FL', '1', "Generalized Defect Corrected Sensitivity Deviation Probability Value", '', 'GeneralizedDefectCorrectedSensitivityDeviationProbabilityValue'),
    0x00240105: ('FL', '1', "Minimum Sensitivity Value", '', 'MinimumSensitivityValue'),
    0x00240106: ('CS', '1', "Blind Spot Localized", '', 'BlindSpotLocalized'),
    0x00240107: ('FL', '1', "Blind Spot X-Coordinate", '', 'BlindSpotXCoordinate'),
    0x00240108: ('FL', '1', "Blind Spot Y-Coordinate", '', 'BlindSpotYCoordinate'),
    0x00240110: ('SQ', '1', "Visual Acuity Measurement Sequence", '', 'VisualAcuityMeasurementSequence'),
    0x00240112: ('SQ', '1', "Refractive Parameters Used on Patient Sequence", '', 'RefractiveParametersUsedOnPatientSequence'),
    0x00240113: ('CS', '1', "Measurement Laterality", '', 'MeasurementLaterality'),
    0x00240114: ('SQ', '1', "Ophthalmic Patient Clinical Information Left Eye Sequence", '', 'OphthalmicPatientClinicalInformationLeftEyeSequence'),
    0x00240115: ('SQ', '1', "Ophthalmic Patient Clinical Information Right Eye Sequence", '', 'OphthalmicPatientClinicalInformationRightEyeSequence'),
    0x00240117: ('CS', '1', "Foveal Point Normative Data Flag", '', 'FovealPointNormativeDataFlag'),
    0x00240118: ('FL', '1', "Foveal Point Probability Value", '', 'FovealPointProbabilityValue'),
    0x00240120: ('CS', '1', "Screening Baseline Measured", '', 'ScreeningBaselineMeasured'),
    0x00240122: ('SQ', '1', "Screening Baseline Measured Sequence", '', 'ScreeningBaselineMeasuredSequence'),
    0x00240124: ('CS', '1', "Screening Baseline Type", '', 'ScreeningBaselineType'),
    0x00240126: ('FL', '1', "Screening Baseline Value", '', 'ScreeningBaselineValue'),
    0x00240202: ('LO', '1', "Algorithm Source", '', 'AlgorithmSource'),
    0x00240306: ('LO', '1', "Data Set Name", '', 'DataSetName'),
    0x00240307: ('LO', '1', "Data Set Version", '', 'DataSetVersion'),
    0x00240308: ('LO', '1', "Data Set Source", '', 'DataSetSource'),
    0x00240309: ('LO', '1', "Data Set Description", '', 'DataSetDescription'),
    0x00240317: ('SQ', '1', "Visual Field Test Reliability Global Index Sequence", '', 'VisualFieldTestReliabilityGlobalIndexSequence'),
    0x00240320: ('SQ', '1', "Visual Field Global Results Index Sequence", '', 'VisualFieldGlobalResultsIndexSequence'),
    0x00240325: ('SQ', '1', "Data Observation Sequence", '', 'DataObservationSequence'),
    0x00240338: ('CS', '1', "Index Normals Flag", '', 'IndexNormalsFlag'),
    0x00240341: ('FL', '1', "Index Probability", '', 'IndexProbability'),
    0x00240344: ('SQ', '1', "Index Probability Sequence", '', 'IndexProbabilitySequence'),
    0x00280002: ('US', '1', "Samples per Pixel", '', 'SamplesPerPixel'),
    0x00280003: ('US', '1', "Samples per Pixel Used", '', 'SamplesPerPixelUsed'),
    0x00280004: ('CS', '1', "Photometric Interpretation", '', 'PhotometricInterpretation'),
    0x00280005: ('US', '1', "Image Dimensions", 'Retired', 'ImageDimensions'),
    0x00280006: ('US', '1', "Planar Configuration", '', 'PlanarConfiguration'),
    0x00280008: ('IS', '1', "Number of Frames", '', 'NumberOfFrames'),
    0x00280009: ('AT', '1-n', "Frame Increment Pointer", '', 'FrameIncrementPointer'),
    0x0028000A: ('AT', '1-n', "Frame Dimension Pointer", '', 'FrameDimensionPointer'),
    0x00280010: ('US', '1', "Rows", '', 'Rows'),
    0x00280011: ('US', '1', "Columns", '', 'Columns'),
    0x00280012: ('US', '1', "Planes", 'Retired', 'Planes'),
    0x00280014: ('US', '1', "Ultrasound Color Data Present", '', 'UltrasoundColorDataPresent'),
    0x00280020: ('OB', '1', "Retired-blank", 'Retired', ''),
    0x00280030: ('DS', '2', "Pixel Spacing", '', 'PixelSpacing'),
    0x00280031: ('DS', '2', "Zoom Factor", '', 'ZoomFactor'),
    0x00280032: ('DS', '2', "Zoom Center", '', 'ZoomCenter'),
    0x00280034: ('IS', '2', "Pixel Aspect Ratio", '', 'PixelAspectRatio'),
    0x00280040: ('CS', '1', "Image Format", 'Retired', 'ImageFormat'),
    0x00280050: ('LO', '1-n', "Manipulated Image", 'Retired', 'ManipulatedImage'),
    0x00280051: ('CS', '1-n', "Corrected Image", '', 'CorrectedImage'),
    0x0028005F: ('LO', '1', "Compression Recognition Code", 'Retired', 'CompressionRecognitionCode'),
    0x00280060: ('CS', '1', "Compression Code", 'Retired', 'CompressionCode'),
    0x00280061: ('SH', '1', "Compression Originator", 'Retired', 'CompressionOriginator'),
    0x00280062: ('LO', '1', "Compression Label", 'Retired', 'CompressionLabel'),
    0x00280063: ('SH', '1', "Compression Description", 'Retired', 'CompressionDescription'),
    0x00280065: ('CS', '1-n', "Compression Sequence", 'Retired', 'CompressionSequence'),
    0x00280066: ('AT', '1-n', "Compression Step Pointers", 'Retired', 'CompressionStepPointers'),
    0x00280068: ('US', '1', "Repeat Interval", 'Retired', 'RepeatInterval'),
    0x00280069: ('US', '1', "Bits Grouped", 'Retired', 'BitsGrouped'),
    0x00280070: ('US', '1-n', "Perimeter Table", 'Retired', 'PerimeterTable'),
    0x00280071: ('US or SS', '1', "Perimeter Value", 'Retired', 'PerimeterValue'),
    0x00280080: ('US', '1', "Predictor Rows", 'Retired', 'PredictorRows'),
    0x00280081: ('US', '1', "Predictor Columns", 'Retired', 'PredictorColumns'),
    0x00280082: ('US', '1-n', "Predictor Constants", 'Retired', 'PredictorConstants'),
    0x00280090: ('CS', '1', "Blocked Pixels", 'Retired', 'BlockedPixels'),
    0x00280091: ('US', '1', "Block Rows", 'Retired', 'BlockRows'),
    0x00280092: ('US', '1', "Block Columns", 'Retired', 'BlockColumns'),
    0x00280093: ('US', '1', "Row Overlap", 'Retired', 'RowOverlap'),
    0x00280094: ('US', '1', "Column Overlap", 'Retired', 'ColumnOverlap'),
    0x00280100: ('US', '1', "Bits Allocated", '', 'BitsAllocated'),
    0x00280101: ('US', '1', "Bits Stored", '', 'BitsStored'),
    0x00280102: ('US', '1', "High Bit", '', 'HighBit'),
    0x00280103: ('US', '1', "Pixel Representation", '', 'PixelRepresentation'),
    0x00280104: ('US or SS', '1', "Smallest Valid Pixel Value", 'Retired', 'SmallestValidPixelValue'),
    0x00280105: ('US or SS', '1', "Largest Valid Pixel Value", 'Retired', 'LargestValidPixelValue'),
    0x00280106: ('US or SS', '1', "Smallest Image Pixel Value", '', 'SmallestImagePixelValue'),
    0x00280107: ('US or SS', '1', "Largest Image Pixel Value", '', 'LargestImagePixelValue'),
    0x00280108: ('US or SS', '1', "Smallest Pixel Value in Series", '', 'SmallestPixelValueInSeries'),
    0x00280109: ('US or SS', '1', "Largest Pixel Value in Series", '', 'LargestPixelValueInSeries'),
    0x00280110: ('US or SS', '1', "Smallest Image Pixel Value in Plane", 'Retired', 'SmallestImagePixelValueInPlane'),
    0x00280111: ('US or SS', '1', "Largest Image Pixel Value in Plane", 'Retired', 'LargestImagePixelValueInPlane'),
    0x00280120: ('US or SS', '1', "Pixel Padding Value", '', 'PixelPaddingValue'),
    0x00280121: ('US or SS', '1', "Pixel Padding Range Limit", '', 'PixelPaddingRangeLimit'),
    0x00280122: ('FL', '1', "Float Pixel Padding Value", '', 'FloatPixelPaddingValue'),
    0x00280123: ('FD', '1', "Double Float Pixel Padding Value", '', 'DoubleFloatPixelPaddingValue'),
    0x00280124: ('FL', '1', "Float Pixel Padding Range Limit", '', 'FloatPixelPaddingRangeLimit'),
    0x00280125: ('FD', '1', "Double Float Pixel Padding Range Limit", '', 'DoubleFloatPixelPaddingRangeLimit'),
    0x00280200: ('US', '1', "Image Location", 'Retired', 'ImageLocation'),
    0x00280300: ('CS', '1', "Quality Control Image", '', 'QualityControlImage'),
    0x00280301: ('CS', '1', "Burned In Annotation", '', 'BurnedInAnnotation'),
    0x00280302: ('CS', '1', "Recognizable Visual Features", '', 'RecognizableVisualFeatures'),
    0x00280303: ('CS', '1', "Longitudinal Temporal Information Modified", '', 'LongitudinalTemporalInformationModified'),
    0x00280304: ('UI', '1', "Referenced Color Palette Instance UID", '', 'ReferencedColorPaletteInstanceUID'),
    0x00280400: ('LO', '1', "Transform Label", 'Retired', 'TransformLabel'),
    0x00280401: ('LO', '1', "Transform Version Number", 'Retired', 'TransformVersionNumber'),
    0x00280402: ('US', '1', "Number of Transform Steps", 'Retired', 'NumberOfTransformSteps'),
    0x00280403: ('LO', '1-n', "Sequence of Compressed Data", 'Retired', 'SequenceOfCompressedData'),
    0x00280404: ('AT', '1-n', "Details of Coefficients", 'Retired', 'DetailsOfCoefficients'),
    0x00280700: ('LO', '1', "DCT Label", 'Retired', 'DCTLabel'),
    0x00280701: ('CS', '1-n', "Data Block Description", 'Retired', 'DataBlockDescription'),
    0x00280702: ('AT', '1-n', "Data Block", 'Retired', 'DataBlock'),
    0x00280710: ('US', '1', "Normalization Factor Format", 'Retired', 'NormalizationFactorFormat'),
    0x00280720: ('US', '1', "Zonal Map Number Format", 'Retired', 'ZonalMapNumberFormat'),
    0x00280721: ('AT', '1-n', "Zonal Map Location", 'Retired', 'ZonalMapLocation'),
    0x00280722: ('US', '1', "Zonal Map Format", 'Retired', 'ZonalMapFormat'),
    0x00280730: ('US', '1', "Adaptive Map Format", 'Retired', 'AdaptiveMapFormat'),
    0x00280740: ('US', '1', "Code Number Format", 'Retired', 'CodeNumberFormat'),
    0x00280A02: ('CS', '1', "Pixel Spacing Calibration Type", '', 'PixelSpacingCalibrationType'),
    0x00280A04: ('LO', '1', "Pixel Spacing Calibration Description", '', 'PixelSpacingCalibrationDescription'),
    0x00281040: ('CS', '1', "Pixel Intensity Relationship", '', 'PixelIntensityRelationship'),
    0x00281041: ('SS', '1', "Pixel Intensity Relationship Sign", '', 'PixelIntensityRelationshipSign'),
    0x00281050: ('DS', '1-n', "Window Center", '', 'WindowCenter'),
    0x00281051: ('DS', '1-n', "Window Width", '', 'WindowWidth'),
    0x00281052: ('DS', '1', "Rescale Intercept", '', 'RescaleIntercept'),
    0x00281053: ('DS', '1', "Rescale Slope", '', 'RescaleSlope'),
    0x00281054: ('LO', '1', "Rescale Type", '', 'RescaleType'),
    0x00281055: ('LO', '1-n', "Window Center & Width Explanation", '', 'WindowCenterWidthExplanation'),
    0x00281056: ('CS', '1', "VOI LUT Function", '', 'VOILUTFunction'),
    0x00281080: ('CS', '1', "Gray Scale", 'Retired', 'GrayScale'),
    0x00281090: ('CS', '1', "Recommended Viewing Mode", '', 'RecommendedViewingMode'),
    0x00281100: ('US or SS', '3', "Gray Lookup Table Descriptor", 'Retired', 'GrayLookupTableDescriptor'),
    0x00281101: ('US or SS', '3', "Red Palette Color Lookup Table Descriptor", '', 'RedPaletteColorLookupTableDescriptor'),
    0x00281102: ('US or SS', '3', "Green Palette Color Lookup Table Descriptor", '', 'GreenPaletteColorLookupTableDescriptor'),
    0x00281103: ('US or SS', '3', "Blue Palette Color Lookup Table Descriptor", '', 'BluePaletteColorLookupTableDescriptor'),
    0x00281104: ('US', '3', "Alpha Palette Color Lookup Table Descriptor", '', 'AlphaPaletteColorLookupTableDescriptor'),
    0x00281111: ('US or SS', '4', "Large Red Palette Color Lookup Table Descriptor", 'Retired', 'LargeRedPaletteColorLookupTableDescriptor'),
    0x00281112: ('US or SS', '4', "Large Green Palette Color Lookup Table Descriptor", 'Retired', 'LargeGreenPaletteColorLookupTableDescriptor'),
    0x00281113: ('US or SS', '4', "Large Blue Palette Color Lookup Table Descriptor", 'Retired', 'LargeBluePaletteColorLookupTableDescriptor'),
    0x00281199: ('UI', '1', "Palette Color Lookup Table UID", '', 'PaletteColorLookupTableUID'),
    0x00281200: ('US or SS or OW', '1-n', "Gray Lookup Table Data", 'Retired', 'GrayLookupTableData'),
    0x00281201: ('OW', '1', "Red Palette Color Lookup Table Data", '', 'RedPaletteColorLookupTableData'),
    0x00281202: ('OW', '1', "Green Palette Color Lookup Table Data", '', 'GreenPaletteColorLookupTableData'),
    0x00281203: ('OW', '1', "Blue Palette Color Lookup Table Data", '', 'BluePaletteColorLookupTableData'),
    0x00281204: ('OW', '1', "Alpha Palette Color Lookup Table Data", '', 'AlphaPaletteColorLookupTableData'),
    0x00281211: ('OW', '1', "Large Red Palette Color Lookup Table Data", 'Retired', 'LargeRedPaletteColorLookupTableData'),
    0x00281212: ('OW', '1', "Large Green Palette Color Lookup Table Data", 'Retired', 'LargeGreenPaletteColorLookupTableData'),
    0x00281213: ('OW', '1', "Large Blue Palette Color Lookup Table Data", 'Retired', 'LargeBluePaletteColorLookupTableData'),
    0x00281214: ('UI', '1', "Large Palette Color Lookup Table UID", 'Retired', 'LargePaletteColorLookupTableUID'),
    0x00281221: ('OW', '1', "Segmented Red Palette Color Lookup Table Data", '', 'SegmentedRedPaletteColorLookupTableData'),
    0x00281222: ('OW', '1', "Segmented Green Palette Color Lookup Table Data", '', 'SegmentedGreenPaletteColorLookupTableData'),
    0x00281223: ('OW', '1', "Segmented Blue Palette Color Lookup Table Data", '', 'SegmentedBluePaletteColorLookupTableData'),
    0x00281224: ('OW', '1', "Segmented Alpha Palette Color Lookup Table Data", '', 'SegmentedAlphaPaletteColorLookupTableData'),
    0x00281230: ('SQ', '1', "Stored Value Color Range Sequence", '', 'StoredValueColorRangeSequence'),
    0x00281231: ('FD', '1', "Minimum Stored Value Mapped", '', 'MinimumStoredValueMapped'),
    0x00281232: ('FD', '1', "Maximum Stored Value Mapped", '', 'MaximumStoredValueMapped'),
    0x00281300: ('CS', '1', "Breast Implant Present", '', 'BreastImplantPresent'),
    0x00281350: ('CS', '1', "Partial View", '', 'PartialView'),
    0x00281351: ('ST', '1', "Partial View Description", '', 'PartialViewDescription'),
    0x00281352: ('SQ', '1', "Partial View Code Sequence", '', 'PartialViewCodeSequence'),
    0x0028135A: ('CS', '1', "Spatial Locations Preserved", '', 'SpatialLocationsPreserved'),
    0x00281401: ('SQ', '1', "Data Frame Assignment Sequence", '', 'DataFrameAssignmentSequence'),
    0x00281402: ('CS', '1', "Data Path Assignment", '', 'DataPathAssignment'),
    0x00281403: ('US', '1', "Bits Mapped to Color Lookup Table", '', 'BitsMappedToColorLookupTable'),
    0x00281404: ('SQ', '1', "Blending LUT 1 Sequence", '', 'BlendingLUT1Sequence'),
    0x00281405: ('CS', '1', "Blending LUT 1 Transfer Function", '', 'BlendingLUT1TransferFunction'),
    0x00281406: ('FD', '1', "Blending Weight Constant", '', 'BlendingWeightConstant'),
    0x00281407: ('US', '3', "Blending Lookup Table Descriptor", '', 'BlendingLookupTableDescriptor'),
    0x00281408: ('OW', '1', "Blending Lookup Table Data", '', 'BlendingLookupTableData'),
    0x0028140B: ('SQ', '1', "Enhanced Palette Color Lookup Table Sequence", '', 'EnhancedPaletteColorLookupTableSequence'),
    0x0028140C: ('SQ', '1', "Blending LUT 2 Sequence", '', 'BlendingLUT2Sequence'),
    0x0028140D: ('CS', '1', "Blending LUT 2 Transfer Function", '', 'BlendingLUT2TransferFunction'),
    0x0028140E: ('CS', '1', "Data Path ID", '', 'DataPathID'),
    0x0028140F: ('CS', '1', "RGB LUT Transfer Function", '', 'RGBLUTTransferFunction'),
    0x00281410: ('CS', '1', "Alpha LUT Transfer Function", '', 'AlphaLUTTransferFunction'),
    0x00282000: ('OB', '1', "ICC Profile", '', 'ICCProfile'),
    0x00282002: ('CS', '1', "Color Space", '', 'ColorSpace'),
    0x00282110: ('CS', '1', "Lossy Image Compression", '', 'LossyImageCompression'),
    0x00282112: ('DS', '1-n', "Lossy Image Compression Ratio", '', 'LossyImageCompressionRatio'),
    0x00282114: ('CS', '1-n', "Lossy Image Compression Method", '', 'LossyImageCompressionMethod'),
    0x00283000: ('SQ', '1', "Modality LUT Sequence", '', 'ModalityLUTSequence'),
    0x00283001: ('SQ', '1', "Variable Modality LUT Sequence", '', 'VariableModalityLUTSequence'),
    0x00283002: ('US or SS', '3', "LUT Descriptor", '', 'LUTDescriptor'),
    0x00283003: ('LO', '1', "LUT Explanation", '', 'LUTExplanation'),
    0x00283004: ('LO', '1', "Modality LUT Type", '', 'ModalityLUTType'),
    0x00283006: ('US or OW', '1-n', "LUT Data", '', 'LUTData'),
    0x00283010: ('SQ', '1', "VOI LUT Sequence", '', 'VOILUTSequence'),
    0x00283110: ('SQ', '1', "Softcopy VOI LUT Sequence", '', 'SoftcopyVOILUTSequence'),
    0x00284000: ('LT', '1', "Image Presentation Comments", 'Retired', 'ImagePresentationComments'),
    0x00285000: ('SQ', '1', "Bi-Plane Acquisition Sequence", 'Retired', 'BiPlaneAcquisitionSequence'),
    0x00286010: ('US', '1', "Representative Frame Number", '', 'RepresentativeFrameNumber'),
    0x00286020: ('US', '1-n', "Frame Numbers of Interest (FOI)", '', 'FrameNumbersOfInterest'),
    0x00286022: ('LO', '1-n', "Frame of Interest Description", '', 'FrameOfInterestDescription'),
    0x00286023: ('CS', '1-n', "Frame of Interest Type", '', 'FrameOfInterestType'),
    0x00286030: ('US', '1-n', "Mask Pointer(s)", 'Retired', 'MaskPointers'),
    0x00286040: ('US', '1-n', "R Wave Pointer", '', 'RWavePointer'),
    0x00286100: ('SQ', '1', "Mask Subtraction Sequence", '', 'MaskSubtractionSequence'),
    0x00286101: ('CS', '1', "Mask Operation", '', 'MaskOperation'),
    0x00286102: ('US', '2-2n', "Applicable Frame Range", '', 'ApplicableFrameRange'),
    0x00286110: ('US', '1-n', "Mask Frame Numbers", '', 'MaskFrameNumbers'),
    0x00286112: ('US', '1', "Contrast Frame Averaging", '', 'ContrastFrameAveraging'),
    0x00286114: ('FL', '2', "Mask Sub-pixel Shift", '', 'MaskSubPixelShift'),
    0x00286120: ('SS', '1', "TID Offset", '', 'TIDOffset'),
    0x00286190: ('ST', '1', "Mask Operation Explanation", '', 'MaskOperationExplanation'),
    0x00287000: ('SQ', '1', "Equipment Administrator Sequence", '', 'EquipmentAdministratorSequence'),
    0x00287001: ('US', '1', "Number of Display Subsystems", '', 'NumberOfDisplaySubsystems'),
    0x00287002: ('US', '1', "Current Configuration ID", '', 'CurrentConfigurationID'),
    0x00287003: ('US', '1', "Display Subsystem ID", '', 'DisplaySubsystemID'),
    0x00287004: ('SH', '1', "Display Subsystem Name", '', 'DisplaySubsystemName'),
    0x00287005: ('LO', '1', "Display Subsystem Description", '', 'DisplaySubsystemDescription'),
    0x00287006: ('CS', '1', "System Status", '', 'SystemStatus'),
    0x00287007: ('LO', '1', "System Status Comment", '', 'SystemStatusComment'),
    0x00287008: ('SQ', '1', "Target Luminance Characteristics Sequence", '', 'TargetLuminanceCharacteristicsSequence'),
    0x00287009: ('US', '1', "Luminance Characteristics ID", '', 'LuminanceCharacteristicsID'),
    0x0028700A: ('SQ', '1', "Display Subsystem Configuration Sequence", '', 'DisplaySubsystemConfigurationSequence'),
    0x0028700B: ('US', '1', "Configuration ID", '', 'ConfigurationID'),
    0x0028700C: ('SH', '1', "Configuration Name", '', 'ConfigurationName'),
    0x0028700D: ('LO', '1', "Configuration Description", '', 'ConfigurationDescription'),
    0x0028700E: ('US', '1', "Referenced Target Luminance Characteristics ID", '', 'ReferencedTargetLuminanceCharacteristicsID'),
    0x0028700F: ('SQ', '1', "QA Results Sequence", '', 'QAResultsSequence'),
    0x00287010: ('SQ', '1', "Display Subsystem QA Results Sequence", '', 'DisplaySubsystemQAResultsSequence'),
    0x00287011: ('SQ', '1', "Configuration QA Results Sequence", '', 'ConfigurationQAResultsSequence'),
    0x00287012: ('SQ', '1', "Measurement Equipment Sequence", '', 'MeasurementEquipmentSequence'),
    0x00287013: ('CS', '1-n', "Measurement Functions", '', 'MeasurementFunctions'),
    0x00287014: ('CS', '1', "Measurement Equipment Type", '', 'MeasurementEquipmentType'),
    0x00287015: ('SQ', '1', "Visual Evaluation Result Sequence", '', 'VisualEvaluationResultSequence'),
    0x00287016: ('SQ', '1', "Display Calibration Result Sequence", '', 'DisplayCalibrationResultSequence'),
    0x00287017: ('US', '1', "DDL Value", '', 'DDLValue'),
    0x00287018: ('FL', '2', "CIExy White Point", '', 'CIExyWhitePoint'),
    0x00287019: ('CS', '1', "Display Function Type", '', 'DisplayFunctionType'),
    0x0028701A: ('FL', '1', "Gamma Value", '', 'GammaValue'),
    0x0028701B: ('US', '1', "Number of Luminance Points", '', 'NumberOfLuminancePoints'),
    0x0028701C: ('SQ', '1', "Luminance Response Sequence", '', 'LuminanceResponseSequence'),
    0x0028701D: ('FL', '1', "Target Minimum Luminance", '', 'TargetMinimumLuminance'),
    0x0028701E: ('FL', '1', "Target Maximum Luminance", '', 'TargetMaximumLuminance'),
    0x0028701F: ('FL', '1', "Luminance Value", '', 'LuminanceValue'),
    0x00287020: ('LO', '1', "Luminance Response Description", '', 'LuminanceResponseDescription'),
    0x00287021: ('CS', '1', "White Point Flag", '', 'WhitePointFlag'),
    0x00287022: ('SQ', '1', "Display Device Type Code Sequence", '', 'DisplayDeviceTypeCodeSequence'),
    0x00287023: ('SQ', '1', "Display Subsystem Sequence", '', 'DisplaySubsystemSequence'),
    0x00287024: ('SQ', '1', "Luminance Result Sequence", '', 'LuminanceResultSequence'),
    0x00287025: ('CS', '1', "Ambient Light Value Source", '', 'AmbientLightValueSource'),
    0x00287026: ('CS', '1-n', "Measured Characteristics", '', 'MeasuredCharacteristics'),
    0x00287027: ('SQ', '1', "Luminance Uniformity Result Sequence", '', 'LuminanceUniformityResultSequence'),
    0x00287028: ('SQ', '1', "Visual Evaluation Test Sequence", '', 'VisualEvaluationTestSequence'),
    0x00287029: ('CS', '1', "Test Result", '', 'TestResult'),
    0x0028702A: ('LO', '1', "Test Result Comment", '', 'TestResultComment'),
    0x0028702B: ('CS', '1', "Test Image Validation", '', 'TestImageValidation'),
    0x0028702C: ('SQ', '1', "Test Pattern Code Sequence", '', 'TestPatternCodeSequence'),
    0x0028702D: ('SQ', '1', "Measurement Pattern Code Sequence", '', 'MeasurementPatternCodeSequence'),
    0x0028702E: ('SQ', '1', "Visual Evaluation Method Code Sequence", '', 'VisualEvaluationMethodCodeSequence'),
    0x00287FE0: ('UR', '1', "Pixel Data Provider URL", '', 'PixelDataProviderURL'),
    0x00289001: ('UL', '1', "Data Point Rows", '', 'DataPointRows'),
    0x00289002: ('UL', '1', "Data Point Columns", '', 'DataPointColumns'),
    0x00289003: ('CS', '1', "Signal Domain Columns", '', 'SignalDomainColumns'),
    0x00289099: ('US', '1', "Largest Monochrome Pixel Value", 'Retired', 'LargestMonochromePixelValue'),
    0x00289108: ('CS', '1', "Data Representation", '', 'DataRepresentation'),
    0x00289110: ('SQ', '1', "Pixel Measures Sequence", '', 'PixelMeasuresSequence'),
    0x00289132: ('SQ', '1', "Frame VOI LUT Sequence", '', 'FrameVOILUTSequence'),
    0x00289145: ('SQ', '1', "Pixel Value Transformation Sequence", '', 'PixelValueTransformationSequence'),
    0x00289235: ('CS', '1', "Signal Domain Rows", '', 'SignalDomainRows'),
    0x00289411: ('FL', '1', "Display Filter Percentage", '', 'DisplayFilterPercentage'),
    0x00289415: ('SQ', '1', "Frame Pixel Shift Sequence", '', 'FramePixelShiftSequence'),
    0x00289416: ('US', '1', "Subtraction Item ID", '', 'SubtractionItemID'),
    0x00289422: ('SQ', '1', "Pixel Intensity Relationship LUT Sequence", '', 'PixelIntensityRelationshipLUTSequence'),
    0x00289443: ('SQ', '1', "Frame Pixel Data Properties Sequence", '', 'FramePixelDataPropertiesSequence'),
    0x00289444: ('CS', '1', "Geometrical Properties", '', 'GeometricalProperties'),
    0x00289445: ('FL', '1', "Geometric Maximum Distortion", '', 'GeometricMaximumDistortion'),
    0x00289446: ('CS', '1-n', "Image Processing Applied", '', 'ImageProcessingApplied'),
    0x00289454: ('CS', '1', "Mask Selection Mode", '', 'MaskSelectionMode'),
    0x00289474: ('CS', '1', "LUT Function", '', 'LUTFunction'),
    0x00289478: ('FL', '1', "Mask Visibility Percentage", '', 'MaskVisibilityPercentage'),
    0x00289501: ('SQ', '1', "Pixel Shift Sequence", '', 'PixelShiftSequence'),
    0x00289502: ('SQ', '1', "Region Pixel Shift Sequence", '', 'RegionPixelShiftSequence'),
    0x00289503: ('SS', '2-2n', "Vertices of the Region", '', 'VerticesOfTheRegion'),
    0x00289505: ('SQ', '1', "Multi-frame Presentation Sequence", '', 'MultiFramePresentationSequence'),
    0x00289506: ('US', '2-2n', "Pixel Shift Frame Range", '', 'PixelShiftFrameRange'),
    0x00289507: ('US', '2-2n', "LUT Frame Range", '', 'LUTFrameRange'),
    0x00289520: ('DS', '16', "Image to Equipment Mapping Matrix", '', 'ImageToEquipmentMappingMatrix'),
    0x00289537: ('CS', '1', "Equipment Coordinate System Identification", '', 'EquipmentCoordinateSystemIdentification'),
    0x0032000A: ('CS', '1', "Study Status ID", 'Retired', 'StudyStatusID'),
    0x0032000C: ('CS', '1', "Study Priority ID", 'Retired', 'StudyPriorityID'),
    0x00320012: ('LO', '1', "Study ID Issuer", 'Retired', 'StudyIDIssuer'),
    0x00320032: ('DA', '1', "Study Verified Date", 'Retired', 'StudyVerifiedDate'),
    0x00320033: ('TM', '1', "Study Verified Time", 'Retired', 'StudyVerifiedTime'),
    0x00320034: ('DA', '1', "Study Read Date", 'Retired', 'StudyReadDate'),
    0x00320035: ('TM', '1', "Study Read Time", 'Retired', 'StudyReadTime'),
    0x00321000: ('DA', '1', "Scheduled Study Start Date", 'Retired', 'ScheduledStudyStartDate'),
    0x00321001: ('TM', '1', "Scheduled Study Start Time", 'Retired', 'ScheduledStudyStartTime'),
    0x00321010: ('DA', '1', "Scheduled Study Stop Date", 'Retired', 'ScheduledStudyStopDate'),
    0x00321011: ('TM', '1', "Scheduled Study Stop Time", 'Retired', 'ScheduledStudyStopTime'),
    0x00321020: ('LO', '1', "Scheduled Study Location", 'Retired', 'ScheduledStudyLocation'),
    0x00321021: ('AE', '1-n', "Scheduled Study Location AE Title", 'Retired', 'ScheduledStudyLocationAETitle'),
    0x00321030: ('LO', '1', "Reason for Study", 'Retired', 'ReasonForStudy'),
    0x00321031: ('SQ', '1', "Requesting Physician Identification Sequence", '', 'RequestingPhysicianIdentificationSequence'),
    0x00321032: ('PN', '1', "Requesting Physician", '', 'RequestingPhysician'),
    0x00321033: ('LO', '1', "Requesting Service", '', 'RequestingService'),
    0x00321034: ('SQ', '1', "Requesting Service Code Sequence", '', 'RequestingServiceCodeSequence'),
    0x00321040: ('DA', '1', "Study Arrival Date", 'Retired', 'StudyArrivalDate'),
    0x00321041: ('TM', '1', "Study Arrival Time", 'Retired', 'StudyArrivalTime'),
    0x00321050: ('DA', '1', "Study Completion Date", 'Retired', 'StudyCompletionDate'),
    0x00321051: ('TM', '1', "Study Completion Time", 'Retired', 'StudyCompletionTime'),
    0x00321055: ('CS', '1', "Study Component Status ID", 'Retired', 'StudyComponentStatusID'),
    0x00321060: ('LO', '1', "Requested Procedure Description", '', 'RequestedProcedureDescription'),
    0x00321064: ('SQ', '1', "Requested Procedure Code Sequence", '', 'RequestedProcedureCodeSequence'),
    0x00321065: ('SQ', '1', "Requested Laterality Code Sequence", '', 'RequestedLateralityCodeSequence'),
    0x00321066: ('UT', '1', "Reason for Visit", '', 'ReasonForVisit'),
    0x00321067: ('SQ', '1', "Reason for Visit Code Sequence", '', 'ReasonForVisitCodeSequence'),
    0x00321070: ('LO', '1', "Requested Contrast Agent", '', 'RequestedContrastAgent'),
    0x00324000: ('LT', '1', "Study Comments", 'Retired', 'StudyComments'),
    0x00340001: ('SQ', '1', "Flow Identifier Sequence", '', 'FlowIdentifierSequence'),
    0x00340002: ('OB', '1', "Flow Identifier", '', 'FlowIdentifier'),
    0x00340003: ('UI', '1', "Flow Transfer Syntax UID", '', 'FlowTransferSyntaxUID'),
    0x00340004: ('UL', '1', "Flow RTP Sampling Rate", '', 'FlowRTPSamplingRate'),
    0x00340005: ('OB', '1', "Source Identifier", '', 'SourceIdentifier'),
    0x00340007: ('OB', '1', "Frame Origin Timestamp", '', 'FrameOriginTimestamp'),
    0x00340008: ('CS', '1', "Includes Imaging Subject", '', 'IncludesImagingSubject'),
    0x00340009: ('SQ', '1', "Frame Usefulness Group Sequence", '', 'FrameUsefulnessGroupSequence'),
    0x0034000A: ('SQ', '1', "Real-Time Bulk Data Flow Sequence", '', 'RealTimeBulkDataFlowSequence'),
    0x0034000B: ('SQ', '1', "Camera Position Group Sequence", '', 'CameraPositionGroupSequence'),
    0x0034000C: ('CS', '1', "Includes Information", '', 'IncludesInformation'),
    0x0034000D: ('SQ', '1', "Time of Frame Group Sequence", '', 'TimeOfFrameGroupSequence'),
    0x00380004: ('SQ', '1', "Referenced Patient Alias Sequence", 'Retired', 'ReferencedPatientAliasSequence'),
    0x00380008: ('CS', '1', "Visit Status ID", '', 'VisitStatusID'),
    0x00380010: ('LO', '1', "Admission ID", '', 'AdmissionID'),
    0x00380011: ('LO', '1', "Issuer of Admission ID", 'Retired', 'IssuerOfAdmissionID'),
    0x00380014: ('SQ', '1', "Issuer of Admission ID Sequence", '', 'IssuerOfAdmissionIDSequence'),
    0x00380016: ('LO', '1', "Route of Admissions", '', 'RouteOfAdmissions'),
    0x0038001A: ('DA', '1', "Scheduled Admission Date", 'Retired', 'ScheduledAdmissionDate'),
    0x0038001B: ('TM', '1', "Scheduled Admission Time", 'Retired', 'ScheduledAdmissionTime'),
    0x0038001C: ('DA', '1', "Scheduled Discharge Date", 'Retired', 'ScheduledDischargeDate'),
    0x0038001D: ('TM', '1', "Scheduled Discharge Time", 'Retired', 'ScheduledDischargeTime'),
    0x0038001E: ('LO', '1', "Scheduled Patient Institution Residence", 'Retired', 'ScheduledPatientInstitutionResidence'),
    0x00380020: ('DA', '1', "Admitting Date", '', 'AdmittingDate'),
    0x00380021: ('TM', '1', "Admitting Time", '', 'AdmittingTime'),
    0x00380030: ('DA', '1', "Discharge Date", 'Retired', 'DischargeDate'),
    0x00380032: ('TM', '1', "Discharge Time", 'Retired', 'DischargeTime'),
    0x00380040: ('LO', '1', "Discharge Diagnosis Description", 'Retired', 'DischargeDiagnosisDescription'),
    0x00380044: ('SQ', '1', "Discharge Diagnosis Code Sequence", 'Retired', 'DischargeDiagnosisCodeSequence'),
    0x00380050: ('LO', '1', "Special Needs", '', 'SpecialNeeds'),
    0x00380060: ('LO', '1', "Service Episode ID", '', 'ServiceEpisodeID'),
    0x00380061: ('LO', '1', "Issuer of Service Episode ID", 'Retired', 'IssuerOfServiceEpisodeID'),
    0x00380062: ('LO', '1', "Service Episode Description", '', 'ServiceEpisodeDescription'),
    0x00380064: ('SQ', '1', "Issuer of Service Episode ID Sequence", '', 'IssuerOfServiceEpisodeIDSequence'),
    0x00380100: ('SQ', '1', "Pertinent Documents Sequence", '', 'PertinentDocumentsSequence'),
    0x00380101: ('SQ', '1', "Pertinent Resources Sequence", '', 'PertinentResourcesSequence'),
    0x00380102: ('LO', '1', "Resource Description", '', 'ResourceDescription'),
    0x00380300: ('LO', '1', "Current Patient Location", '', 'CurrentPatientLocation'),
    0x00380400: ('LO', '1', "Patient's Institution Residence", '', 'PatientInstitutionResidence'),
    0x00380500: ('LO', '1', "Patient State", '', 'PatientState'),
    0x00380502: ('SQ', '1', "Patient Clinical Trial Participation Sequence", '', 'PatientClinicalTrialParticipationSequence'),
    0x00384000: ('LT', '1', "Visit Comments", '', 'VisitComments'),
    0x003A0004: ('CS', '1', "Waveform Originality", '', 'WaveformOriginality'),
    0x003A0005: ('US', '1', "Number of Waveform Channels", '', 'NumberOfWaveformChannels'),
    0x003A0010: ('UL', '1', "Number of Waveform Samples", '', 'NumberOfWaveformSamples'),
    0x003A001A: ('DS', '1', "Sampling Frequency", '', 'SamplingFrequency'),
    0x003A0020: ('SH', '1', "Multiplex Group Label", '', 'MultiplexGroupLabel'),
    0x003A0200: ('SQ', '1', "Channel Definition Sequence", '', 'ChannelDefinitionSequence'),
    0x003A0202: ('IS', '1', "Waveform Channel Number", '', 'WaveformChannelNumber'),
    0x003A0203: ('SH', '1', "Channel Label", '', 'ChannelLabel'),
    0x003A0205: ('CS', '1-n', "Channel Status", '', 'ChannelStatus'),
    0x003A0208: ('SQ', '1', "Channel Source Sequence", '', 'ChannelSourceSequence'),
    0x003A0209: ('SQ', '1', "Channel Source Modifiers Sequence", '', 'ChannelSourceModifiersSequence'),
    0x003A020A: ('SQ', '1', "Source Waveform Sequence", '', 'SourceWaveformSequence'),
    0x003A020C: ('LO', '1', "Channel Derivation Description", '', 'ChannelDerivationDescription'),
    0x003A0210: ('DS', '1', "Channel Sensitivity", '', 'ChannelSensitivity'),
    0x003A0211: ('SQ', '1', "Channel Sensitivity Units Sequence", '', 'ChannelSensitivityUnitsSequence'),
    0x003A0212: ('DS', '1', "Channel Sensitivity Correction Factor", '', 'ChannelSensitivityCorrectionFactor'),
    0x003A0213: ('DS', '1', "Channel Baseline", '', 'ChannelBaseline'),
    0x003A0214: ('DS', '1', "Channel Time Skew", '', 'ChannelTimeSkew'),
    0x003A0215: ('DS', '1', "Channel Sample Skew", '', 'ChannelSampleSkew'),
    0x003A0218: ('DS', '1', "Channel Offset", '', 'ChannelOffset'),
    0x003A021A: ('US', '1', "Waveform Bits Stored", '', 'WaveformBitsStored'),
    0x003A0220: ('DS', '1', "Filter Low Frequency", '', 'FilterLowFrequency'),
    0x003A0221: ('DS', '1', "Filter High Frequency", '', 'FilterHighFrequency'),
    0x003A0222: ('DS', '1', "Notch Filter Frequency", '', 'NotchFilterFrequency'),
    0x003A0223: ('DS', '1', "Notch Filter Bandwidth", '', 'NotchFilterBandwidth'),
    0x003A0230: ('FL', '1', "Waveform Data Display Scale", '', 'WaveformDataDisplayScale'),
    0x003A0231: ('US', '3', "Waveform Display Background CIELab Value", '', 'WaveformDisplayBackgroundCIELabValue'),
    0x003A0240: ('SQ', '1', "Waveform Presentation Group Sequence", '', 'WaveformPresentationGroupSequence'),
    0x003A0241: ('US', '1', "Presentation Group Number", '', 'PresentationGroupNumber'),
    0x003A0242: ('SQ', '1', "Channel Display Sequence", '', 'ChannelDisplaySequence'),
    0x003A0244: ('US', '3', "Channel Recommended Display CIELab Value", '', 'ChannelRecommendedDisplayCIELabValue'),
    0x003A0245: ('FL', '1', "Channel Position", '', 'ChannelPosition'),
    0x003A0246: ('CS', '1', "Display Shading Flag", '', 'DisplayShadingFlag'),
    0x003A0247: ('FL', '1', "Fractional Channel Display Scale", '', 'FractionalChannelDisplayScale'),
    0x003A0248: ('FL', '1', "Absolute Channel Display Scale", '', 'AbsoluteChannelDisplayScale'),
    0x003A0300: ('SQ', '1', "Multiplexed Audio Channels Description Code Sequence", '', 'MultiplexedAudioChannelsDescriptionCodeSequence'),
    0x003A0301: ('IS', '1', "Channel Identification Code", '', 'ChannelIdentificationCode'),
    0x003A0302: ('CS', '1', "Channel Mode", '', 'ChannelMode'),
    0x003A0310: ('UI', '1', "Multiplex Group UID", '', 'MultiplexGroupUID'),
    0x003A0311: ('DS', '1', "Powerline Frequency", '', 'PowerlineFrequency'),
    0x003A0312: ('SQ', '1', "Channel Impedance Sequence", '', 'ChannelImpedanceSequence'),
    0x003A0313: ('DS', '1', "Impedance Value", '', 'ImpedanceValue'),
    0x003A0314: ('DT', '1', "Impedance Measurement DateTime", '', 'ImpedanceMeasurementDateTime'),
    0x003A0315: ('DS', '1', "Impedance Measurement Frequency", '', 'ImpedanceMeasurementFrequency'),
    0x003A0316: ('CS', '1', "Impedance Measurement Current Type", '', 'ImpedanceMeasurementCurrentType'),
    0x003A0317: ('CS', '1', "Waveform Amplifier Type", '', 'WaveformAmplifierType'),
    0x003A0318: ('SQ', '1', "Filter Low Frequency Characteristics Sequence", '', 'FilterLowFrequencyCharacteristicsSequence'),
    0x003A0319: ('SQ', '1', "Filter High Frequency Characteristics Sequence", '', 'FilterHighFrequencyCharacteristicsSequence'),
    0x003A0320: ('SQ', '1', "Summarized Filter Lookup Table Sequence", '', 'SummarizedFilterLookupTableSequence'),
    0x003A0321: ('SQ', '1', "Notch Filter Characteristics Sequence", '', 'NotchFilterCharacteristicsSequence'),
    0x003A0322: ('CS', '1', "Waveform Filter Type", '', 'WaveformFilterType'),
    0x003A0323: ('SQ', '1', "Analog Filter Characteristics Sequence", '', 'AnalogFilterCharacteristicsSequence'),
    0x003A0324: ('DS', '1', "Analog Filter Roll Off", '', 'AnalogFilterRollOff'),
    0x003A0325: ('SQ', '1', "Analog Filter Type Code Sequence", '', 'AnalogFilterTypeCodeSequence'),
    0x003A0326: ('SQ', '1', "Digital Filter Characteristics Sequence", '', 'DigitalFilterCharacteristicsSequence'),
    0x003A0327: ('IS', '1', "Digital Filter Order", '', 'DigitalFilterOrder'),
    0x003A0328: ('SQ', '1', "Digital Filter Type Code Sequence", '', 'DigitalFilterTypeCodeSequence'),
    0x003A0329: ('ST', '1', "Waveform Filter Description", '', 'WaveformFilterDescription'),
    0x003A032A: ('SQ', '1', "Filter Lookup Table Sequence", '', 'FilterLookupTableSequence'),
    0x003A032B: ('ST', '1', "Filter Lookup Table Description", '', 'FilterLookupTableDescription'),
    0x003A032C: ('SQ', '1', "Frequency Encoding Code Sequence", '', 'FrequencyEncodingCodeSequence'),
    0x003A032D: ('SQ', '1', "Magnitude Encoding Code Sequence", '', 'MagnitudeEncodingCodeSequence'),
    0x003A032E: ('OD', '1', "Filter Lookup Table Data", '', 'FilterLookupTableData'),
    0x00400001: ('AE', '1-n', "Scheduled Station AE Title", '', 'ScheduledStationAETitle'),
    0x00400002: ('DA', '1', "Scheduled Procedure Step Start Date", '', 'ScheduledProcedureStepStartDate'),
    0x00400003: ('TM', '1', "Scheduled Procedure Step Start Time", '', 'ScheduledProcedureStepStartTime'),
    0x00400004: ('DA', '1', "Scheduled Procedure Step End Date", '', 'ScheduledProcedureStepEndDate'),
    0x00400005: ('TM', '1', "Scheduled Procedure Step End Time", '', 'ScheduledProcedureStepEndTime'),
    0x00400006: ('PN', '1', "Scheduled Performing Physician's Name", '', 'ScheduledPerformingPhysicianName'),
    0x00400007: ('LO', '1', "Scheduled Procedure Step Description", '', 'ScheduledProcedureStepDescription'),
    0x00400008: ('SQ', '1', "Scheduled Protocol Code Sequence", '', 'ScheduledProtocolCodeSequence'),
    0x00400009: ('SH', '1', "Scheduled Procedure Step ID", '', 'ScheduledProcedureStepID'),
    0x0040000A: ('SQ', '1', "Stage Code Sequence", '', 'StageCodeSequence'),
    0x0040000B: ('SQ', '1', "Scheduled Performing Physician Identification Sequence", '', 'ScheduledPerformingPhysicianIdentificationSequence'),
    0x00400010: ('SH', '1-n', "Scheduled Station Name", '', 'ScheduledStationName'),
    0x00400011: ('SH', '1', "Scheduled Procedure Step Location", '', 'ScheduledProcedureStepLocation'),
    0x00400012: ('LO', '1', "Pre-Medication", '', 'PreMedication'),
    0x00400020: ('CS', '1', "Scheduled Procedure Step Status", '', 'ScheduledProcedureStepStatus'),
    0x00400026: ('SQ', '1', "Order Placer Identifier Sequence", '', 'OrderPlacerIdentifierSequence'),
    0x00400027: ('SQ', '1', "Order Filler Identifier Sequence", '', 'OrderFillerIdentifierSequence'),
    0x00400031: ('UT', '1', "Local Namespace Entity ID", '', 'LocalNamespaceEntityID'),
    0x00400032: ('UT', '1', "Universal Entity ID", '', 'UniversalEntityID'),
    0x00400033: ('CS', '1', "Universal Entity ID Type", '', 'UniversalEntityIDType'),
    0x00400035: ('CS', '1', "Identifier Type Code", '', 'IdentifierTypeCode'),
    0x00400036: ('SQ', '1', "Assigning Facility Sequence", '', 'AssigningFacilitySequence'),
    0x00400039: ('SQ', '1', "Assigning Jurisdiction Code Sequence", '', 'AssigningJurisdictionCodeSequence'),
    0x0040003A: ('SQ', '1', "Assigning Agency or Department Code Sequence", '', 'AssigningAgencyOrDepartmentCodeSequence'),
    0x00400100: ('SQ', '1', "Scheduled Procedure Step Sequence", '', 'ScheduledProcedureStepSequence'),
    0x00400220: ('SQ', '1', "Referenced Non-Image Composite SOP Instance Sequence", '', 'ReferencedNonImageCompositeSOPInstanceSequence'),
    0x00400241: ('AE', '1', "Performed Station AE Title", '', 'PerformedStationAETitle'),
    0x00400242: ('SH', '1', "Performed Station Name", '', 'PerformedStationName'),
    0x00400243: ('SH', '1', "Performed Location", '', 'PerformedLocation'),
    0x00400244: ('DA', '1', "Performed Procedure Step Start Date", '', 'PerformedProcedureStepStartDate'),
    0x00400245: ('TM', '1', "Performed Procedure Step Start Time", '', 'PerformedProcedureStepStartTime'),
    0x00400250: ('DA', '1', "Performed Procedure Step End Date", '', 'PerformedProcedureStepEndDate'),
    0x00400251: ('TM', '1', "Performed Procedure Step End Time", '', 'PerformedProcedureStepEndTime'),
    0x00400252: ('CS', '1', "Performed Procedure Step Status", '', 'PerformedProcedureStepStatus'),
    0x00400253: ('SH', '1', "Performed Procedure Step ID", '', 'PerformedProcedureStepID'),
    0x00400254: ('LO', '1', "Performed Procedure Step Description", '', 'PerformedProcedureStepDescription'),
    0x00400255: ('LO', '1', "Performed Procedure Type Description", '', 'PerformedProcedureTypeDescription'),
    0x00400260: ('SQ', '1', "Performed Protocol Code Sequence", '', 'PerformedProtocolCodeSequence'),
    0x00400261: ('CS', '1', "Performed Protocol Type", '', 'PerformedProtocolType'),
    0x00400270: ('SQ', '1', "Scheduled Step Attributes Sequence", '', 'ScheduledStepAttributesSequence'),
    0x00400275: ('SQ', '1', "Request Attributes Sequence", '', 'RequestAttributesSequence'),
    0x00400280: ('ST', '1', "Comments on the Performed Procedure Step", '', 'CommentsOnThePerformedProcedureStep'),
    0x00400281: ('SQ', '1', "Performed Procedure Step Discontinuation Reason Code Sequence", '', 'PerformedProcedureStepDiscontinuationReasonCodeSequence'),
    0x00400293: ('SQ', '1', "Quantity Sequence", '', 'QuantitySequence'),
    0x00400294: ('DS', '1', "Quantity", '', 'Quantity'),
    0x00400295: ('SQ', '1', "Measuring Units Sequence", '', 'MeasuringUnitsSequence'),
    0x00400296: ('SQ', '1', "Billing Item Sequence", '', 'BillingItemSequence'),
    0x00400300: ('US', '1', "Total Time of Fluoroscopy", 'Retired', 'TotalTimeOfFluoroscopy'),
    0x00400301: ('US', '1', "Total Number of Exposures", 'Retired', 'TotalNumberOfExposures'),
    0x00400302: ('US', '1', "Entrance Dose", '', 'EntranceDose'),
    0x00400303: ('US', '1-2', "Exposed Area", '', 'ExposedArea'),
    0x00400306: ('DS', '1', "Distance Source to Entrance", '', 'DistanceSourceToEntrance'),
    0x00400307: ('DS', '1', "Distance Source to Support", 'Retired', 'DistanceSourceToSupport'),
    0x0040030E: ('SQ', '1', "Exposure Dose Sequence", 'Retired', 'ExposureDoseSequence'),
    0x00400310: ('ST', '1', "Comments on Radiation Dose", '', 'CommentsOnRadiationDose'),
    0x00400312: ('DS', '1', "X-Ray Output", '', 'XRayOutput'),
    0x00400314: ('DS', '1', "Half Value Layer", '', 'HalfValueLayer'),
    0x00400316: ('DS', '1', "Organ Dose", '', 'OrganDose'),
    0x00400318: ('CS', '1', "Organ Exposed", '', 'OrganExposed'),
    0x00400320: ('SQ', '1', "Billing Procedure Step Sequence", '', 'BillingProcedureStepSequence'),
    0x00400321: ('SQ', '1', "Film Consumption Sequence", '', 'FilmConsumptionSequence'),
    0x00400324: ('SQ', '1', "Billing Supplies and Devices Sequence", '', 'BillingSuppliesAndDevicesSequence'),
    0x00400330: ('SQ', '1', "Referenced Procedure Step Sequence", 'Retired', 'ReferencedProcedureStepSequence'),
    0x00400340: ('SQ', '1', "Performed Series Sequence", '', 'PerformedSeriesSequence'),
    0x00400400: ('LT', '1', "Comments on the Scheduled Procedure Step", '', 'CommentsOnTheScheduledProcedureStep'),
    0x00400440: ('SQ', '1', "Protocol Context Sequence", '', 'ProtocolContextSequence'),
    0x00400441: ('SQ', '1', "Content Item Modifier Sequence", '', 'ContentItemModifierSequence'),
    0x00400500: ('SQ', '1', "Scheduled Specimen Sequence", '', 'ScheduledSpecimenSequence'),
    0x0040050A: ('LO', '1', "Specimen Accession Number", 'Retired', 'SpecimenAccessionNumber'),
    0x00400512: ('LO', '1', "Container Identifier", '', 'ContainerIdentifier'),
    0x00400513: ('SQ', '1', "Issuer of the Container Identifier Sequence", '', 'IssuerOfTheContainerIdentifierSequence'),
    0x00400515: ('SQ', '1', "Alternate Container Identifier Sequence", '', 'AlternateContainerIdentifierSequence'),
    0x00400518: ('SQ', '1', "Container Type Code Sequence", '', 'ContainerTypeCodeSequence'),
    0x0040051A: ('LO', '1', "Container Description", '', 'ContainerDescription'),
    0x00400520: ('SQ', '1', "Container Component Sequence", '', 'ContainerComponentSequence'),
    0x00400550: ('SQ', '1', "Specimen Sequence", 'Retired', 'SpecimenSequence'),
    0x00400551: ('LO', '1', "Specimen Identifier", '', 'SpecimenIdentifier'),
    0x00400552: ('SQ', '1', "Specimen Description Sequence (Trial)", 'Retired', 'SpecimenDescriptionSequenceTrial'),
    0x00400553: ('ST', '1', "Specimen Description (Trial)", 'Retired', 'SpecimenDescriptionTrial'),
    0x00400554: ('UI', '1', "Specimen UID", '', 'SpecimenUID'),
    0x00400555: ('SQ', '1', "Acquisition Context Sequence", '', 'AcquisitionContextSequence'),
    0x00400556: ('ST', '1', "Acquisition Context Description", '', 'AcquisitionContextDescription'),
    0x00400560: ('SQ', '1', "Specimen Description Sequence", '', 'SpecimenDescriptionSequence'),
    0x00400562: ('SQ', '1', "Issuer of the Specimen Identifier Sequence", '', 'IssuerOfTheSpecimenIdentifierSequence'),
    0x0040059A: ('SQ', '1', "Specimen Type Code Sequence", '', 'SpecimenTypeCodeSequence'),
    0x00400600: ('LO', '1', "Specimen Short Description", '', 'SpecimenShortDescription'),
    0x00400602: ('UT', '1', "Specimen Detailed Description", '', 'SpecimenDetailedDescription'),
    0x00400610: ('SQ', '1', "Specimen Preparation Sequence", '', 'SpecimenPreparationSequence'),
    0x00400612: ('SQ', '1', "Specimen Preparation Step Content Item Sequence", '', 'SpecimenPreparationStepContentItemSequence'),
    0x00400620: ('SQ', '1', "Specimen Localization Content Item Sequence", '', 'SpecimenLocalizationContentItemSequence'),
    0x004006FA: ('LO', '1', "Slide Identifier", 'Retired', 'SlideIdentifier'),
    0x00400710: ('SQ', '1', "Whole Slide Microscopy Image Frame Type Sequence", '', 'WholeSlideMicroscopyImageFrameTypeSequence'),
    0x0040071A: ('SQ', '1', "Image Center Point Coordinates Sequence", '', 'ImageCenterPointCoordinatesSequence'),
    0x0040072A: ('DS', '1', "X Offset in Slide Coordinate System", '', 'XOffsetInSlideCoordinateSystem'),
    0x0040073A: ('DS', '1', "Y Offset in Slide Coordinate System", '', 'YOffsetInSlideCoordinateSystem'),
    0x0040074A: ('DS', '1', "Z Offset in Slide Coordinate System", '', 'ZOffsetInSlideCoordinateSystem'),
    0x004008D8: ('SQ', '1', "Pixel Spacing Sequence", 'Retired', 'PixelSpacingSequence'),
    0x004008DA: ('SQ', '1', "Coordinate System Axis Code Sequence", 'Retired', 'CoordinateSystemAxisCodeSequence'),
    0x004008EA: ('SQ', '1', "Measurement Units Code Sequence", '', 'MeasurementUnitsCodeSequence'),
    0x004009F8: ('SQ', '1', "Vital Stain Code Sequence (Trial)", 'Retired', 'VitalStainCodeSequenceTrial'),
    0x00401001: ('SH', '1', "Requested Procedure ID", '', 'RequestedProcedureID'),
    0x00401002: ('LO', '1', "Reason for the Requested Procedure", '', 'ReasonForTheRequestedProcedure'),
    0x00401003: ('SH', '1', "Requested Procedure Priority", '', 'RequestedProcedurePriority'),
    0x00401004: ('LO', '1', "Patient Transport Arrangements", '', 'PatientTransportArrangements'),
    0x00401005: ('LO', '1', "Requested Procedure Location", '', 'RequestedProcedureLocation'),
    0x00401006: ('SH', '1', "Placer Order Number / Procedure", 'Retired', 'PlacerOrderNumberProcedure'),
    0x00401007: ('SH', '1', "Filler Order Number / Procedure", 'Retired', 'FillerOrderNumberProcedure'),
    0x00401008: ('LO', '1', "Confidentiality Code", '', 'ConfidentialityCode'),
    0x00401009: ('SH', '1', "Reporting Priority", '', 'ReportingPriority'),
    0x0040100A: ('SQ', '1', "Reason for Requested Procedure Code Sequence", '', 'ReasonForRequestedProcedureCodeSequence'),
    0x00401010: ('PN', '1-n', "Names of Intended Recipients of Results", '', 'NamesOfIntendedRecipientsOfResults'),
    0x00401011: ('SQ', '1', "Intended Recipients of Results Identification Sequence", '', 'IntendedRecipientsOfResultsIdentificationSequence'),
    0x00401012: ('SQ', '1', "Reason For Performed Procedure Code Sequence", '', 'ReasonForPerformedProcedureCodeSequence'),
    0x00401060: ('LO', '1', "Requested Procedure Description (Trial)", 'Retired', 'RequestedProcedureDescriptionTrial'),
    0x00401101: ('SQ', '1', "Person Identification Code Sequence", '', 'PersonIdentificationCodeSequence'),
    0x00401102: ('ST', '1', "Person's Address", '', 'PersonAddress'),
    0x00401103: ('LO', '1-n', "Person's Telephone Numbers", '', 'PersonTelephoneNumbers'),
    0x00401104: ('LT', '1', "Person's Telecom Information", '', 'PersonTelecomInformation'),
    0x00401400: ('LT', '1', "Requested Procedure Comments", '', 'RequestedProcedureComments'),
    0x00402001: ('LO', '1', "Reason for the Imaging Service Request", 'Retired', 'ReasonForTheImagingServiceRequest'),
    0x00402004: ('DA', '1', "Issue Date of Imaging Service Request", '', 'IssueDateOfImagingServiceRequest'),
    0x00402005: ('TM', '1', "Issue Time of Imaging Service Request", '', 'IssueTimeOfImagingServiceRequest'),
    0x00402006: ('SH', '1', "Placer Order Number / Imaging Service Request (Retired)", 'Retired', 'PlacerOrderNumberImagingServiceRequestRetired'),
    0x00402007: ('SH', '1', "Filler Order Number / Imaging Service Request (Retired)", 'Retired', 'FillerOrderNumberImagingServiceRequestRetired'),
    0x00402008: ('PN', '1', "Order Entered By", '', 'OrderEnteredBy'),
    0x00402009: ('SH', '1', "Order Enterer's Location", '', 'OrderEntererLocation'),
    0x00402010: ('SH', '1', "Order Callback Phone Number", '', 'OrderCallbackPhoneNumber'),
    0x00402011: ('LT', '1', "Order Callback Telecom Information", '', 'OrderCallbackTelecomInformation'),
    0x00402016: ('LO', '1', "Placer Order Number / Imaging Service Request", '', 'PlacerOrderNumberImagingServiceRequest'),
    0x00402017: ('LO', '1', "Filler Order Number / Imaging Service Request", '', 'FillerOrderNumberImagingServiceRequest'),
    0x00402400: ('LT', '1', "Imaging Service Request Comments", '', 'ImagingServiceRequestComments'),
    0x00403001: ('LO', '1', "Confidentiality Constraint on Patient Data Description", '', 'ConfidentialityConstraintOnPatientDataDescription'),
    0x00404001: ('CS', '1', "General Purpose Scheduled Procedure Step Status", 'Retired', 'GeneralPurposeScheduledProcedureStepStatus'),
    0x00404002: ('CS', '1', "General Purpose Performed Procedure Step Status", 'Retired', 'GeneralPurposePerformedProcedureStepStatus'),
    0x00404003: ('CS', '1', "General Purpose Scheduled Procedure Step Priority", 'Retired', 'GeneralPurposeScheduledProcedureStepPriority'),
    0x00404004: ('SQ', '1', "Scheduled Processing Applications Code Sequence", 'Retired', 'ScheduledProcessingApplicationsCodeSequence'),
    0x00404005: ('DT', '1', "Scheduled Procedure Step Start DateTime", '', 'ScheduledProcedureStepStartDateTime'),
    0x00404006: ('CS', '1', "Multiple Copies Flag", 'Retired', 'MultipleCopiesFlag'),
    0x00404007: ('SQ', '1', "Performed Processing Applications Code Sequence", 'Retired', 'PerformedProcessingApplicationsCodeSequence'),
    0x00404008: ('DT', '1', "Scheduled Procedure Step Expiration DateTime", '', 'ScheduledProcedureStepExpirationDateTime'),
    0x00404009: ('SQ', '1', "Human Performer Code Sequence", '', 'HumanPerformerCodeSequence'),
    0x00404010: ('DT', '1', "Scheduled Procedure Step Modification DateTime", '', 'ScheduledProcedureStepModificationDateTime'),
    0x00404011: ('DT', '1', "Expected Completion DateTime", '', 'ExpectedCompletionDateTime'),
    0x00404015: ('SQ', '1', "Resulting General Purpose Performed Procedure Steps Sequence", 'Retired', 'ResultingGeneralPurposePerformedProcedureStepsSequence'),
    0x00404016: ('SQ', '1', "Referenced General Purpose Scheduled Procedure Step Sequence", 'Retired', 'ReferencedGeneralPurposeScheduledProcedureStepSequence'),
    0x00404018: ('SQ', '1', "Scheduled Workitem Code Sequence", '', 'ScheduledWorkitemCodeSequence'),
    0x00404019: ('SQ', '1', "Performed Workitem Code Sequence", '', 'PerformedWorkitemCodeSequence'),
    0x00404020: ('CS', '1', "Input Availability Flag", 'Retired', 'InputAvailabilityFlag'),
    0x00404021: ('SQ', '1', "Input Information Sequence", '', 'InputInformationSequence'),
    0x00404022: ('SQ', '1', "Relevant Information Sequence", 'Retired', 'RelevantInformationSequence'),
    0x00404023: ('UI', '1', "Referenced General Purpose Scheduled Procedure Step Transaction UID", 'Retired', 'ReferencedGeneralPurposeScheduledProcedureStepTransactionUID'),
    0x00404025: ('SQ', '1', "Scheduled Station Name Code Sequence", '', 'ScheduledStationNameCodeSequence'),
    0x00404026: ('SQ', '1', "Scheduled Station Class Code Sequence", '', 'ScheduledStationClassCodeSequence'),
    0x00404027: ('SQ', '1', "Scheduled Station Geographic Location Code Sequence", '', 'ScheduledStationGeographicLocationCodeSequence'),
    0x00404028: ('SQ', '1', "Performed Station Name Code Sequence", '', 'PerformedStationNameCodeSequence'),
    0x00404029: ('SQ', '1', "Performed Station Class Code Sequence", '', 'PerformedStationClassCodeSequence'),
    0x00404030: ('SQ', '1', "Performed Station Geographic Location Code Sequence", '', 'PerformedStationGeographicLocationCodeSequence'),
    0x00404031: ('SQ', '1', "Requested Subsequent Workitem Code Sequence", 'Retired', 'RequestedSubsequentWorkitemCodeSequence'),
    0x00404032: ('SQ', '1', "Non-DICOM Output Code Sequence", 'Retired', 'NonDICOMOutputCodeSequence'),
    0x00404033: ('SQ', '1', "Output Information Sequence", '', 'OutputInformationSequence'),
    0x00404034: ('SQ', '1', "Scheduled Human Performers Sequence", '', 'ScheduledHumanPerformersSequence'),
    0x00404035: ('SQ', '1', "Actual Human Performers Sequence", '', 'ActualHumanPerformersSequence'),
    0x00404036: ('LO', '1', "Human Performer's Organization", '', 'HumanPerformerOrganization'),
    0x00404037: ('PN', '1', "Human Performer's Name", '', 'HumanPerformerName'),
    0x00404040: ('CS', '1', "Raw Data Handling", '', 'RawDataHandling'),
    0x00404041: ('CS', '1', "Input Readiness State", '', 'InputReadinessState'),
    0x00404050: ('DT', '1', "Performed Procedure Step Start DateTime", '', 'PerformedProcedureStepStartDateTime'),
    0x00404051: ('DT', '1', "Performed Procedure Step End DateTime", '', 'PerformedProcedureStepEndDateTime'),
    0x00404052: ('DT', '1', "Procedure Step Cancellation DateTime", '', 'ProcedureStepCancellationDateTime'),
    0x00404070: ('SQ', '1', "Output Destination Sequence", '', 'OutputDestinationSequence'),
    0x00404071: ('SQ', '1', "DICOM Storage Sequence", '', 'DICOMStorageSequence'),
    0x00404072: ('SQ', '1', "STOW-RS Storage Sequence", '', 'STOWRSStorageSequence'),
    0x00404073: ('UR', '1', "Storage URL", '', 'StorageURL'),
    0x00404074: ('SQ', '1', "XDS Storage Sequence", '', 'XDSStorageSequence'),
    0x00408302: ('DS', '1', "Entrance Dose in mGy", '', 'EntranceDoseInmGy'),
    0x00408303: ('CS', '1', "Entrance Dose Derivation", '', 'EntranceDoseDerivation'),
    0x00409092: ('SQ', '1', "Parametric Map Frame Type Sequence", '', 'ParametricMapFrameTypeSequence'),
    0x00409094: ('SQ', '1', "Referenced Image Real World Value Mapping Sequence", '', 'ReferencedImageRealWorldValueMappingSequence'),
    0x00409096: ('SQ', '1', "Real World Value Mapping Sequence", '', 'RealWorldValueMappingSequence'),
    0x00409098: ('SQ', '1', "Pixel Value Mapping Code Sequence", '', 'PixelValueMappingCodeSequence'),
    0x00409210: ('SH', '1', "LUT Label", '', 'LUTLabel'),
    0x00409211: ('US or SS', '1', "Real World Value Last Value Mapped", '', 'RealWorldValueLastValueMapped'),
    0x00409212: ('FD', '1-n', "Real World Value LUT Data", '', 'RealWorldValueLUTData'),
    0x00409213: ('FD', '1', "Double Float Real World Value Last Value Mapped", '', 'DoubleFloatRealWorldValueLastValueMapped'),
    0x00409214: ('FD', '1', "Double Float Real World Value First Value Mapped", '', 'DoubleFloatRealWorldValueFirstValueMapped'),
    0x00409216: ('US or SS', '1', "Real World Value First Value Mapped", '', 'RealWorldValueFirstValueMapped'),
    0x00409220: ('SQ', '1', "Quantity Definition Sequence", '', 'QuantityDefinitionSequence'),
    0x00409224: ('FD', '1', "Real World Value Intercept", '', 'RealWorldValueIntercept'),
    0x00409225: ('FD', '1', "Real World Value Slope", '', 'RealWorldValueSlope'),
    0x0040A007: ('CS', '1', "Findings Flag (Trial)", 'Retired', 'FindingsFlagTrial'),
    0x0040A010: ('CS', '1', "Relationship Type", '', 'RelationshipType'),
    0x0040A020: ('SQ', '1', "Findings Sequence (Trial)", 'Retired', 'FindingsSequenceTrial'),
    0x0040A021: ('UI', '1', "Findings Group UID (Trial)", 'Retired', 'FindingsGroupUIDTrial'),
    0x0040A022: ('UI', '1', "Referenced Findings Group UID (Trial)", 'Retired', 'ReferencedFindingsGroupUIDTrial'),
    0x0040A023: ('DA', '1', "Findings Group Recording Date (Trial)", 'Retired', 'FindingsGroupRecordingDateTrial'),
    0x0040A024: ('TM', '1', "Findings Group Recording Time (Trial)", 'Retired', 'FindingsGroupRecordingTimeTrial'),
    0x0040A026: ('SQ', '1', "Findings Source Category Code Sequence (Trial)", 'Retired', 'FindingsSourceCategoryCodeSequenceTrial'),
    0x0040A027: ('LO', '1', "Verifying Organization", '', 'VerifyingOrganization'),
    0x0040A028: ('SQ', '1', "Documenting Organization Identifier Code Sequence (Trial)", 'Retired', 'DocumentingOrganizationIdentifierCodeSequenceTrial'),
    0x0040A030: ('DT', '1', "Verification DateTime", '', 'VerificationDateTime'),
    0x0040A032: ('DT', '1', "Observation DateTime", '', 'ObservationDateTime'),
    0x0040A033: ('DT', '1', "Observation Start DateTime", '', 'ObservationStartDateTime'),
    0x0040A040: ('CS', '1', "Value Type", '', 'ValueType'),
    0x0040A043: ('SQ', '1', "Concept Name Code Sequence", '', 'ConceptNameCodeSequence'),
    0x0040A047: ('LO', '1', "Measurement Precision Description (Trial)", 'Retired', 'MeasurementPrecisionDescriptionTrial'),
    0x0040A050: ('CS', '1', "Continuity Of Content", '', 'ContinuityOfContent'),
    0x0040A057: ('CS', '1-n', "Urgency or Priority Alerts (Trial)", 'Retired', 'UrgencyOrPriorityAlertsTrial'),
    0x0040A060: ('LO', '1', "Sequencing Indicator (Trial)", 'Retired', 'SequencingIndicatorTrial'),
    0x0040A066: ('SQ', '1', "Document Identifier Code Sequence (Trial)", 'Retired', 'DocumentIdentifierCodeSequenceTrial'),
    0x0040A067: ('PN', '1', "Document Author (Trial)", 'Retired', 'DocumentAuthorTrial'),
    0x0040A068: ('SQ', '1', "Document Author Identifier Code Sequence (Trial)", 'Retired', 'DocumentAuthorIdentifierCodeSequenceTrial'),
    0x0040A070: ('SQ', '1', "Identifier Code Sequence (Trial)", 'Retired', 'IdentifierCodeSequenceTrial'),
    0x0040A073: ('SQ', '1', "Verifying Observer Sequence", '', 'VerifyingObserverSequence'),
    0x0040A074: ('OB', '1', "Object Binary Identifier (Trial)", 'Retired', 'ObjectBinaryIdentifierTrial'),
    0x0040A075: ('PN', '1', "Verifying Observer Name", '', 'VerifyingObserverName'),
    0x0040A076: ('SQ', '1', "Documenting Observer Identifier Code Sequence (Trial)", 'Retired', 'DocumentingObserverIdentifierCodeSequenceTrial'),
    0x0040A078: ('SQ', '1', "Author Observer Sequence", '', 'AuthorObserverSequence'),
    0x0040A07A: ('SQ', '1', "Participant Sequence", '', 'ParticipantSequence'),
    0x0040A07C: ('SQ', '1', "Custodial Organization Sequence", '', 'CustodialOrganizationSequence'),
    0x0040A080: ('CS', '1', "Participation Type", '', 'ParticipationType'),
    0x0040A082: ('DT', '1', "Participation DateTime", '', 'ParticipationDateTime'),
    0x0040A084: ('CS', '1', "Observer Type", '', 'ObserverType'),
    0x0040A085: ('SQ', '1', "Procedure Identifier Code Sequence (Trial)", 'Retired', 'ProcedureIdentifierCodeSequenceTrial'),
    0x0040A088: ('SQ', '1', "Verifying Observer Identification Code Sequence", '', 'VerifyingObserverIdentificationCodeSequence'),
    0x0040A089: ('OB', '1', "Object Directory Binary Identifier (Trial)", 'Retired', 'ObjectDirectoryBinaryIdentifierTrial'),
    0x0040A090: ('SQ', '1', "Equivalent CDA Document Sequence", 'Retired', 'EquivalentCDADocumentSequence'),
    0x0040A0B0: ('US', '2-2n', "Referenced Waveform Channels", '', 'ReferencedWaveformChannels'),
    0x0040A110: ('DA', '1', "Date of Document or Verbal Transaction (Trial)", 'Retired', 'DateOfDocumentOrVerbalTransactionTrial'),
    0x0040A112: ('TM', '1', "Time of Document Creation or Verbal Transaction (Trial)", 'Retired', 'TimeOfDocumentCreationOrVerbalTransactionTrial'),
    0x0040A120: ('DT', '1', "DateTime", '', 'DateTime'),
    0x0040A121: ('DA', '1', "Date", '', 'Date'),
    0x0040A122: ('TM', '1', "Time", '', 'Time'),
    0x0040A123: ('PN', '1', "Person Name", '', 'PersonName'),
    0x0040A124: ('UI', '1', "UID", '', 'UID'),
    0x0040A125: ('CS', '2', "Report Status ID (Trial)", 'Retired', 'ReportStatusIDTrial'),
    0x0040A130: ('CS', '1', "Temporal Range Type", '', 'TemporalRangeType'),
    0x0040A132: ('UL', '1-n', "Referenced Sample Positions", '', 'ReferencedSamplePositions'),
    0x0040A136: ('US', '1-n', "Referenced Frame Numbers", 'Retired', 'ReferencedFrameNumbers'),
    0x0040A138: ('DS', '1-n', "Referenced Time Offsets", '', 'ReferencedTimeOffsets'),
    0x0040A13A: ('DT', '1-n', "Referenced DateTime", '', 'ReferencedDateTime'),
    0x0040A160: ('UT', '1', "Text Value", '', 'TextValue'),
    0x0040A161: ('FD', '1-n', "Floating Point Value", '', 'FloatingPointValue'),
    0x0040A162: ('SL', '1-n', "Rational Numerator Value", '', 'RationalNumeratorValue'),
    0x0040A163: ('UL', '1-n', "Rational Denominator Value", '', 'RationalDenominatorValue'),
    0x0040A167: ('SQ', '1', "Observation Category Code Sequence (Trial)", 'Retired', 'ObservationCategoryCodeSequenceTrial'),
    0x0040A168: ('SQ', '1', "Concept Code Sequence", '', 'ConceptCodeSequence'),
    0x0040A16A: ('ST', '1', "Bibliographic Citation (Trial)", 'Retired', 'BibliographicCitationTrial'),
    0x0040A170: ('SQ', '1', "Purpose of Reference Code Sequence", '', 'PurposeOfReferenceCodeSequence'),
    0x0040A171: ('UI', '1', "Observation UID", '', 'ObservationUID'),
    0x0040A172: ('UI', '1', "Referenced Observation UID (Trial)", 'Retired', 'ReferencedObservationUIDTrial'),
    0x0040A173: ('CS', '1', "Referenced Observation Class (Trial)", 'Retired', 'ReferencedObservationClassTrial'),
    0x0040A174: ('CS', '1', "Referenced Object Observation Class (Trial)", 'Retired', 'ReferencedObjectObservationClassTrial'),
    0x0040A180: ('US', '1', "Annotation Group Number", '', 'AnnotationGroupNumber'),
    0x0040A192: ('DA', '1', "Observation Date (Trial)", 'Retired', 'ObservationDateTrial'),
    0x0040A193: ('TM', '1', "Observation Time (Trial)", 'Retired', 'ObservationTimeTrial'),
    0x0040A194: ('CS', '1', "Measurement Automation (Trial)", 'Retired', 'MeasurementAutomationTrial'),
    0x0040A195: ('SQ', '1', "Modifier Code Sequence", '', 'ModifierCodeSequence'),
    0x0040A224: ('ST', '1', "Identification Description (Trial)", 'Retired', 'IdentificationDescriptionTrial'),
    0x0040A290: ('CS', '1', "Coordinates Set Geometric Type (Trial)", 'Retired', 'CoordinatesSetGeometricTypeTrial'),
    0x0040A296: ('SQ', '1', "Algorithm Code Sequence (Trial)", 'Retired', 'AlgorithmCodeSequenceTrial'),
    0x0040A297: ('ST', '1', "Algorithm Description (Trial)", 'Retired', 'AlgorithmDescriptionTrial'),
    0x0040A29A: ('SL', '2-2n', "Pixel Coordinates Set (Trial)", 'Retired', 'PixelCoordinatesSetTrial'),
    0x0040A300: ('SQ', '1', "Measured Value Sequence", '', 'MeasuredValueSequence'),
    0x0040A301: ('SQ', '1', "Numeric Value Qualifier Code Sequence", '', 'NumericValueQualifierCodeSequence'),
    0x0040A307: ('PN', '1', "Current Observer (Trial)", 'Retired', 'CurrentObserverTrial'),
    0x0040A30A: ('DS', '1-n', "Numeric Value", '', 'NumericValue'),
    0x0040A313: ('SQ', '1', "Referenced Accession Sequence (Trial)", 'Retired', 'ReferencedAccessionSequenceTrial'),
    0x0040A33A: ('ST', '1', "Report Status Comment (Trial)", 'Retired', 'ReportStatusCommentTrial'),
    0x0040A340: ('SQ', '1', "Procedure Context Sequence (Trial)", 'Retired', 'ProcedureContextSequenceTrial'),
    0x0040A352: ('PN', '1', "Verbal Source (Trial)", 'Retired', 'VerbalSourceTrial'),
    0x0040A353: ('ST', '1', "Address (Trial)", 'Retired', 'AddressTrial'),
    0x0040A354: ('LO', '1', "Telephone Number (Trial)", 'Retired', 'TelephoneNumberTrial'),
    0x0040A358: ('SQ', '1', "Verbal Source Identifier Code Sequence (Trial)", 'Retired', 'VerbalSourceIdentifierCodeSequenceTrial'),
    0x0040A360: ('SQ', '1', "Predecessor Documents Sequence", '', 'PredecessorDocumentsSequence'),
    0x0040A370: ('SQ', '1', "Referenced Request Sequence", '', 'ReferencedRequestSequence'),
    0x0040A372: ('SQ', '1', "Performed Procedure Code Sequence", '', 'PerformedProcedureCodeSequence'),
    0x0040A375: ('SQ', '1', "Current Requested Procedure Evidence Sequence", '', 'CurrentRequestedProcedureEvidenceSequence'),
    0x0040A380: ('SQ', '1', "Report Detail Sequence (Trial)", 'Retired', 'ReportDetailSequenceTrial'),
    0x0040A385: ('SQ', '1', "Pertinent Other Evidence Sequence", '', 'PertinentOtherEvidenceSequence'),
    0x0040A390: ('SQ', '1', "HL7 Structured Document Reference Sequence", '', 'HL7StructuredDocumentReferenceSequence'),
    0x0040A402: ('UI', '1', "Observation Subject UID (Trial)", 'Retired', 'ObservationSubjectUIDTrial'),
    0x0040A403: ('CS', '1', "Observation Subject Class (Trial)", 'Retired', 'ObservationSubjectClassTrial'),
    0x0040A404: ('SQ', '1', "Observation Subject Type Code Sequence (Trial)", 'Retired', 'ObservationSubjectTypeCodeSequenceTrial'),
    0x0040A491: ('CS', '1', "Completion Flag", '', 'CompletionFlag'),
    0x0040A492: ('LO', '1', "Completion Flag Description", '', 'CompletionFlagDescription'),
    0x0040A493: ('CS', '1', "Verification Flag", '', 'VerificationFlag'),
    0x0040A494: ('CS', '1', "Archive Requested", '', 'ArchiveRequested'),
    0x0040A496: ('CS', '1', "Preliminary Flag", '', 'PreliminaryFlag'),
    0x0040A504: ('SQ', '1', "Content Template Sequence", '', 'ContentTemplateSequence'),
    0x0040A525: ('SQ', '1', "Identical Documents Sequence", '', 'IdenticalDocumentsSequence'),
    0x0040A600: ('CS', '1', "Observation Subject Context Flag (Trial)", 'Retired', 'ObservationSubjectContextFlagTrial'),
    0x0040A601: ('CS', '1', "Observer Context Flag (Trial)", 'Retired', 'ObserverContextFlagTrial'),
    0x0040A603: ('CS', '1', "Procedure Context Flag (Trial)", 'Retired', 'ProcedureContextFlagTrial'),
    0x0040A730: ('SQ', '1', "Content Sequence", '', 'ContentSequence'),
    0x0040A731: ('SQ', '1', "Relationship Sequence (Trial)", 'Retired', 'RelationshipSequenceTrial'),
    0x0040A732: ('SQ', '1', "Relationship Type Code Sequence (Trial)", 'Retired', 'RelationshipTypeCodeSequenceTrial'),
    0x0040A744: ('SQ', '1', "Language Code Sequence (Trial)", 'Retired', 'LanguageCodeSequenceTrial'),
    0x0040A801: ('SQ', '1', "Tabulated Values Sequence", '', 'TabulatedValuesSequence'),
    0x0040A802: ('UL', '1', "Number of Table Rows", '', 'NumberOfTableRows'),
    0x0040A803: ('UL', '1', "Number of Table Columns", '', 'NumberOfTableColumns'),
    0x0040A804: ('UL', '1', "Table Row Number", '', 'TableRowNumber'),
    0x0040A805: ('UL', '1', "Table Column Number", '', 'TableColumnNumber'),
    0x0040A806: ('SQ', '1', "Table Row Definition Sequence", '', 'TableRowDefinitionSequence'),
    0x0040A807: ('SQ', '1', "Table Column Definition Sequence", '', 'TableColumnDefinitionSequence'),
    0x0040A808: ('SQ', '1', "Cell Values Sequence", '', 'CellValuesSequence'),
    0x0040A992: ('ST', '1', "Uniform Resource Locator (Trial)", 'Retired', 'UniformResourceLocatorTrial'),
    0x0040B020: ('SQ', '1', "Waveform Annotation Sequence", '', 'WaveformAnnotationSequence'),
    0x0040DB00: ('CS', '1', "Template Identifier", '', 'TemplateIdentifier'),
    0x0040DB06: ('DT', '1', "Template Version", 'Retired', 'TemplateVersion'),
    0x0040DB07: ('DT', '1', "Template Local Version", 'Retired', 'TemplateLocalVersion'),
    0x0040DB0B: ('CS', '1', "Template Extension Flag", 'Retired', 'TemplateExtensionFlag'),
    0x0040DB0C: ('UI', '1', "Template Extension Organization UID", 'Retired', 'TemplateExtensionOrganizationUID'),
    0x0040DB0D: ('UI', '1', "Template Extension Creator UID", 'Retired', 'TemplateExtensionCreatorUID'),
    0x0040DB73: ('UL', '1-n', "Referenced Content Item Identifier", '', 'ReferencedContentItemIdentifier'),
    0x0040E001: ('ST', '1', "HL7 Instance Identifier", '', 'HL7InstanceIdentifier'),
    0x0040E004: ('DT', '1', "HL7 Document Effective Time", '', 'HL7DocumentEffectiveTime'),
    0x0040E006: ('SQ', '1', "HL7 Document Type Code Sequence", '', 'HL7DocumentTypeCodeSequence'),
    0x0040E008: ('SQ', '1', "Document Class Code Sequence", '', 'DocumentClassCodeSequence'),
    0x0040E010: ('UR', '1', "Retrieve URI", '', 'RetrieveURI'),
    0x0040E011: ('UI', '1', "Retrieve Location UID", '', 'RetrieveLocationUID'),
    0x0040E020: ('CS', '1', "Type of Instances", '', 'TypeOfInstances'),
    0x0040E021: ('SQ', '1', "DICOM Retrieval Sequence", '', 'DICOMRetrievalSequence'),
    0x0040E022: ('SQ', '1', "DICOM Media Retrieval Sequence", '', 'DICOMMediaRetrievalSequence'),
    0x0040E023: ('SQ', '1', "WADO Retrieval Sequence", '', 'WADORetrievalSequence'),
    0x0040E024: ('SQ', '1', "XDS Retrieval Sequence", '', 'XDSRetrievalSequence'),
    0x0040E025: ('SQ', '1', "WADO-RS Retrieval Sequence", '', 'WADORSRetrievalSequence'),
    0x0040E030: ('UI', '1', "Repository Unique ID", '', 'RepositoryUniqueID'),
    0x0040E031: ('UI', '1', "Home Community ID", '', 'HomeCommunityID'),
    0x00420010: ('ST', '1', "Document Title", '', 'DocumentTitle'),
    0x00420011: ('OB', '1', "Encapsulated Document", '', 'EncapsulatedDocument'),
    0x00420012: ('LO', '1', "MIME Type of Encapsulated Document", '', 'MIMETypeOfEncapsulatedDocument'),
    0x00420013: ('SQ', '1', "Source Instance Sequence", '', 'SourceInstanceSequence'),
    0x00420014: ('LO', '1-n', "List of MIME Types", '', 'ListOfMIMETypes'),
    0x00420015: ('UL', '1', "Encapsulated Document Length", '', 'EncapsulatedDocumentLength'),
    0x00440001: ('ST', '1', "Product Package Identifier", '', 'ProductPackageIdentifier'),
    0x00440002: ('CS', '1', "Substance Administration Approval", '', 'SubstanceAdministrationApproval'),
    0x00440003: ('LT', '1', "Approval Status Further Description", '', 'ApprovalStatusFurtherDescription'),
    0x00440004: ('DT', '1', "Approval Status DateTime", '', 'ApprovalStatusDateTime'),
    0x00440007: ('SQ', '1', "Product Type Code Sequence", '', 'ProductTypeCodeSequence'),
    0x00440008: ('LO', '1-n', "Product Name", '', 'ProductName'),
    0x00440009: ('LT', '1', "Product Description", '', 'ProductDescription'),
    0x0044000A: ('LO', '1', "Product Lot Identifier", '', 'ProductLotIdentifier'),
    0x0044000B: ('DT', '1', "Product Expiration DateTime", '', 'ProductExpirationDateTime'),
    0x00440010: ('DT', '1', "Substance Administration DateTime", '', 'SubstanceAdministrationDateTime'),
    0x00440011: ('LO', '1', "Substance Administration Notes", '', 'SubstanceAdministrationNotes'),
    0x00440012: ('LO', '1', "Substance Administration Device ID", '', 'SubstanceAdministrationDeviceID'),
    0x00440013: ('SQ', '1', "Product Parameter Sequence", '', 'ProductParameterSequence'),
    0x00440019: ('SQ', '1', "Substance Administration Parameter Sequence", '', 'SubstanceAdministrationParameterSequence'),
    0x00440100: ('SQ', '1', "Approval Sequence", '', 'ApprovalSequence'),
    0x00440101: ('SQ', '1', "Assertion Code Sequence", '', 'AssertionCodeSequence'),
    0x00440102: ('UI', '1', "Assertion UID", '', 'AssertionUID'),
    0x00440103: ('SQ', '1', "Asserter Identification Sequence", '', 'AsserterIdentificationSequence'),
    0x00440104: ('DT', '1', "Assertion DateTime", '', 'AssertionDateTime'),
    0x00440105: ('DT', '1', "Assertion Expiration DateTime", '', 'AssertionExpirationDateTime'),
    0x00440106: ('UT', '1', "Assertion Comments", '', 'AssertionComments'),
    0x00440107: ('SQ', '1', "Related Assertion Sequence", '', 'RelatedAssertionSequence'),
    0x00440108: ('UI', '1', "Referenced Assertion UID", '', 'ReferencedAssertionUID'),
    0x00440109: ('SQ', '1', "Approval Subject Sequence", '', 'ApprovalSubjectSequence'),
    0x0044010A: ('SQ', '1', "Organizational Role Code Sequence", '', 'OrganizationalRoleCodeSequence'),
    0x00460012: ('LO', '1', "Lens Description", '', 'LensDescription'),
    0x00460014: ('SQ', '1', "Right Lens Sequence", '', 'RightLensSequence'),
    0x00460015: ('SQ', '1', "Left Lens Sequence", '', 'LeftLensSequence'),
    0x00460016: ('SQ', '1', "Unspecified Laterality Lens Sequence", '', 'UnspecifiedLateralityLensSequence'),
    0x00460018: ('SQ', '1', "Cylinder Sequence", '', 'CylinderSequence'),
    0x00460028: ('SQ', '1', "Prism Sequence", '', 'PrismSequence'),
    0x00460030: ('FD', '1', "Horizontal Prism Power", '', 'HorizontalPrismPower'),
    0x00460032: ('CS', '1', "Horizontal Prism Base", '', 'HorizontalPrismBase'),
    0x00460034: ('FD', '1', "Vertical Prism Power", '', 'VerticalPrismPower'),
    0x00460036: ('CS', '1', "Vertical Prism Base", '', 'VerticalPrismBase'),
    0x00460038: ('CS', '1', "Lens Segment Type", '', 'LensSegmentType'),
    0x00460040: ('FD', '1', "Optical Transmittance", '', 'OpticalTransmittance'),
    0x00460042: ('FD', '1', "Channel Width", '', 'ChannelWidth'),
    0x00460044: ('FD', '1', "Pupil Size", '', 'PupilSize'),
    0x00460046: ('FD', '1', "Corneal Size", '', 'CornealSize'),
    0x00460047: ('SQ', '1', "Corneal Size Sequence", '', 'CornealSizeSequence'),
    0x00460050: ('SQ', '1', "Autorefraction Right Eye Sequence", '', 'AutorefractionRightEyeSequence'),
    0x00460052: ('SQ', '1', "Autorefraction Left Eye Sequence", '', 'AutorefractionLeftEyeSequence'),
    0x00460060: ('FD', '1', "Distance Pupillary Distance", '', 'DistancePupillaryDistance'),
    0x00460062: ('FD', '1', "Near Pupillary Distance", '', 'NearPupillaryDistance'),
    0x00460063: ('FD', '1', "Intermediate Pupillary Distance", '', 'IntermediatePupillaryDistance'),
    0x00460064: ('FD', '1', "Other Pupillary Distance", '', 'OtherPupillaryDistance'),
    0x00460070: ('SQ', '1', "Keratometry Right Eye Sequence", '', 'KeratometryRightEyeSequence'),
    0x00460071: ('SQ', '1', "Keratometry Left Eye Sequence", '', 'KeratometryLeftEyeSequence'),
    0x00460074: ('SQ', '1', "Steep Keratometric Axis Sequence", '', 'SteepKeratometricAxisSequence'),
    0x00460075: ('FD', '1', "Radius of Curvature", '', 'RadiusOfCurvature'),
    0x00460076: ('FD', '1', "Keratometric Power", '', 'KeratometricPower'),
    0x00460077: ('FD', '1', "Keratometric Axis", '', 'KeratometricAxis'),
    0x00460080: ('SQ', '1', "Flat Keratometric Axis Sequence", '', 'FlatKeratometricAxisSequence'),
    0x00460092: ('CS', '1', "Background Color", '', 'BackgroundColor'),
    0x00460094: ('CS', '1', "Optotype", '', 'Optotype'),
    0x00460095: ('CS', '1', "Optotype Presentation", '', 'OptotypePresentation'),
    0x00460097: ('SQ', '1', "Subjective Refraction Right Eye Sequence", '', 'SubjectiveRefractionRightEyeSequence'),
    0x00460098: ('SQ', '1', "Subjective Refraction Left Eye Sequence", '', 'SubjectiveRefractionLeftEyeSequence'),
    0x00460100: ('SQ', '1', "Add Near Sequence", '', 'AddNearSequence'),
    0x00460101: ('SQ', '1', "Add Intermediate Sequence", '', 'AddIntermediateSequence'),
    0x00460102: ('SQ', '1', "Add Other Sequence", '', 'AddOtherSequence'),
    0x00460104: ('FD', '1', "Add Power", '', 'AddPower'),
    0x00460106: ('FD', '1', "Viewing Distance", '', 'ViewingDistance'),
    0x00460110: ('SQ', '1', "Cornea Measurements Sequence", '', 'CorneaMeasurementsSequence'),
    0x00460111: ('SQ', '1', "Source of Cornea Measurement Data Code Sequence", '', 'SourceOfCorneaMeasurementDataCodeSequence'),
    0x00460112: ('SQ', '1', "Steep Corneal Axis Sequence", '', 'SteepCornealAxisSequence'),
    0x00460113: ('SQ', '1', "Flat Corneal Axis Sequence", '', 'FlatCornealAxisSequence'),
    0x00460114: ('FD', '1', "Corneal Power", '', 'CornealPower'),
    0x00460115: ('FD', '1', "Corneal Axis", '', 'CornealAxis'),
    0x00460116: ('SQ', '1', "Cornea Measurement Method Code Sequence", '', 'CorneaMeasurementMethodCodeSequence'),
    0x00460117: ('FL', '1', "Refractive Index of Cornea", '', 'RefractiveIndexOfCornea'),
    0x00460118: ('FL', '1', "Refractive Index of Aqueous Humor", '', 'RefractiveIndexOfAqueousHumor'),
    0x00460121: ('SQ', '1', "Visual Acuity Type Code Sequence", '', 'VisualAcuityTypeCodeSequence'),
    0x00460122: ('SQ', '1', "Visual Acuity Right Eye Sequence", '', 'VisualAcuityRightEyeSequence'),
    0x00460123: ('SQ', '1', "Visual Acuity Left Eye Sequence", '', 'VisualAcuityLeftEyeSequence'),
    0x00460124: ('SQ', '1', "Visual Acuity Both Eyes Open Sequence", '', 'VisualAcuityBothEyesOpenSequence'),
    0x00460125: ('CS', '1', "Viewing Distance Type", '', 'ViewingDistanceType'),
    0x00460135: ('SS', '2', "Visual Acuity Modifiers", '', 'VisualAcuityModifiers'),
    0x00460137: ('FD', '1', "Decimal Visual Acuity", '', 'DecimalVisualAcuity'),
    0x00460139: ('LO', '1', "Optotype Detailed Definition", '', 'OptotypeDetailedDefinition'),
    0x00460145: ('SQ', '1', "Referenced Refractive Measurements Sequence", '', 'ReferencedRefractiveMeasurementsSequence'),
    0x00460146: ('FD', '1', "Sphere Power", '', 'SpherePower'),
    0x00460147: ('FD', '1', "Cylinder Power", '', 'CylinderPower'),
    0x00460201: ('CS', '1', "Corneal Topography Surface", '', 'CornealTopographySurface'),
    0x00460202: ('FL', '2', "Corneal Vertex Location", '', 'CornealVertexLocation'),
    0x00460203: ('FL', '1', "Pupil Centroid X-Coordinate", '', 'PupilCentroidXCoordinate'),
    0x00460204: ('FL', '1', "Pupil Centroid Y-Coordinate", '', 'PupilCentroidYCoordinate'),
    0x00460205: ('FL', '1', "Equivalent Pupil Radius", '', 'EquivalentPupilRadius'),
    0x00460207: ('SQ', '1', "Corneal Topography Map Type Code Sequence", '', 'CornealTopographyMapTypeCodeSequence'),
    0x00460208: ('IS', '2-2n', "Vertices of the Outline of Pupil", '', 'VerticesOfTheOutlineOfPupil'),
    0x00460210: ('SQ', '1', "Corneal Topography Mapping Normals Sequence", '', 'CornealTopographyMappingNormalsSequence'),
    0x00460211: ('SQ', '1', "Maximum Corneal Curvature Sequence", '', 'MaximumCornealCurvatureSequence'),
    0x00460212: ('FL', '1', "Maximum Corneal Curvature", '', 'MaximumCornealCurvature'),
    0x00460213: ('FL', '2', "Maximum Corneal Curvature Location", '', 'MaximumCornealCurvatureLocation'),
    0x00460215: ('SQ', '1', "Minimum Keratometric Sequence", '', 'MinimumKeratometricSequence'),
    0x00460218: ('SQ', '1', "Simulated Keratometric Cylinder Sequence", '', 'SimulatedKeratometricCylinderSequence'),
    0x00460220: ('FL', '1', "Average Corneal Power", '', 'AverageCornealPower'),
    0x00460224: ('FL', '1', "Corneal I-S Value", '', 'CornealISValue'),
    0x00460227: ('FL', '1', "Analyzed Area", '', 'AnalyzedArea'),
    0x00460230: ('FL', '1', "Surface Regularity Index", '', 'SurfaceRegularityIndex'),
    0x00460232: ('FL', '1', "Surface Asymmetry Index", '', 'SurfaceAsymmetryIndex'),
    0x00460234: ('FL', '1', "Corneal Eccentricity Index", '', 'CornealEccentricityIndex'),
    0x00460236: ('FL', '1', "Keratoconus Prediction Index", '', 'KeratoconusPredictionIndex'),
    0x00460238: ('FL', '1', "Decimal Potential Visual Acuity", '', 'DecimalPotentialVisualAcuity'),
    0x00460242: ('CS', '1', "Corneal Topography Map Quality Evaluation", '', 'CornealTopographyMapQualityEvaluation'),
    0x00460244: ('SQ', '1', "Source Image Corneal Processed Data Sequence", '', 'SourceImageCornealProcessedDataSequence'),
    0x00460247: ('FL', '3', "Corneal Point Location", '', 'CornealPointLocation'),
    0x00460248: ('CS', '1', "Corneal Point Estimated", '', 'CornealPointEstimated'),
    0x00460249: ('FL', '1', "Axial Power", '', 'AxialPower'),
    0x00460250: ('FL', '1', "Tangential Power", '', 'TangentialPower'),
    0x00460251: ('FL', '1', "Refractive Power", '', 'RefractivePower'),
    0x00460252: ('FL', '1', "Relative Elevation", '', 'RelativeElevation'),
    0x00460253: ('FL', '1', "Corneal Wavefront", '', 'CornealWavefront'),
    0x00480001: ('FL', '1', "Imaged Volume Width", '', 'ImagedVolumeWidth'),
    0x00480002: ('FL', '1', "Imaged Volume Height", '', 'ImagedVolumeHeight'),
    0x00480003: ('FL', '1', "Imaged Volume Depth", '', 'ImagedVolumeDepth'),
    0x00480006: ('UL', '1', "Total Pixel Matrix Columns", '', 'TotalPixelMatrixColumns'),
    0x00480007: ('UL', '1', "Total Pixel Matrix Rows", '', 'TotalPixelMatrixRows'),
    0x00480008: ('SQ', '1', "Total Pixel Matrix Origin Sequence", '', 'TotalPixelMatrixOriginSequence'),
    0x00480010: ('CS', '1', "Specimen Label in Image", '', 'SpecimenLabelInImage'),
    0x00480011: ('CS', '1', "Focus Method", '', 'FocusMethod'),
    0x00480012: ('CS', '1', "Extended Depth of Field", '', 'ExtendedDepthOfField'),
    0x00480013: ('US', '1', "Number of Focal Planes", '', 'NumberOfFocalPlanes'),
    0x00480014: ('FL', '1', "Distance Between Focal Planes", '', 'DistanceBetweenFocalPlanes'),
    0x00480015: ('US', '3', "Recommended Absent Pixel CIELab Value", '', 'RecommendedAbsentPixelCIELabValue'),
    0x00480100: ('SQ', '1', "Illuminator Type Code Sequence", '', 'IlluminatorTypeCodeSequence'),
    0x00480102: ('DS', '6', "Image Orientation (Slide)", '', 'ImageOrientationSlide'),
    0x00480105: ('SQ', '1', "Optical Path Sequence", '', 'OpticalPathSequence'),
    0x00480106: ('SH', '1', "Optical Path Identifier", '', 'OpticalPathIdentifier'),
    0x00480107: ('ST', '1', "Optical Path Description", '', 'OpticalPathDescription'),
    0x00480108: ('SQ', '1', "Illumination Color Code Sequence", '', 'IlluminationColorCodeSequence'),
    0x00480110: ('SQ', '1', "Specimen Reference Sequence", '', 'SpecimenReferenceSequence'),
    0x00480111: ('DS', '1', "Condenser Lens Power", '', 'CondenserLensPower'),
    0x00480112: ('DS', '1', "Objective Lens Power", '', 'ObjectiveLensPower'),
    0x00480113: ('DS', '1', "Objective Lens Numerical Aperture", '', 'ObjectiveLensNumericalAperture'),
    0x00480114: ('CS', '1', "Confocal Mode", '', 'ConfocalMode'),
    0x00480115: ('CS', '1', "Tissue Location", '', 'TissueLocation'),
    0x00480116: ('SQ', '1', "Confocal Microscopy Image Frame Type Sequence", '', 'ConfocalMicroscopyImageFrameTypeSequence'),
    0x00480117: ('FD', '1', "Image Acquisition Depth", '', 'ImageAcquisitionDepth'),
    0x00480120: ('SQ', '1', "Palette Color Lookup Table Sequence", '', 'PaletteColorLookupTableSequence'),
    0x00480200: ('SQ', '1', "Referenced Image Navigation Sequence", 'Retired', 'ReferencedImageNavigationSequence'),
    0x00480201: ('US', '2', "Top Left Hand Corner of Localizer Area", 'Retired', 'TopLeftHandCornerOfLocalizerArea'),
    0x00480202: ('US', '2', "Bottom Right Hand Corner of Localizer Area", 'Retired', 'BottomRightHandCornerOfLocalizerArea'),
    0x00480207: ('SQ', '1', "Optical Path Identification Sequence", '', 'OpticalPathIdentificationSequence'),
    0x0048021A: ('SQ', '1', "Plane Position (Slide) Sequence", '', 'PlanePositionSlideSequence'),
    0x0048021E: ('SL', '1', "Column Position In Total Image Pixel Matrix", '', 'ColumnPositionInTotalImagePixelMatrix'),
    0x0048021F: ('SL', '1', "Row Position In Total Image Pixel Matrix", '', 'RowPositionInTotalImagePixelMatrix'),
    0x00480301: ('CS', '1', "Pixel Origin Interpretation", '', 'PixelOriginInterpretation'),
    0x00480302: ('UL', '1', "Number of Optical Paths", '', 'NumberOfOpticalPaths'),
    0x00480303: ('UL', '1', "Total Pixel Matrix Focal Planes", '', 'TotalPixelMatrixFocalPlanes'),
    0x00500004: ('CS', '1', "Calibration Image", '', 'CalibrationImage'),
    0x00500010: ('SQ', '1', "Device Sequence", '', 'DeviceSequence'),
    0x00500012: ('SQ', '1', "Container Component Type Code Sequence", '', 'ContainerComponentTypeCodeSequence'),
    0x00500013: ('FD', '1', "Container Component Thickness", '', 'ContainerComponentThickness'),
    0x00500014: ('DS', '1', "Device Length", '', 'DeviceLength'),
    0x00500015: ('FD', '1', "Container Component Width", '', 'ContainerComponentWidth'),
    0x00500016: ('DS', '1', "Device Diameter", '', 'DeviceDiameter'),
    0x00500017: ('CS', '1', "Device Diameter Units", '', 'DeviceDiameterUnits'),
    0x00500018: ('DS', '1', "Device Volume", '', 'DeviceVolume'),
    0x00500019: ('DS', '1', "Inter-Marker Distance", '', 'InterMarkerDistance'),
    0x0050001A: ('CS', '1', "Container Component Material", '', 'ContainerComponentMaterial'),
    0x0050001B: ('LO', '1', "Container Component ID", '', 'ContainerComponentID'),
    0x0050001C: ('FD', '1', "Container Component Length", '', 'ContainerComponentLength'),
    0x0050001D: ('FD', '1', "Container Component Diameter", '', 'ContainerComponentDiameter'),
    0x0050001E: ('LO', '1', "Container Component Description", '', 'ContainerComponentDescription'),
    0x00500020: ('LO', '1', "Device Description", '', 'DeviceDescription'),
    0x00500021: ('ST', '1', "Long Device Description", '', 'LongDeviceDescription'),
    0x00520001: ('FL', '1', "Contrast/Bolus Ingredient Percent by Volume", '', 'ContrastBolusIngredientPercentByVolume'),
    0x00520002: ('FD', '1', "OCT Focal Distance", '', 'OCTFocalDistance'),
    0x00520003: ('FD', '1', "Beam Spot Size", '', 'BeamSpotSize'),
    0x00520004: ('FD', '1', "Effective Refractive Index", '', 'EffectiveRefractiveIndex'),
    0x00520006: ('CS', '1', "OCT Acquisition Domain", '', 'OCTAcquisitionDomain'),
    0x00520007: ('FD', '1', "OCT Optical Center Wavelength", '', 'OCTOpticalCenterWavelength'),
    0x00520008: ('FD', '1', "Axial Resolution", '', 'AxialResolution'),
    0x00520009: ('FD', '1', "Ranging Depth", '', 'RangingDepth'),
    0x00520011: ('FD', '1', "A-line Rate", '', 'ALineRate'),
    0x00520012: ('US', '1', "A-lines Per Frame", '', 'ALinesPerFrame'),
    0x00520013: ('FD', '1', "Catheter Rotational Rate", '', 'CatheterRotationalRate'),
    0x00520014: ('FD', '1', "A-line Pixel Spacing", '', 'ALinePixelSpacing'),
    0x00520016: ('SQ', '1', "Mode of Percutaneous Access Sequence", '', 'ModeOfPercutaneousAccessSequence'),
    0x00520025: ('SQ', '1', "Intravascular OCT Frame Type Sequence", '', 'IntravascularOCTFrameTypeSequence'),
    0x00520026: ('CS', '1', "OCT Z Offset Applied", '', 'OCTZOffsetApplied'),
    0x00520027: ('SQ', '1', "Intravascular Frame Content Sequence", '', 'IntravascularFrameContentSequence'),
    0x00520028: ('FD', '1', "Intravascular Longitudinal Distance", '', 'IntravascularLongitudinalDistance'),
    0x00520029: ('SQ', '1', "Intravascular OCT Frame Content Sequence", '', 'IntravascularOCTFrameContentSequence'),
    0x00520030: ('SS', '1', "OCT Z Offset Correction", '', 'OCTZOffsetCorrection'),
    0x00520031: ('CS', '1', "Catheter Direction of Rotation", '', 'CatheterDirectionOfRotation'),
    0x00520033: ('FD', '1', "Seam Line Location", '', 'SeamLineLocation'),
    0x00520034: ('FD', '1', "First A-line Location", '', 'FirstALineLocation'),
    0x00520036: ('US', '1', "Seam Line Index", '', 'SeamLineIndex'),
    0x00520038: ('US', '1', "Number of Padded A-lines", '', 'NumberOfPaddedALines'),
    0x00520039: ('CS', '1', "Interpolation Type", '', 'InterpolationType'),
    0x0052003A: ('CS', '1', "Refractive Index Applied", '', 'RefractiveIndexApplied'),
    0x00540010: ('US', '1-n', "Energy Window Vector", '', 'EnergyWindowVector'),
    0x00540011: ('US', '1', "Number of Energy Windows", '', 'NumberOfEnergyWindows'),
    0x00540012: ('SQ', '1', "Energy Window Information Sequence", '', 'EnergyWindowInformationSequence'),
    0x00540013: ('SQ', '1', "Energy Window Range Sequence", '', 'EnergyWindowRangeSequence'),
    0x00540014: ('DS', '1', "Energy Window Lower Limit", '', 'EnergyWindowLowerLimit'),
    0x00540015: ('DS', '1', "Energy Window Upper Limit", '', 'EnergyWindowUpperLimit'),
    0x00540016: ('SQ', '1', "Radiopharmaceutical Information Sequence", '', 'RadiopharmaceuticalInformationSequence'),
    0x00540017: ('IS', '1', "Residual Syringe Counts", '', 'ResidualSyringeCounts'),
    0x00540018: ('SH', '1', "Energy Window Name", '', 'EnergyWindowName'),
    0x00540020: ('US', '1-n', "Detector Vector", '', 'DetectorVector'),
    0x00540021: ('US', '1', "Number of Detectors", '', 'NumberOfDetectors'),
    0x00540022: ('SQ', '1', "Detector Information Sequence", '', 'DetectorInformationSequence'),
    0x00540030: ('US', '1-n', "Phase Vector", '', 'PhaseVector'),
    0x00540031: ('US', '1', "Number of Phases", '', 'NumberOfPhases'),
    0x00540032: ('SQ', '1', "Phase Information Sequence", '', 'PhaseInformationSequence'),
    0x00540033: ('US', '1', "Number of Frames in Phase", '', 'NumberOfFramesInPhase'),
    0x00540036: ('IS', '1', "Phase Delay", '', 'PhaseDelay'),
    0x00540038: ('IS', '1', "Pause Between Frames", '', 'PauseBetweenFrames'),
    0x00540039: ('CS', '1', "Phase Description", '', 'PhaseDescription'),
    0x00540050: ('US', '1-n', "Rotation Vector", '', 'RotationVector'),
    0x00540051: ('US', '1', "Number of Rotations", '', 'NumberOfRotations'),
    0x00540052: ('SQ', '1', "Rotation Information Sequence", '', 'RotationInformationSequence'),
    0x00540053: ('US', '1', "Number of Frames in Rotation", '', 'NumberOfFramesInRotation'),
    0x00540060: ('US', '1-n', "R-R Interval Vector", '', 'RRIntervalVector'),
    0x00540061: ('US', '1', "Number of R-R Intervals", '', 'NumberOfRRIntervals'),
    0x00540062: ('SQ', '1', "Gated Information Sequence", '', 'GatedInformationSequence'),
    0x00540063: ('SQ', '1', "Data Information Sequence", '', 'DataInformationSequence'),
    0x00540070: ('US', '1-n', "Time Slot Vector", '', 'TimeSlotVector'),
    0x00540071: ('US', '1', "Number of Time Slots", '', 'NumberOfTimeSlots'),
    0x00540072: ('SQ', '1', "Time Slot Information Sequence", '', 'TimeSlotInformationSequence'),
    0x00540073: ('DS', '1', "Time Slot Time", '', 'TimeSlotTime'),
    0x00540080: ('US', '1-n', "Slice Vector", '', 'SliceVector'),
    0x00540081: ('US', '1', "Number of Slices", '', 'NumberOfSlices'),
    0x00540090: ('US', '1-n', "Angular View Vector", '', 'AngularViewVector'),
    0x00540100: ('US', '1-n', "Time Slice Vector", '', 'TimeSliceVector'),
    0x00540101: ('US', '1', "Number of Time Slices", '', 'NumberOfTimeSlices'),
    0x00540200: ('DS', '1', "Start Angle", '', 'StartAngle'),
    0x00540202: ('CS', '1', "Type of Detector Motion", '', 'TypeOfDetectorMotion'),
    0x00540210: ('IS', '1-n', "Trigger Vector", '', 'TriggerVector'),
    0x00540211: ('US', '1', "Number of Triggers in Phase", '', 'NumberOfTriggersInPhase'),
    0x00540220: ('SQ', '1', "View Code Sequence", '', 'ViewCodeSequence'),
    0x00540222: ('SQ', '1', "View Modifier Code Sequence", '', 'ViewModifierCodeSequence'),
    0x00540300: ('SQ', '1', "Radionuclide Code Sequence", '', 'RadionuclideCodeSequence'),
    0x00540302: ('SQ', '1', "Administration Route Code Sequence", '', 'AdministrationRouteCodeSequence'),
    0x00540304: ('SQ', '1', "Radiopharmaceutical Code Sequence", '', 'RadiopharmaceuticalCodeSequence'),
    0x00540306: ('SQ', '1', "Calibration Data Sequence", '', 'CalibrationDataSequence'),
    0x00540308: ('US', '1', "Energy Window Number", '', 'EnergyWindowNumber'),
    0x00540400: ('SH', '1', "Image ID", '', 'ImageID'),
    0x00540410: ('SQ', '1', "Patient Orientation Code Sequence", '', 'PatientOrientationCodeSequence'),
    0x00540412: ('SQ', '1', "Patient Orientation Modifier Code Sequence", '', 'PatientOrientationModifierCodeSequence'),
    0x00540414: ('SQ', '1', "Patient Gantry Relationship Code Sequence", '', 'PatientGantryRelationshipCodeSequence'),
    0x00540500: ('CS', '1', "Slice Progression Direction", '', 'SliceProgressionDirection'),
    0x00540501: ('CS', '1', "Scan Progression Direction", '', 'ScanProgressionDirection'),
    0x00541000: ('CS', '2', "Series Type", '', 'SeriesType'),
    0x00541001: ('CS', '1', "Units", '', 'Units'),
    0x00541002: ('CS', '1', "Counts Source", '', 'CountsSource'),
    0x00541004: ('CS', '1', "Reprojection Method", '', 'ReprojectionMethod'),
    0x00541006: ('CS', '1', "SUV Type", '', 'SUVType'),
    0x00541100: ('CS', '1', "Randoms Correction Method", '', 'RandomsCorrectionMethod'),
    0x00541101: ('LO', '1', "Attenuation Correction Method", '', 'AttenuationCorrectionMethod'),
    0x00541102: ('CS', '1', "Decay Correction", '', 'DecayCorrection'),
    0x00541103: ('LO', '1', "Reconstruction Method", '', 'ReconstructionMethod'),
    0x00541104: ('LO', '1', "Detector Lines of Response Used", '', 'DetectorLinesOfResponseUsed'),
    0x00541105: ('LO', '1', "Scatter Correction Method", '', 'ScatterCorrectionMethod'),
    0x00541200: ('DS', '1', "Axial Acceptance", '', 'AxialAcceptance'),
    0x00541201: ('IS', '2', "Axial Mash", '', 'AxialMash'),
    0x00541202: ('IS', '1', "Transverse Mash", '', 'TransverseMash'),
    0x00541203: ('DS', '2', "Detector Element Size", '', 'DetectorElementSize'),
    0x00541210: ('DS', '1', "Coincidence Window Width", '', 'CoincidenceWindowWidth'),
    0x00541220: ('CS', '1-n', "Secondary Counts Type", '', 'SecondaryCountsType'),
    0x00541300: ('DS', '1', "Frame Reference Time", '', 'FrameReferenceTime'),
    0x00541310: ('IS', '1', "Primary (Prompts) Counts Accumulated", '', 'PrimaryPromptsCountsAccumulated'),
    0x00541311: ('IS', '1-n', "Secondary Counts Accumulated", '', 'SecondaryCountsAccumulated'),
    0x00541320: ('DS', '1', "Slice Sensitivity Factor", '', 'SliceSensitivityFactor'),
    0x00541321: ('DS', '1', "Decay Factor", '', 'DecayFactor'),
    0x00541322: ('DS', '1', "Dose Calibration Factor", '', 'DoseCalibrationFactor'),
    0x00541323: ('DS', '1', "Scatter Fraction Factor", '', 'ScatterFractionFactor'),
    0x00541324: ('DS', '1', "Dead Time Factor", '', 'DeadTimeFactor'),
    0x00541330: ('US', '1', "Image Index", '', 'ImageIndex'),
    0x00541400: ('CS', '1-n', "Counts Included", 'Retired', 'CountsIncluded'),
    0x00541401: ('CS', '1', "Dead Time Correction Flag", 'Retired', 'DeadTimeCorrectionFlag'),
    0x00603000: ('SQ', '1', "Histogram Sequence", '', 'HistogramSequence'),
    0x00603002: ('US', '1', "Histogram Number of Bins", '', 'HistogramNumberOfBins'),
    0x00603004: ('US or SS', '1', "Histogram First Bin Value", '', 'HistogramFirstBinValue'),
    0x00603006: ('US or SS', '1', "Histogram Last Bin Value", '', 'HistogramLastBinValue'),
    0x00603008: ('US', '1', "Histogram Bin Width", '', 'HistogramBinWidth'),
    0x00603010: ('LO', '1', "Histogram Explanation", '', 'HistogramExplanation'),
    0x00603020: ('UL', '1-n', "Histogram Data", '', 'HistogramData'),
    0x00620001: ('CS', '1', "Segmentation Type", '', 'SegmentationType'),
    0x00620002: ('SQ', '1', "Segment Sequence", '', 'SegmentSequence'),
    0x00620003: ('SQ', '1', "Segmented Property Category Code Sequence", '', 'SegmentedPropertyCategoryCodeSequence'),
    0x00620004: ('US', '1', "Segment Number", '', 'SegmentNumber'),
    0x00620005: ('LO', '1', "Segment Label", '', 'SegmentLabel'),
    0x00620006: ('ST', '1', "Segment Description", '', 'SegmentDescription'),
    0x00620007: ('SQ', '1', "Segmentation Algorithm Identification Sequence", '', 'SegmentationAlgorithmIdentificationSequence'),
    0x00620008: ('CS', '1', "Segment Algorithm Type", '', 'SegmentAlgorithmType'),
    0x00620009: ('LO', '1-n', "Segment Algorithm Name", '', 'SegmentAlgorithmName'),
    0x0062000A: ('SQ', '1', "Segment Identification Sequence", '', 'SegmentIdentificationSequence'),
    0x0062000B: ('US', '1-n', "Referenced Segment Number", '', 'ReferencedSegmentNumber'),
    0x0062000C: ('US', '1', "Recommended Display Grayscale Value", '', 'RecommendedDisplayGrayscaleValue'),
    0x0062000D: ('US', '3', "Recommended Display CIELab Value", '', 'RecommendedDisplayCIELabValue'),
    0x0062000E: ('US', '1', "Maximum Fractional Value", '', 'MaximumFractionalValue'),
    0x0062000F: ('SQ', '1', "Segmented Property Type Code Sequence", '', 'SegmentedPropertyTypeCodeSequence'),
    0x00620010: ('CS', '1', "Segmentation Fractional Type", '', 'SegmentationFractionalType'),
    0x00620011: ('SQ', '1', "Segmented Property Type Modifier Code Sequence", '', 'SegmentedPropertyTypeModifierCodeSequence'),
    0x00620012: ('SQ', '1', "Used Segments Sequence", '', 'UsedSegmentsSequence'),
    0x00620013: ('CS', '1', "Segments Overlap", '', 'SegmentsOverlap'),
    0x00620020: ('UT', '1', "Tracking ID", '', 'TrackingID'),
    0x00620021: ('UI', '1', "Tracking UID", '', 'TrackingUID'),
    0x00640002: ('SQ', '1', "Deformable Registration Sequence", '', 'DeformableRegistrationSequence'),
    0x00640003: ('UI', '1', "Source Frame of Reference UID", '', 'SourceFrameOfReferenceUID'),
    0x00640005: ('SQ', '1', "Deformable Registration Grid Sequence", '', 'DeformableRegistrationGridSequence'),
    0x00640007: ('UL', '3', "Grid Dimensions", '', 'GridDimensions'),
    0x00640008: ('FD', '3', "Grid Resolution", '', 'GridResolution'),
    0x00640009: ('OF', '1', "Vector Grid Data", '', 'VectorGridData'),
    0x0064000F: ('SQ', '1', "Pre Deformation Matrix Registration Sequence", '', 'PreDeformationMatrixRegistrationSequence'),
    0x00640010: ('SQ', '1', "Post Deformation Matrix Registration Sequence", '', 'PostDeformationMatrixRegistrationSequence'),
    0x00660001: ('UL', '1', "Number of Surfaces", '', 'NumberOfSurfaces'),
    0x00660002: ('SQ', '1', "Surface Sequence", '', 'SurfaceSequence'),
    0x00660003: ('UL', '1', "Surface Number", '', 'SurfaceNumber'),
    0x00660004: ('LT', '1', "Surface Comments", '', 'SurfaceComments'),
    0x00660005: ('FL', '1', "Surface Offset", '', 'SurfaceOffset'),
    0x00660009: ('CS', '1', "Surface Processing", '', 'SurfaceProcessing'),
    0x0066000A: ('FL', '1', "Surface Processing Ratio", '', 'SurfaceProcessingRatio'),
    0x0066000B: ('LO', '1', "Surface Processing Description", '', 'SurfaceProcessingDescription'),
    0x0066000C: ('FL', '1', "Recommended Presentation Opacity", '', 'RecommendedPresentationOpacity'),
    0x0066000D: ('CS', '1', "Recommended Presentation Type", '', 'RecommendedPresentationType'),
    0x0066000E: ('CS', '1', "Finite Volume", '', 'FiniteVolume'),
    0x00660010: ('CS', '1', "Manifold", '', 'Manifold'),
    0x00660011: ('SQ', '1', "Surface Points Sequence", '', 'SurfacePointsSequence'),
    0x00660012: ('SQ', '1', "Surface Points Normals Sequence", '', 'SurfacePointsNormalsSequence'),
    0x00660013: ('SQ', '1', "Surface Mesh Primitives Sequence", '', 'SurfaceMeshPrimitivesSequence'),
    0x00660015: ('UL', '1', "Number of Surface Points", '', 'NumberOfSurfacePoints'),
    0x00660016: ('OF', '1', "Point Coordinates Data", '', 'PointCoordinatesData'),
    0x00660017: ('FL', '3', "Point Position Accuracy", '', 'PointPositionAccuracy'),
    0x00660018: ('FL', '1', "Mean Point Distance", '', 'MeanPointDistance'),
    0x00660019: ('FL', '1', "Maximum Point Distance", '', 'MaximumPointDistance'),
    0x0066001A: ('FL', '6', "Points Bounding Box Coordinates", '', 'PointsBoundingBoxCoordinates'),
    0x0066001B: ('FL', '3', "Axis of Rotation", '', 'AxisOfRotation'),
    0x0066001C: ('FL', '3', "Center of Rotation", '', 'CenterOfRotation'),
    0x0066001E: ('UL', '1', "Number of Vectors", '', 'NumberOfVectors'),
    0x0066001F: ('US', '1', "Vector Dimensionality", '', 'VectorDimensionality'),
    0x00660020: ('FL', '1-n', "Vector Accuracy", '', 'VectorAccuracy'),
    0x00660021: ('OF', '1', "Vector Coordinate Data", '', 'VectorCoordinateData'),
    0x00660022: ('OD', '1', "Double Point Coordinates Data", '', 'DoublePointCoordinatesData'),
    0x00660023: ('OW', '1', "Triangle Point Index List", 'Retired', 'TrianglePointIndexList'),
    0x00660024: ('OW', '1', "Edge Point Index List", 'Retired', 'EdgePointIndexList'),
    0x00660025: ('OW', '1', "Vertex Point Index List", 'Retired', 'VertexPointIndexList'),
    0x00660026: ('SQ', '1', "Triangle Strip Sequence", '', 'TriangleStripSequence'),
    0x00660027: ('SQ', '1', "Triangle Fan Sequence", '', 'TriangleFanSequence'),
    0x00660028: ('SQ', '1', "Line Sequence", '', 'LineSequence'),
    0x00660029: ('OW', '1', "Primitive Point Index List", 'Retired', 'PrimitivePointIndexList'),
    0x0066002A: ('UL', '1', "Surface Count", '', 'SurfaceCount'),
    0x0066002B: ('SQ', '1', "Referenced Surface Sequence", '', 'ReferencedSurfaceSequence'),
    0x0066002C: ('UL', '1', "Referenced Surface Number", '', 'ReferencedSurfaceNumber'),
    0x0066002D: ('SQ', '1', "Segment Surface Generation Algorithm Identification Sequence", '', 'SegmentSurfaceGenerationAlgorithmIdentificationSequence'),
    0x0066002E: ('SQ', '1', "Segment Surface Source Instance Sequence", '', 'SegmentSurfaceSourceInstanceSequence'),
    0x0066002F: ('SQ', '1', "Algorithm Family Code Sequence", '', 'AlgorithmFamilyCodeSequence'),
    0x00660030: ('SQ', '1', "Algorithm Name Code Sequence", '', 'AlgorithmNameCodeSequence'),
    0x00660031: ('LO', '1', "Algorithm Version", '', 'AlgorithmVersion'),
    0x00660032: ('LT', '1', "Algorithm Parameters", '', 'AlgorithmParameters'),
    0x00660034: ('SQ', '1', "Facet Sequence", '', 'FacetSequence'),
    0x00660035: ('SQ', '1', "Surface Processing Algorithm Identification Sequence", '', 'SurfaceProcessingAlgorithmIdentificationSequence'),
    0x00660036: ('LO', '1', "Algorithm Name", '', 'AlgorithmName'),
    0x00660037: ('FL', '1', "Recommended Point Radius", '', 'RecommendedPointRadius'),
    0x00660038: ('FL', '1', "Recommended Line Thickness", '', 'RecommendedLineThickness'),
    0x00660040: ('OL', '1', "Long Primitive Point Index List", '', 'LongPrimitivePointIndexList'),
    0x00660041: ('OL', '1', "Long Triangle Point Index List", '', 'LongTrianglePointIndexList'),
    0x00660042: ('OL', '1', "Long Edge Point Index List", '', 'LongEdgePointIndexList'),
    0x00660043: ('OL', '1', "Long Vertex Point Index List", '', 'LongVertexPointIndexList'),
    0x00660101: ('SQ', '1', "Track Set Sequence", '', 'TrackSetSequence'),
    0x00660102: ('SQ', '1', "Track Sequence", '', 'TrackSequence'),
    0x00660103: ('OW', '1', "Recommended Display CIELab Value List", '', 'RecommendedDisplayCIELabValueList'),
    0x00660104: ('SQ', '1', "Tracking Algorithm Identification Sequence", '', 'TrackingAlgorithmIdentificationSequence'),
    0x00660105: ('UL', '1', "Track Set Number", '', 'TrackSetNumber'),
    0x00660106: ('LO', '1', "Track Set Label", '', 'TrackSetLabel'),
    0x00660107: ('UT', '1', "Track Set Description", '', 'TrackSetDescription'),
    0x00660108: ('SQ', '1', "Track Set Anatomical Type Code Sequence", '', 'TrackSetAnatomicalTypeCodeSequence'),
    0x00660121: ('SQ', '1', "Measurements Sequence", '', 'MeasurementsSequence'),
    0x00660124: ('SQ', '1', "Track Set Statistics Sequence", '', 'TrackSetStatisticsSequence'),
    0x00660125: ('OF', '1', "Floating Point Values", '', 'FloatingPointValues'),
    0x00660129: ('OL', '1', "Track Point Index List", '', 'TrackPointIndexList'),
    0x00660130: ('SQ', '1', "Track Statistics Sequence", '', 'TrackStatisticsSequence'),
    0x00660132: ('SQ', '1', "Measurement Values Sequence", '', 'MeasurementValuesSequence'),
    0x00660133: ('SQ', '1', "Diffusion Acquisition Code Sequence", '', 'DiffusionAcquisitionCodeSequence'),
    0x00660134: ('SQ', '1', "Diffusion Model Code Sequence", '', 'DiffusionModelCodeSequence'),
    0x00686210: ('LO', '1', "Implant Size", '', 'ImplantSize'),
    0x00686221: ('LO', '1', "Implant Template Version", '', 'ImplantTemplateVersion'),
    0x00686222: ('SQ', '1', "Replaced Implant Template Sequence", '', 'ReplacedImplantTemplateSequence'),
    0x00686223: ('CS', '1', "Implant Type", '', 'ImplantType'),
    0x00686224: ('SQ', '1', "Derivation Implant Template Sequence", '', 'DerivationImplantTemplateSequence'),
    0x00686225: ('SQ', '1', "Original Implant Template Sequence", '', 'OriginalImplantTemplateSequence'),
    0x00686226: ('DT', '1', "Effective DateTime", '', 'EffectiveDateTime'),
    0x00686230: ('SQ', '1', "Implant Target Anatomy Sequence", '', 'ImplantTargetAnatomySequence'),
    0x00686260: ('SQ', '1', "Information From Manufacturer Sequence", '', 'InformationFromManufacturerSequence'),
    0x00686265: ('SQ', '1', "Notification From Manufacturer Sequence", '', 'NotificationFromManufacturerSequence'),
    0x00686270: ('DT', '1', "Information Issue DateTime", '', 'InformationIssueDateTime'),
    0x00686280: ('ST', '1', "Information Summary", '', 'InformationSummary'),
    0x006862A0: ('SQ', '1', "Implant Regulatory Disapproval Code Sequence", '', 'ImplantRegulatoryDisapprovalCodeSequence'),
    0x006862A5: ('FD', '1', "Overall Template Spatial Tolerance", '', 'OverallTemplateSpatialTolerance'),
    0x006862C0: ('SQ', '1', "HPGL Document Sequence", '', 'HPGLDocumentSequence'),
    0x006862D0: ('US', '1', "HPGL Document ID", '', 'HPGLDocumentID'),
    0x006862D5: ('LO', '1', "HPGL Document Label", '', 'HPGLDocumentLabel'),
    0x006862E0: ('SQ', '1', "View Orientation Code Sequence", '', 'ViewOrientationCodeSequence'),
    0x006862F0: ('SQ', '1', "View Orientation Modifier Code Sequence", '', 'ViewOrientationModifierCodeSequence'),
    0x006862F2: ('FD', '1', "HPGL Document Scaling", '', 'HPGLDocumentScaling'),
    0x00686300: ('OB', '1', "HPGL Document", '', 'HPGLDocument'),
    0x00686310: ('US', '1', "HPGL Contour Pen Number", '', 'HPGLContourPenNumber'),
    0x00686320: ('SQ', '1', "HPGL Pen Sequence", '', 'HPGLPenSequence'),
    0x00686330: ('US', '1', "HPGL Pen Number", '', 'HPGLPenNumber'),
    0x00686340: ('LO', '1', "HPGL Pen Label", '', 'HPGLPenLabel'),
    0x00686345: ('ST', '1', "HPGL Pen Description", '', 'HPGLPenDescription'),
    0x00686346: ('FD', '2', "Recommended Rotation Point", '', 'RecommendedRotationPoint'),
    0x00686347: ('FD', '4', "Bounding Rectangle", '', 'BoundingRectangle'),
    0x00686350: ('US', '1-n', "Implant Template 3D Model Surface Number", '', 'ImplantTemplate3DModelSurfaceNumber'),
    0x00686360: ('SQ', '1', "Surface Model Description Sequence", '', 'SurfaceModelDescriptionSequence'),
    0x00686380: ('LO', '1', "Surface Model Label", '', 'SurfaceModelLabel'),
    0x00686390: ('FD', '1', "Surface Model Scaling Factor", '', 'SurfaceModelScalingFactor'),
    0x006863A0: ('SQ', '1', "Materials Code Sequence", '', 'MaterialsCodeSequence'),
    0x006863A4: ('SQ', '1', "Coating Materials Code Sequence", '', 'CoatingMaterialsCodeSequence'),
    0x006863A8: ('SQ', '1', "Implant Type Code Sequence", '', 'ImplantTypeCodeSequence'),
    0x006863AC: ('SQ', '1', "Fixation Method Code Sequence", '', 'FixationMethodCodeSequence'),
    0x006863B0: ('SQ', '1', "Mating Feature Sets Sequence", '', 'MatingFeatureSetsSequence'),
    0x006863C0: ('US', '1', "Mating Feature Set ID", '', 'MatingFeatureSetID'),
    0x006863D0: ('LO', '1', "Mating Feature Set Label", '', 'MatingFeatureSetLabel'),
    0x006863E0: ('SQ', '1', "Mating Feature Sequence", '', 'MatingFeatureSequence'),
    0x006863F0: ('US', '1', "Mating Feature ID", '', 'MatingFeatureID'),
    0x00686400: ('SQ', '1', "Mating Feature Degree of Freedom Sequence", '', 'MatingFeatureDegreeOfFreedomSequence'),
    0x00686410: ('US', '1', "Degree of Freedom ID", '', 'DegreeOfFreedomID'),
    0x00686420: ('CS', '1', "Degree of Freedom Type", '', 'DegreeOfFreedomType'),
    0x00686430: ('SQ', '1', "2D Mating Feature Coordinates Sequence", '', 'TwoDMatingFeatureCoordinatesSequence'),
    0x00686440: ('US', '1', "Referenced HPGL Document ID", '', 'ReferencedHPGLDocumentID'),
    0x00686450: ('FD', '2', "2D Mating Point", '', 'TwoDMatingPoint'),
    0x00686460: ('FD', '4', "2D Mating Axes", '', 'TwoDMatingAxes'),
    0x00686470: ('SQ', '1', "2D Degree of Freedom Sequence", '', 'TwoDDegreeOfFreedomSequence'),
    0x00686490: ('FD', '3', "3D Degree of Freedom Axis", '', 'ThreeDDegreeOfFreedomAxis'),
    0x006864A0: ('FD', '2', "Range of Freedom", '', 'RangeOfFreedom'),
    0x006864C0: ('FD', '3', "3D Mating Point", '', 'ThreeDMatingPoint'),
    0x006864D0: ('FD', '9', "3D Mating Axes", '', 'ThreeDMatingAxes'),
    0x006864F0: ('FD', '3', "2D Degree of Freedom Axis", '', 'TwoDDegreeOfFreedomAxis'),
    0x00686500: ('SQ', '1', "Planning Landmark Point Sequence", '', 'PlanningLandmarkPointSequence'),
    0x00686510: ('SQ', '1', "Planning Landmark Line Sequence", '', 'PlanningLandmarkLineSequence'),
    0x00686520: ('SQ', '1', "Planning Landmark Plane Sequence", '', 'PlanningLandmarkPlaneSequence'),
    0x00686530: ('US', '1', "Planning Landmark ID", '', 'PlanningLandmarkID'),
    0x00686540: ('LO', '1', "Planning Landmark Description", '', 'PlanningLandmarkDescription'),
    0x00686545: ('SQ', '1', "Planning Landmark Identification Code Sequence", '', 'PlanningLandmarkIdentificationCodeSequence'),
    0x00686550: ('SQ', '1', "2D Point Coordinates Sequence", '', 'TwoDPointCoordinatesSequence'),
    0x00686560: ('FD', '2', "2D Point Coordinates", '', 'TwoDPointCoordinates'),
    0x00686590: ('FD', '3', "3D Point Coordinates", '', 'ThreeDPointCoordinates'),
    0x006865A0: ('SQ', '1', "2D Line Coordinates Sequence", '', 'TwoDLineCoordinatesSequence'),
    0x006865B0: ('FD', '4', "2D Line Coordinates", '', 'TwoDLineCoordinates'),
    0x006865D0: ('FD', '6', "3D Line Coordinates", '', 'ThreeDLineCoordinates'),
    0x006865E0: ('SQ', '1', "2D Plane Coordinates Sequence", '', 'TwoDPlaneCoordinatesSequence'),
    0x006865F0: ('FD', '4', "2D Plane Intersection", '', 'TwoDPlaneIntersection'),
    0x00686610: ('FD', '3', "3D Plane Origin", '', 'ThreeDPlaneOrigin'),
    0x00686620: ('FD', '3', "3D Plane Normal", '', 'ThreeDPlaneNormal'),
    0x00687001: ('CS', '1', "Model Modification", '', 'ModelModification'),
    0x00687002: ('CS', '1', "Model Mirroring", '', 'ModelMirroring'),
    0x00687003: ('SQ', '1', "Model Usage Code Sequence", '', 'ModelUsageCodeSequence'),
    0x00687004: ('UI', '1', "Model Group UID", '', 'ModelGroupUID'),
    0x00687005: ('UR', '1', "Relative URI Reference Within Encapsulated Document", '', 'RelativeURIReferenceWithinEncapsulatedDocument'),
    0x006A0001: ('CS', '1', "Annotation Coordinate Type", '', 'AnnotationCoordinateType'),
    0x006A0002: ('SQ', '1', "Annotation Group Sequence", '', 'AnnotationGroupSequence'),
    0x006A0003: ('UI', '1', "Annotation Group UID", '', 'AnnotationGroupUID'),
    0x006A0005: ('LO', '1', "Annotation Group Label", '', 'AnnotationGroupLabel'),
    0x006A0006: ('UT', '1', "Annotation Group Description", '', 'AnnotationGroupDescription'),
    0x006A0007: ('CS', '1', "Annotation Group Generation Type", '', 'AnnotationGroupGenerationType'),
    0x006A0008: ('SQ', '1', "Annotation Group Algorithm Identification Sequence", '', 'AnnotationGroupAlgorithmIdentificationSequence'),
    0x006A0009: ('SQ', '1', "Annotation Property Category Code Sequence", '', 'AnnotationPropertyCategoryCodeSequence'),
    0x006A000A: ('SQ', '1', "Annotation Property Type Code Sequence", '', 'AnnotationPropertyTypeCodeSequence'),
    0x006A000B: ('SQ', '1', "Annotation Property Type Modifier Code Sequence", '', 'AnnotationPropertyTypeModifierCodeSequence'),
    0x006A000C: ('UL', '1', "Number of Annotations", '', 'NumberOfAnnotations'),
    0x006A000D: ('CS', '1', "Annotation Applies to All Optical Paths", '', 'AnnotationAppliesToAllOpticalPaths'),
    0x006A000E: ('SH', '1-n', "Referenced Optical Path Identifier", '', 'ReferencedOpticalPathIdentifier'),
    0x006A000F: ('CS', '1', "Annotation Applies to All Z Planes", '', 'AnnotationAppliesToAllZPlanes'),
    0x006A0010: ('FD', '1-n', "Common Z Coordinate Value", '', 'CommonZCoordinateValue'),
    0x006A0011: ('OL', '1', "Annotation Index List", '', 'AnnotationIndexList'),
    0x00700001: ('SQ', '1', "Graphic Annotation Sequence", '', 'GraphicAnnotationSequence'),
    0x00700002: ('CS', '1', "Graphic Layer", '', 'GraphicLayer'),
    0x00700003: ('CS', '1', "Bounding Box Annotation Units", '', 'BoundingBoxAnnotationUnits'),
    0x00700004: ('CS', '1', "Anchor Point Annotation Units", '', 'AnchorPointAnnotationUnits'),
    0x00700005: ('CS', '1', "Graphic Annotation Units", '', 'GraphicAnnotationUnits'),
    0x00700006: ('ST', '1', "Unformatted Text Value", '', 'UnformattedTextValue'),
    0x00700008: ('SQ', '1', "Text Object Sequence", '', 'TextObjectSequence'),
    0x00700009: ('SQ', '1', "Graphic Object Sequence", '', 'GraphicObjectSequence'),
    0x00700010: ('FL', '2', "Bounding Box Top Left Hand Corner", '', 'BoundingBoxTopLeftHandCorner'),
    0x00700011: ('FL', '2', "Bounding Box Bottom Right Hand Corner", '', 'BoundingBoxBottomRightHandCorner'),
    0x00700012: ('CS', '1', "Bounding Box Text Horizontal Justification", '', 'BoundingBoxTextHorizontalJustification'),
    0x00700014: ('FL', '2', "Anchor Point", '', 'AnchorPoint'),
    0x00700015: ('CS', '1', "Anchor Point Visibility", '', 'AnchorPointVisibility'),
    0x00700020: ('US', '1', "Graphic Dimensions", '', 'GraphicDimensions'),
    0x00700021: ('US', '1', "Number of Graphic Points", '', 'NumberOfGraphicPoints'),
    0x00700022: ('FL', '2-n', "Graphic Data", '', 'GraphicData'),
    0x00700023: ('CS', '1', "Graphic Type", '', 'GraphicType'),
    0x00700024: ('CS', '1', "Graphic Filled", '', 'GraphicFilled'),
    0x00700040: ('IS', '1', "Image Rotation (Retired)", 'Retired', 'ImageRotationRetired'),
    0x00700041: ('CS', '1', "Image Horizontal Flip", '', 'ImageHorizontalFlip'),
    0x00700042: ('US', '1', "Image Rotation", '', 'ImageRotation'),
    0x00700050: ('US', '2', "Displayed Area Top Left Hand Corner (Trial)", 'Retired', 'DisplayedAreaTopLeftHandCornerTrial'),
    0x00700051: ('US', '2', "Displayed Area Bottom Right Hand Corner (Trial)", 'Retired', 'DisplayedAreaBottomRightHandCornerTrial'),
    0x00700052: ('SL', '2', "Displayed Area Top Left Hand Corner", '', 'DisplayedAreaTopLeftHandCorner'),
    0x00700053: ('SL', '2', "Displayed Area Bottom Right Hand Corner", '', 'DisplayedAreaBottomRightHandCorner'),
    0x0070005A: ('SQ', '1', "Displayed Area Selection Sequence", '', 'DisplayedAreaSelectionSequence'),
    0x00700060: ('SQ', '1', "Graphic Layer Sequence", '', 'GraphicLayerSequence'),
    0x00700062: ('IS', '1', "Graphic Layer Order", '', 'GraphicLayerOrder'),
    0x00700066: ('US', '1', "Graphic Layer Recommended Display Grayscale Value", '', 'GraphicLayerRecommendedDisplayGrayscaleValue'),
    0x00700067: ('US', '3', "Graphic Layer Recommended Display RGB Value", 'Retired', 'GraphicLayerRecommendedDisplayRGBValue'),
    0x00700068: ('LO', '1', "Graphic Layer Description", '', 'GraphicLayerDescription'),
    0x00700080: ('CS', '1', "Content Label", '', 'ContentLabel'),
    0x00700081: ('LO', '1', "Content Description", '', 'ContentDescription'),
    0x00700082: ('DA', '1', "Presentation Creation Date", '', 'PresentationCreationDate'),
    0x00700083: ('TM', '1', "Presentation Creation Time", '', 'PresentationCreationTime'),
    0x00700084: ('PN', '1', "Content Creator's Name", '', 'ContentCreatorName'),
    0x00700086: ('SQ', '1', "Content Creator's Identification Code Sequence", '', 'ContentCreatorIdentificationCodeSequence'),
    0x00700087: ('SQ', '1', "Alternate Content Description Sequence", '', 'AlternateContentDescriptionSequence'),
    0x00700100: ('CS', '1', "Presentation Size Mode", '', 'PresentationSizeMode'),
    0x00700101: ('DS', '2', "Presentation Pixel Spacing", '', 'PresentationPixelSpacing'),
    0x00700102: ('IS', '2', "Presentation Pixel Aspect Ratio", '', 'PresentationPixelAspectRatio'),
    0x00700103: ('FL', '1', "Presentation Pixel Magnification Ratio", '', 'PresentationPixelMagnificationRatio'),
    0x00700207: ('LO', '1', "Graphic Group Label", '', 'GraphicGroupLabel'),
    0x00700208: ('ST', '1', "Graphic Group Description", '', 'GraphicGroupDescription'),
    0x00700209: ('SQ', '1', "Compound Graphic Sequence", '', 'CompoundGraphicSequence'),
    0x00700226: ('UL', '1', "Compound Graphic Instance ID", '', 'CompoundGraphicInstanceID'),
    0x00700227: ('LO', '1', "Font Name", '', 'FontName'),
    0x00700228: ('CS', '1', "Font Name Type", '', 'FontNameType'),
    0x00700229: ('LO', '1', "CSS Font Name", '', 'CSSFontName'),
    0x00700230: ('FD', '1', "Rotation Angle", '', 'RotationAngle'),
    0x00700231: ('SQ', '1', "Text Style Sequence", '', 'TextStyleSequence'),
    0x00700232: ('SQ', '1', "Line Style Sequence", '', 'LineStyleSequence'),
    0x00700233: ('SQ', '1', "Fill Style Sequence", '', 'FillStyleSequence'),
    0x00700234: ('SQ', '1', "Graphic Group Sequence", '', 'GraphicGroupSequence'),
    0x00700241: ('US', '3', "Text Color CIELab Value", '', 'TextColorCIELabValue'),
    0x00700242: ('CS', '1', "Horizontal Alignment", '', 'HorizontalAlignment'),
    0x00700243: ('CS', '1', "Vertical Alignment", '', 'VerticalAlignment'),
    0x00700244: ('CS', '1', "Shadow Style", '', 'ShadowStyle'),
    0x00700245: ('FL', '1', "Shadow Offset X", '', 'ShadowOffsetX'),
    0x00700246: ('FL', '1', "Shadow Offset Y", '', 'ShadowOffsetY'),
    0x00700247: ('US', '3', "Shadow Color CIELab Value", '', 'ShadowColorCIELabValue'),
    0x00700248: ('CS', '1', "Underlined", '', 'Underlined'),
    0x00700249: ('CS', '1', "Bold", '', 'Bold'),
    0x00700250: ('CS', '1', "Italic", '', 'Italic'),
    0x00700251: ('US', '3', "Pattern On Color CIELab Value", '', 'PatternOnColorCIELabValue'),
    0x00700252: ('US', '3', "Pattern Off Color CIELab Value", '', 'PatternOffColorCIELabValue'),
    0x00700253: ('FL', '1', "Line Thickness", '', 'LineThickness'),
    0x00700254: ('CS', '1', "Line Dashing Style", '', 'LineDashingStyle'),
    0x00700255: ('UL', '1', "Line Pattern", '', 'LinePattern'),
    0x00700256: ('OB', '1', "Fill Pattern", '', 'FillPattern'),
    0x00700257: ('CS', '1', "Fill Mode", '', 'FillMode'),
    0x00700258: ('FL', '1', "Shadow Opacity", '', 'ShadowOpacity'),
    0x00700261: ('FL', '1', "Gap Length", '', 'GapLength'),
    0x00700262: ('FL', '1', "Diameter of Visibility", '', 'DiameterOfVisibility'),
    0x00700273: ('FL', '2', "Rotation Point", '', 'RotationPoint'),
    0x00700274: ('CS', '1', "Tick Alignment", '', 'TickAlignment'),
    0x00700278: ('CS', '1', "Show Tick Label", '', 'ShowTickLabel'),
    0x00700279: ('CS', '1', "Tick Label Alignment", '', 'TickLabelAlignment'),
    0x00700282: ('CS', '1', "Compound Graphic Units", '', 'CompoundGraphicUnits'),
    0x00700284: ('FL', '1', "Pattern On Opacity", '', 'PatternOnOpacity'),
    0x00700285: ('FL', '1', "Pattern Off Opacity", '', 'PatternOffOpacity'),
    0x00700287: ('SQ', '1', "Major Ticks Sequence", '', 'MajorTicksSequence'),
    0x00700288: ('FL', '1', "Tick Position", '', 'TickPosition'),
    0x00700289: ('SH', '1', "Tick Label", '', 'TickLabel'),
    0x00700294: ('CS', '1', "Compound Graphic Type", '', 'CompoundGraphicType'),
    0x00700295: ('UL', '1', "Graphic Group ID", '', 'GraphicGroupID'),
    0x00700306: ('CS', '1', "Shape Type", '', 'ShapeType'),
    0x00700308: ('SQ', '1', "Registration Sequence", '', 'RegistrationSequence'),
    0x00700309: ('SQ', '1', "Matrix Registration Sequence", '', 'MatrixRegistrationSequence'),
    0x0070030A: ('SQ', '1', "Matrix Sequence", '', 'MatrixSequence'),
    0x0070030B: ('FD', '16', "Frame of Reference to Displayed Coordinate System Transformation Matrix", '', 'FrameOfReferenceToDisplayedCoordinateSystemTransformationMatrix'),
    0x0070030C: ('CS', '1', "Frame of Reference Transformation Matrix Type", '', 'FrameOfReferenceTransformationMatrixType'),
    0x0070030D: ('SQ', '1', "Registration Type Code Sequence", '', 'RegistrationTypeCodeSequence'),
    0x0070030F: ('ST', '1', "Fiducial Description", '', 'FiducialDescription'),
    0x00700310: ('SH', '1', "Fiducial Identifier", '', 'FiducialIdentifier'),
    0x00700311: ('SQ', '1', "Fiducial Identifier Code Sequence", '', 'FiducialIdentifierCodeSequence'),
    0x00700312: ('FD', '1', "Contour Uncertainty Radius", '', 'ContourUncertaintyRadius'),
    0x00700314: ('SQ', '1', "Used Fiducials Sequence", '', 'UsedFiducialsSequence'),
    0x00700315: ('SQ', '1', "Used RT Structure Set ROI Sequence", '', 'UsedRTStructureSetROISequence'),
    0x00700318: ('SQ', '1', "Graphic Coordinates Data Sequence", '', 'GraphicCoordinatesDataSequence'),
    0x0070031A: ('UI', '1', "Fiducial UID", '', 'FiducialUID'),
    0x0070031B: ('UI', '1', "Referenced Fiducial UID", '', 'ReferencedFiducialUID'),
    0x0070031C: ('SQ', '1', "Fiducial Set Sequence", '', 'FiducialSetSequence'),
    0x0070031E: ('SQ', '1', "Fiducial Sequence", '', 'FiducialSequence'),
    0x0070031F: ('SQ', '1', "Fiducials Property Category Code Sequence", '', 'FiducialsPropertyCategoryCodeSequence'),
    0x00700401: ('US', '3', "Graphic Layer Recommended Display CIELab Value", '', 'GraphicLayerRecommendedDisplayCIELabValue'),
    0x00700402: ('SQ', '1', "Blending Sequence", '', 'BlendingSequence'),
    0x00700403: ('FL', '1', "Relative Opacity", '', 'RelativeOpacity'),
    0x00700404: ('SQ', '1', "Referenced Spatial Registration Sequence", '', 'ReferencedSpatialRegistrationSequence'),
    0x00700405: ('CS', '1', "Blending Position", '', 'BlendingPosition'),
    0x00701101: ('UI', '1', "Presentation Display Collection UID", '', 'PresentationDisplayCollectionUID'),
    0x00701102: ('UI', '1', "Presentation Sequence Collection UID", '', 'PresentationSequenceCollectionUID'),
    0x00701103: ('US', '1', "Presentation Sequence Position Index", '', 'PresentationSequencePositionIndex'),
    0x00701104: ('SQ', '1', "Rendered Image Reference Sequence", '', 'RenderedImageReferenceSequence'),
    0x00701201: ('SQ', '1', "Volumetric Presentation State Input Sequence", '', 'VolumetricPresentationStateInputSequence'),
    0x00701202: ('CS', '1', "Presentation Input Type", '', 'PresentationInputType'),
    0x00701203: ('US', '1', "Input Sequence Position Index", '', 'InputSequencePositionIndex'),
    0x00701204: ('CS', '1', "Crop", '', 'Crop'),
    0x00701205: ('US', '1-n', "Cropping Specification Index", '', 'CroppingSpecificationIndex'),
    0x00701206: ('CS', '1', "Compositing Method", 'Retired', 'CompositingMethod'),
    0x00701207: ('US', '1', "Volumetric Presentation Input Number", '', 'VolumetricPresentationInputNumber'),
    0x00701208: ('CS', '1', "Image Volume Geometry", '', 'ImageVolumeGeometry'),
    0x00701209: ('UI', '1', "Volumetric Presentation Input Set UID", '', 'VolumetricPresentationInputSetUID'),
    0x0070120A: ('SQ', '1', "Volumetric Presentation Input Set Sequence", '', 'VolumetricPresentationInputSetSequence'),
    0x0070120B: ('CS', '1', "Global Crop", '', 'GlobalCrop'),
    0x0070120C: ('US', '1-n', "Global Cropping Specification Index", '', 'GlobalCroppingSpecificationIndex'),
    0x0070120D: ('CS', '1', "Rendering Method", '', 'RenderingMethod'),
    0x00701301: ('SQ', '1', "Volume Cropping Sequence", '', 'VolumeCroppingSequence'),
    0x00701302: ('CS', '1', "Volume Cropping Method", '', 'VolumeCroppingMethod'),
    0x00701303: ('FD', '6', "Bounding Box Crop", '', 'BoundingBoxCrop'),
    0x00701304: ('SQ', '1', "Oblique Cropping Plane Sequence", '', 'ObliqueCroppingPlaneSequence'),
    0x00701305: ('FD', '4', "Plane", '', 'Plane'),
    0x00701306: ('FD', '3', "Plane Normal", '', 'PlaneNormal'),
    0x00701309: ('US', '1', "Cropping Specification Number", '', 'CroppingSpecificationNumber'),
    0x00701501: ('CS', '1', "Multi-Planar Reconstruction Style", '', 'MultiPlanarReconstructionStyle'),
    0x00701502: ('CS', '1', "MPR Thickness Type", '', 'MPRThicknessType'),
    0x00701503: ('FD', '1', "MPR Slab Thickness", '', 'MPRSlabThickness'),
    0x00701505: ('FD', '3', "MPR Top Left Hand Corner", '', 'MPRTopLeftHandCorner'),
    0x00701507: ('FD', '3', "MPR View Width Direction", '', 'MPRViewWidthDirection'),
    0x00701508: ('FD', '1', "MPR View Width", '', 'MPRViewWidth'),
    0x0070150C: ('UL', '1', "Number of Volumetric Curve Points", '', 'NumberOfVolumetricCurvePoints'),
    0x0070150D: ('OD', '1', "Volumetric Curve Points", '', 'VolumetricCurvePoints'),
    0x00701511: ('FD', '3', "MPR View Height Direction", '', 'MPRViewHeightDirection'),
    0x00701512: ('FD', '1', "MPR View Height", '', 'MPRViewHeight'),
    0x00701602: ('CS', '1', "Render Projection", '', 'RenderProjection'),
    0x00701603: ('FD', '3', "Viewpoint Position", '', 'ViewpointPosition'),
    0x00701604: ('FD', '3', "Viewpoint LookAt Point", '', 'ViewpointLookAtPoint'),
    0x00701605: ('FD', '3', "Viewpoint Up Direction", '', 'ViewpointUpDirection'),
    0x00701606: ('FD', '6', "Render Field of View", '', 'RenderFieldOfView'),
    0x00701607: ('FD', '1', "Sampling Step Size", '', 'SamplingStepSize'),
    0x00701701: ('CS', '1', "Shading Style", '', 'ShadingStyle'),
    0x00701702: ('FD', '1', "Ambient Reflection Intensity", '', 'AmbientReflectionIntensity'),
    0x00701703: ('FD', '3', "Light Direction", '', 'LightDirection'),
    0x00701704: ('FD', '1', "Diffuse Reflection Intensity", '', 'DiffuseReflectionIntensity'),
    0x00701705: ('FD', '1', "Specular Reflection Intensity", '', 'SpecularReflectionIntensity'),
    0x00701706: ('FD', '1', "Shininess", '', 'Shininess'),
    0x00701801: ('SQ', '1', "Presentation State Classification Component Sequence", '', 'PresentationStateClassificationComponentSequence'),
    0x00701802: ('CS', '1', "Component Type", '', 'ComponentType'),
    0x00701803: ('SQ', '1', "Component Input Sequence", '', 'ComponentInputSequence'),
    0x00701804: ('US', '1', "Volumetric Presentation Input Index", '', 'VolumetricPresentationInputIndex'),
    0x00701805: ('SQ', '1', "Presentation State Compositor Component Sequence", '', 'PresentationStateCompositorComponentSequence'),
    0x00701806: ('SQ', '1', "Weighting Transfer Function Sequence", '', 'WeightingTransferFunctionSequence'),
    0x00701807: ('US', '3', "Weighting Lookup Table Descriptor", 'Retired', 'WeightingLookupTableDescriptor'),
    0x00701808: ('OB', '1', "Weighting Lookup Table Data", 'Retired', 'WeightingLookupTableData'),
    0x00701901: ('SQ', '1', "Volumetric Annotation Sequence", '', 'VolumetricAnnotationSequence'),
    0x00701903: ('SQ', '1', "Referenced Structured Context Sequence", '', 'ReferencedStructuredContextSequence'),
    0x00701904: ('UI', '1', "Referenced Content Item", '', 'ReferencedContentItem'),
    0x00701905: ('SQ', '1', "Volumetric Presentation Input Annotation Sequence", '', 'VolumetricPresentationInputAnnotationSequence'),
    0x00701907: ('CS', '1', "Annotation Clipping", '', 'AnnotationClipping'),
    0x00701A01: ('CS', '1', "Presentation Animation Style", '', 'PresentationAnimationStyle'),
    0x00701A03: ('FD', '1', "Recommended Animation Rate", '', 'RecommendedAnimationRate'),
    0x00701A04: ('SQ', '1', "Animation Curve Sequence", '', 'AnimationCurveSequence'),
    0x00701A05: ('FD', '1', "Animation Step Size", '', 'AnimationStepSize'),
    0x00701A06: ('FD', '1', "Swivel Range", '', 'SwivelRange'),
    0x00701A07: ('OD', '1', "Volumetric Curve Up Directions", '', 'VolumetricCurveUpDirections'),
    0x00701A08: ('SQ', '1', "Volume Stream Sequence", '', 'VolumeStreamSequence'),
    0x00701A09: ('LO', '1', "RGBA Transfer Function Description", '', 'RGBATransferFunctionDescription'),
    0x00701B01: ('SQ', '1', "Advanced Blending Sequence", '', 'AdvancedBlendingSequence'),
    0x00701B02: ('US', '1', "Blending Input Number", '', 'BlendingInputNumber'),
    0x00701B03: ('SQ', '1', "Blending Display Input Sequence", '', 'BlendingDisplayInputSequence'),
    0x00701B04: ('SQ', '1', "Blending Display Sequence", '', 'BlendingDisplaySequence'),
    0x00701B06: ('CS', '1', "Blending Mode", '', 'BlendingMode'),
    0x00701B07: ('CS', '1', "Time Series Blending", '', 'TimeSeriesBlending'),
    0x00701B08: ('CS', '1', "Geometry for Display", '', 'GeometryForDisplay'),
    0x00701B11: ('SQ', '1', "Threshold Sequence", '', 'ThresholdSequence'),
    0x00701B12: ('SQ', '1', "Threshold Value Sequence", '', 'ThresholdValueSequence'),
    0x00701B13: ('CS', '1', "Threshold Type", '', 'ThresholdType'),
    0x00701B14: ('FD', '1', "Threshold Value", '', 'ThresholdValue'),
    0x00720002: ('SH', '1', "Hanging Protocol Name", '', 'HangingProtocolName'),
    0x00720004: ('LO', '1', "Hanging Protocol Description", '', 'HangingProtocolDescription'),
    0x00720006: ('CS', '1', "Hanging Protocol Level", '', 'HangingProtocolLevel'),
    0x00720008: ('LO', '1', "Hanging Protocol Creator", '', 'HangingProtocolCreator'),
    0x0072000A: ('DT', '1', "Hanging Protocol Creation DateTime", '', 'HangingProtocolCreationDateTime'),
    0x0072000C: ('SQ', '1', "Hanging Protocol Definition Sequence", '', 'HangingProtocolDefinitionSequence'),
    0x0072000E: ('SQ', '1', "Hanging Protocol User Identification Code Sequence", '', 'HangingProtocolUserIdentificationCodeSequence'),
    0x00720010: ('LO', '1', "Hanging Protocol User Group Name", '', 'HangingProtocolUserGroupName'),
    0x00720012: ('SQ', '1', "Source Hanging Protocol Sequence", '', 'SourceHangingProtocolSequence'),
    0x00720014: ('US', '1', "Number of Priors Referenced", '', 'NumberOfPriorsReferenced'),
    0x00720020: ('SQ', '1', "Image Sets Sequence", '', 'ImageSetsSequence'),
    0x00720022: ('SQ', '1', "Image Set Selector Sequence", '', 'ImageSetSelectorSequence'),
    0x00720024: ('CS', '1', "Image Set Selector Usage Flag", '', 'ImageSetSelectorUsageFlag'),
    0x00720026: ('AT', '1', "Selector Attribute", '', 'SelectorAttribute'),
    0x00720028: ('US', '1', "Selector Value Number", '', 'SelectorValueNumber'),
    0x00720030: ('SQ', '1', "Time Based Image Sets Sequence", '', 'TimeBasedImageSetsSequence'),
    0x00720032: ('US', '1', "Image Set Number", '', 'ImageSetNumber'),
    0x00720034: ('CS', '1', "Image Set Selector Category", '', 'ImageSetSelectorCategory'),
    0x00720038: ('US', '2', "Relative Time", '', 'RelativeTime'),
    0x0072003A: ('CS', '1', "Relative Time Units", '', 'RelativeTimeUnits'),
    0x0072003C: ('SS', '2', "Abstract Prior Value", '', 'AbstractPriorValue'),
    0x0072003E: ('SQ', '1', "Abstract Prior Code Sequence", '', 'AbstractPriorCodeSequence'),
    0x00720040: ('LO', '1', "Image Set Label", '', 'ImageSetLabel'),
    0x00720050: ('CS', '1', "Selector Attribute VR", '', 'SelectorAttributeVR'),
    0x00720052: ('AT', '1-n', "Selector Sequence Pointer", '', 'SelectorSequencePointer'),
    0x00720054: ('LO', '1-n', "Selector Sequence Pointer Private Creator", '', 'SelectorSequencePointerPrivateCreator'),
    0x00720056: ('LO', '1', "Selector Attribute Private Creator", '', 'SelectorAttributePrivateCreator'),
    0x0072005E: ('AE', '1-n', "Selector AE Value", '', 'SelectorAEValue'),
    0x0072005F: ('AS', '1-n', "Selector AS Value", '', 'SelectorASValue'),
    0x00720060: ('AT', '1-n', "Selector AT Value", '', 'SelectorATValue'),
    0x00720061: ('DA', '1-n', "Selector DA Value", '', 'SelectorDAValue'),
    0x00720062: ('CS', '1-n', "Selector CS Value", '', 'SelectorCSValue'),
    0x00720063: ('DT', '1-n', "Selector DT Value", '', 'SelectorDTValue'),
    0x00720064: ('IS', '1-n', "Selector IS Value", '', 'SelectorISValue'),
    0x00720065: ('OB', '1', "Selector OB Value", '', 'SelectorOBValue'),
    0x00720066: ('LO', '1-n', "Selector LO Value", '', 'SelectorLOValue'),
    0x00720067: ('OF', '1', "Selector OF Value", '', 'SelectorOFValue'),
    0x00720068: ('LT', '1', "Selector LT Value", '', 'SelectorLTValue'),
    0x00720069: ('OW', '1', "Selector OW Value", '', 'SelectorOWValue'),
    0x0072006A: ('PN', '1-n', "Selector PN Value", '', 'SelectorPNValue'),
    0x0072006B: ('TM', '1-n', "Selector TM Value", '', 'SelectorTMValue'),
    0x0072006C: ('SH', '1-n', "Selector SH Value", '', 'SelectorSHValue'),
    0x0072006D: ('UN', '1', "Selector UN Value", '', 'SelectorUNValue'),
    0x0072006E: ('ST', '1', "Selector ST Value", '', 'SelectorSTValue'),
    0x0072006F: ('UC', '1-n', "Selector UC Value", '', 'SelectorUCValue'),
    0x00720070: ('UT', '1', "Selector UT Value", '', 'SelectorUTValue'),
    0x00720071: ('UR', '1', "Selector UR Value", '', 'SelectorURValue'),
    0x00720072: ('DS', '1-n', "Selector DS Value", '', 'SelectorDSValue'),
    0x00720073: ('OD', '1', "Selector OD Value", '', 'SelectorODValue'),
    0x00720074: ('FD', '1-n', "Selector FD Value", '', 'SelectorFDValue'),
    0x00720075: ('OL', '1', "Selector OL Value", '', 'SelectorOLValue'),
    0x00720076: ('FL', '1-n', "Selector FL Value", '', 'SelectorFLValue'),
    0x00720078: ('UL', '1-n', "Selector UL Value", '', 'SelectorULValue'),
    0x0072007A: ('US', '1-n', "Selector US Value", '', 'SelectorUSValue'),
    0x0072007C: ('SL', '1-n', "Selector SL Value", '', 'SelectorSLValue'),
    0x0072007E: ('SS', '1-n', "Selector SS Value", '', 'SelectorSSValue'),
    0x0072007F: ('UI', '1-n', "Selector UI Value", '', 'SelectorUIValue'),
    0x00720080: ('SQ', '1', "Selector Code Sequence Value", '', 'SelectorCodeSequenceValue'),
    0x00720081: ('OV', '1', "Selector OV Value", '', 'SelectorOVValue'),
    0x00720082: ('SV', '1-n', "Selector SV Value", '', 'SelectorSVValue'),
    0x00720083: ('UV', '1-n', "Selector UV Value", '', 'SelectorUVValue'),
    0x00720100: ('US', '1', "Number of Screens", '', 'NumberOfScreens'),
    0x00720102: ('SQ', '1', "Nominal Screen Definition Sequence", '', 'NominalScreenDefinitionSequence'),
    0x00720104: ('US', '1', "Number of Vertical Pixels", '', 'NumberOfVerticalPixels'),
    0x00720106: ('US', '1', "Number of Horizontal Pixels", '', 'NumberOfHorizontalPixels'),
    0x00720108: ('FD', '4', "Display Environment Spatial Position", '', 'DisplayEnvironmentSpatialPosition'),
    0x0072010A: ('US', '1', "Screen Minimum Grayscale Bit Depth", '', 'ScreenMinimumGrayscaleBitDepth'),
    0x0072010C: ('US', '1', "Screen Minimum Color Bit Depth", '', 'ScreenMinimumColorBitDepth'),
    0x0072010E: ('US', '1', "Application Maximum Repaint Time", '', 'ApplicationMaximumRepaintTime'),
    0x00720200: ('SQ', '1', "Display Sets Sequence", '', 'DisplaySetsSequence'),
    0x00720202: ('US', '1', "Display Set Number", '', 'DisplaySetNumber'),
    0x00720203: ('LO', '1', "Display Set Label", '', 'DisplaySetLabel'),
    0x00720204: ('US', '1', "Display Set Presentation Group", '', 'DisplaySetPresentationGroup'),
    0x00720206: ('LO', '1', "Display Set Presentation Group Description", '', 'DisplaySetPresentationGroupDescription'),
    0x00720208: ('CS', '1', "Partial Data Display Handling", '', 'PartialDataDisplayHandling'),
    0x00720210: ('SQ', '1', "Synchronized Scrolling Sequence", '', 'SynchronizedScrollingSequence'),
    0x00720212: ('US', '2-n', "Display Set Scrolling Group", '', 'DisplaySetScrollingGroup'),
    0x00720214: ('SQ', '1', "Navigation Indicator Sequence", '', 'NavigationIndicatorSequence'),
    0x00720216: ('US', '1', "Navigation Display Set", '', 'NavigationDisplaySet'),
    0x00720218: ('US', '1-n', "Reference Display Sets", '', 'ReferenceDisplaySets'),
    0x00720300: ('SQ', '1', "Image Boxes Sequence", '', 'ImageBoxesSequence'),
    0x00720302: ('US', '1', "Image Box Number", '', 'ImageBoxNumber'),
    0x00720304: ('CS', '1', "Image Box Layout Type", '', 'ImageBoxLayoutType'),
    0x00720306: ('US', '1', "Image Box Tile Horizontal Dimension", '', 'ImageBoxTileHorizontalDimension'),
    0x00720308: ('US', '1', "Image Box Tile Vertical Dimension", '', 'ImageBoxTileVerticalDimension'),
    0x00720310: ('CS', '1', "Image Box Scroll Direction", '', 'ImageBoxScrollDirection'),
    0x00720312: ('CS', '1', "Image Box Small Scroll Type", '', 'ImageBoxSmallScrollType'),
    0x00720314: ('US', '1', "Image Box Small Scroll Amount", '', 'ImageBoxSmallScrollAmount'),
    0x00720316: ('CS', '1', "Image Box Large Scroll Type", '', 'ImageBoxLargeScrollType'),
    0x00720318: ('US', '1', "Image Box Large Scroll Amount", '', 'ImageBoxLargeScrollAmount'),
    0x00720320: ('US', '1', "Image Box Overlap Priority", '', 'ImageBoxOverlapPriority'),
    0x00720330: ('FD', '1', "Cine Relative to Real-Time", '', 'CineRelativeToRealTime'),
    0x00720400: ('SQ', '1', "Filter Operations Sequence", '', 'FilterOperationsSequence'),
    0x00720402: ('CS', '1', "Filter-by Category", '', 'FilterByCategory'),
    0x00720404: ('CS', '1', "Filter-by Attribute Presence", '', 'FilterByAttributePresence'),
    0x00720406: ('CS', '1', "Filter-by Operator", '', 'FilterByOperator'),
    0x00720420: ('US', '3', "Structured Display Background CIELab Value", '', 'StructuredDisplayBackgroundCIELabValue'),
    0x00720421: ('US', '3', "Empty Image Box CIELab Value", '', 'EmptyImageBoxCIELabValue'),
    0x00720422: ('SQ', '1', "Structured Display Image Box Sequence", '', 'StructuredDisplayImageBoxSequence'),
    0x00720424: ('SQ', '1', "Structured Display Text Box Sequence", '', 'StructuredDisplayTextBoxSequence'),
    0x00720427: ('SQ', '1', "Referenced First Frame Sequence", '', 'ReferencedFirstFrameSequence'),
    0x00720430: ('SQ', '1', "Image Box Synchronization Sequence", '', 'ImageBoxSynchronizationSequence'),
    0x00720432: ('US', '2-n', "Synchronized Image Box List", '', 'SynchronizedImageBoxList'),
    0x00720434: ('CS', '1', "Type of Synchronization", '', 'TypeOfSynchronization'),
    0x00720500: ('CS', '1', "Blending Operation Type", '', 'BlendingOperationType'),
    0x00720510: ('CS', '1', "Reformatting Operation Type", '', 'ReformattingOperationType'),
    0x00720512: ('FD', '1', "Reformatting Thickness", '', 'ReformattingThickness'),
    0x00720514: ('FD', '1', "Reformatting Interval", '', 'ReformattingInterval'),
    0x00720516: ('CS', '1', "Reformatting Operation Initial View Direction", '', 'ReformattingOperationInitialViewDirection'),
    0x00720520: ('CS', '1-n', "3D Rendering Type", '', 'ThreeDRenderingType'),
    0x00720600: ('SQ', '1', "Sorting Operations Sequence", '', 'SortingOperationsSequence'),
    0x00720602: ('CS', '1', "Sort-by Category", '', 'SortByCategory'),
    0x00720604: ('CS', '1', "Sorting Direction", '', 'SortingDirection'),
    0x00720700: ('CS', '2', "Display Set Patient Orientation", '', 'DisplaySetPatientOrientation'),
    0x00720702: ('CS', '1', "VOI Type", '', 'VOIType'),
    0x00720704: ('CS', '1', "Pseudo-Color Type", '', 'PseudoColorType'),
    0x00720705: ('SQ', '1', "Pseudo-Color Palette Instance Reference Sequence", '', 'PseudoColorPaletteInstanceReferenceSequence'),
    0x00720706: ('CS', '1', "Show Grayscale Inverted", '', 'ShowGrayscaleInverted'),
    0x00720710: ('CS', '1', "Show Image True Size Flag", '', 'ShowImageTrueSizeFlag'),
    0x00720712: ('CS', '1', "Show Graphic Annotation Flag", '', 'ShowGraphicAnnotationFlag'),
    0x00720714: ('CS', '1', "Show Patient Demographics Flag", '', 'ShowPatientDemographicsFlag'),
    0x00720716: ('CS', '1', "Show Acquisition Techniques Flag", '', 'ShowAcquisitionTechniquesFlag'),
    0x00720717: ('CS', '1', "Display Set Horizontal Justification", '', 'DisplaySetHorizontalJustification'),
    0x00720718: ('CS', '1', "Display Set Vertical Justification", '', 'DisplaySetVerticalJustification'),
    0x00740120: ('FD', '1', "Continuation Start Meterset", '', 'ContinuationStartMeterset'),
    0x00740121: ('FD', '1', "Continuation End Meterset", '', 'ContinuationEndMeterset'),
    0x00741000: ('CS', '1', "Procedure Step State", '', 'ProcedureStepState'),
    0x00741002: ('SQ', '1', "Procedure Step Progress Information Sequence", '', 'ProcedureStepProgressInformationSequence'),
    0x00741004: ('DS', '1', "Procedure Step Progress", '', 'ProcedureStepProgress'),
    0x00741006: ('ST', '1', "Procedure Step Progress Description", '', 'ProcedureStepProgressDescription'),
    0x00741007: ('SQ', '1', "Procedure Step Progress Parameters Sequence", '', 'ProcedureStepProgressParametersSequence'),
    0x00741008: ('SQ', '1', "Procedure Step Communications URI Sequence", '', 'ProcedureStepCommunicationsURISequence'),
    0x0074100A: ('UR', '1', "Contact URI", '', 'ContactURI'),
    0x0074100C: ('LO', '1', "Contact Display Name", '', 'ContactDisplayName'),
    0x0074100E: ('SQ', '1', "Procedure Step Discontinuation Reason Code Sequence", '', 'ProcedureStepDiscontinuationReasonCodeSequence'),
    0x00741020: ('SQ', '1', "Beam Task Sequence", '', 'BeamTaskSequence'),
    0x00741022: ('CS', '1', "Beam Task Type", '', 'BeamTaskType'),
    0x00741024: ('IS', '1', "Beam Order Index (Trial)", 'Retired', 'BeamOrderIndexTrial'),
    0x00741025: ('CS', '1', "Autosequence Flag", '', 'AutosequenceFlag'),
    0x00741026: ('FD', '1', "Table Top Vertical Adjusted Position", '', 'TableTopVerticalAdjustedPosition'),
    0x00741027: ('FD', '1', "Table Top Longitudinal Adjusted Position", '', 'TableTopLongitudinalAdjustedPosition'),
    0x00741028: ('FD', '1', "Table Top Lateral Adjusted Position", '', 'TableTopLateralAdjustedPosition'),
    0x0074102A: ('FD', '1', "Patient Support Adjusted Angle", '', 'PatientSupportAdjustedAngle'),
    0x0074102B: ('FD', '1', "Table Top Eccentric Adjusted Angle", '', 'TableTopEccentricAdjustedAngle'),
    0x0074102C: ('FD', '1', "Table Top Pitch Adjusted Angle", '', 'TableTopPitchAdjustedAngle'),
    0x0074102D: ('FD', '1', "Table Top Roll Adjusted Angle", '', 'TableTopRollAdjustedAngle'),
    0x00741030: ('SQ', '1', "Delivery Verification Image Sequence", '', 'DeliveryVerificationImageSequence'),
    0x00741032: ('CS', '1', "Verification Image Timing", '', 'VerificationImageTiming'),
    0x00741034: ('CS', '1', "Double Exposure Flag", '', 'DoubleExposureFlag'),
    0x00741036: ('CS', '1', "Double Exposure Ordering", '', 'DoubleExposureOrdering'),
    0x00741038: ('DS', '1', "Double Exposure Meterset (Trial)", 'Retired', 'DoubleExposureMetersetTrial'),
    0x0074103A: ('DS', '4', "Double Exposure Field Delta (Trial)", 'Retired', 'DoubleExposureFieldDeltaTrial'),
    0x00741040: ('SQ', '1', "Related Reference RT Image Sequence", '', 'RelatedReferenceRTImageSequence'),
    0x00741042: ('SQ', '1', "General Machine Verification Sequence", '', 'GeneralMachineVerificationSequence'),
    0x00741044: ('SQ', '1', "Conventional Machine Verification Sequence", '', 'ConventionalMachineVerificationSequence'),
    0x00741046: ('SQ', '1', "Ion Machine Verification Sequence", '', 'IonMachineVerificationSequence'),
    0x00741048: ('SQ', '1', "Failed Attributes Sequence", '', 'FailedAttributesSequence'),
    0x0074104A: ('SQ', '1', "Overridden Attributes Sequence", '', 'OverriddenAttributesSequence'),
    0x0074104C: ('SQ', '1', "Conventional Control Point Verification Sequence", '', 'ConventionalControlPointVerificationSequence'),
    0x0074104E: ('SQ', '1', "Ion Control Point Verification Sequence", '', 'IonControlPointVerificationSequence'),
    0x00741050: ('SQ', '1', "Attribute Occurrence Sequence", '', 'AttributeOccurrenceSequence'),
    0x00741052: ('AT', '1', "Attribute Occurrence Pointer", '', 'AttributeOccurrencePointer'),
    0x00741054: ('UL', '1', "Attribute Item Selector", '', 'AttributeItemSelector'),
    0x00741056: ('LO', '1', "Attribute Occurrence Private Creator", '', 'AttributeOccurrencePrivateCreator'),
    0x00741057: ('IS', '1-n', "Selector Sequence Pointer Items", '', 'SelectorSequencePointerItems'),
    0x00741200: ('CS', '1', "Scheduled Procedure Step Priority", '', 'ScheduledProcedureStepPriority'),
    0x00741202: ('LO', '1', "Worklist Label", '', 'WorklistLabel'),
    0x00741204: ('LO', '1', "Procedure Step Label", '', 'ProcedureStepLabel'),
    0x00741210: ('SQ', '1', "Scheduled Processing Parameters Sequence", '', 'ScheduledProcessingParametersSequence'),
    0x00741212: ('SQ', '1', "Performed Processing Parameters Sequence", '', 'PerformedProcessingParametersSequence'),
    0x00741216: ('SQ', '1', "Unified Procedure Step Performed Procedure Sequence", '', 'UnifiedProcedureStepPerformedProcedureSequence'),
    0x00741220: ('SQ', '1', "Related Procedure Step Sequence", 'Retired', 'RelatedProcedureStepSequence'),
    0x00741222: ('LO', '1', "Procedure Step Relationship Type", 'Retired', 'ProcedureStepRelationshipType'),
    0x00741224: ('SQ', '1', "Replaced Procedure Step Sequence", '', 'ReplacedProcedureStepSequence'),
    0x00741230: ('LO', '1', "Deletion Lock", '', 'DeletionLock'),
    0x00741234: ('AE', '1', "Receiving AE", '', 'ReceivingAE'),
    0x00741236: ('AE', '1', "Requesting AE", '', 'RequestingAE'),
    0x00741238: ('LT', '1', "Reason for Cancellation", '', 'ReasonForCancellation'),
    0x00741242: ('CS', '1', "SCP Status", '', 'SCPStatus'),
    0x00741244: ('CS', '1', "Subscription List Status", '', 'SubscriptionListStatus'),
    0x00741246: ('CS', '1', "Unified Procedure Step List Status", '', 'UnifiedProcedureStepListStatus'),
    0x00741324: ('UL', '1', "Beam Order Index", '', 'BeamOrderIndex'),
    0x00741338: ('FD', '1', "Double Exposure Meterset", '', 'DoubleExposureMeterset'),
    0x0074133A: ('FD', '4', "Double Exposure Field Delta", '', 'DoubleExposureFieldDelta'),
    0x00741401: ('SQ', '1', "Brachy Task Sequence", '', 'BrachyTaskSequence'),
    0x00741402: ('DS', '1', "Continuation Start Total Reference Air Kerma", '', 'ContinuationStartTotalReferenceAirKerma'),
    0x00741403: ('DS', '1', "Continuation End Total Reference Air Kerma", '', 'ContinuationEndTotalReferenceAirKerma'),
    0x00741404: ('IS', '1', "Continuation Pulse Number", '', 'ContinuationPulseNumber'),
    0x00741405: ('SQ', '1', "Channel Delivery Order Sequence", '', 'ChannelDeliveryOrderSequence'),
    0x00741406: ('IS', '1', "Referenced Channel Number", '', 'ReferencedChannelNumber'),
    0x00741407: ('DS', '1', "Start Cumulative Time Weight", '', 'StartCumulativeTimeWeight'),
    0x00741408: ('DS', '1', "End Cumulative Time Weight", '', 'EndCumulativeTimeWeight'),
    0x00741409: ('SQ', '1', "Omitted Channel Sequence", '', 'OmittedChannelSequence'),
    0x0074140A: ('CS', '1', "Reason for Channel Omission", '', 'ReasonForChannelOmission'),
    0x0074140B: ('LO', '1', "Reason for Channel Omission Description", '', 'ReasonForChannelOmissionDescription'),
    0x0074140C: ('IS', '1', "Channel Delivery Order Index", '', 'ChannelDeliveryOrderIndex'),
    0x0074140D: ('SQ', '1', "Channel Delivery Continuation Sequence", '', 'ChannelDeliveryContinuationSequence'),
    0x0074140E: ('SQ', '1', "Omitted Application Setup Sequence", '', 'OmittedApplicationSetupSequence'),
    0x00760001: ('LO', '1', "Implant Assembly Template Name", '', 'ImplantAssemblyTemplateName'),
    0x00760003: ('LO', '1', "Implant Assembly Template Issuer", '', 'ImplantAssemblyTemplateIssuer'),
    0x00760006: ('LO', '1', "Implant Assembly Template Version", '', 'ImplantAssemblyTemplateVersion'),
    0x00760008: ('SQ', '1', "Replaced Implant Assembly Template Sequence", '', 'ReplacedImplantAssemblyTemplateSequence'),
    0x0076000A: ('CS', '1', "Implant Assembly Template Type", '', 'ImplantAssemblyTemplateType'),
    0x0076000C: ('SQ', '1', "Original Implant Assembly Template Sequence", '', 'OriginalImplantAssemblyTemplateSequence'),
    0x0076000E: ('SQ', '1', "Derivation Implant Assembly Template Sequence", '', 'DerivationImplantAssemblyTemplateSequence'),
    0x00760010: ('SQ', '1', "Implant Assembly Template Target Anatomy Sequence", '', 'ImplantAssemblyTemplateTargetAnatomySequence'),
    0x00760020: ('SQ', '1', "Procedure Type Code Sequence", '', 'ProcedureTypeCodeSequence'),
    0x00760030: ('LO', '1', "Surgical Technique", '', 'SurgicalTechnique'),
    0x00760032: ('SQ', '1', "Component Types Sequence", '', 'ComponentTypesSequence'),
    0x00760034: ('SQ', '1', "Component Type Code Sequence", '', 'ComponentTypeCodeSequence'),
    0x00760036: ('CS', '1', "Exclusive Component Type", '', 'ExclusiveComponentType'),
    0x00760038: ('CS', '1', "Mandatory Component Type", '', 'MandatoryComponentType'),
    0x00760040: ('SQ', '1', "Component Sequence", '', 'ComponentSequence'),
    0x00760055: ('US', '1', "Component ID", '', 'ComponentID'),
    0x00760060: ('SQ', '1', "Component Assembly Sequence", '', 'ComponentAssemblySequence'),
    0x00760070: ('US', '1', "Component 1 Referenced ID", '', 'Component1ReferencedID'),
    0x00760080: ('US', '1', "Component 1 Referenced Mating Feature Set ID", '', 'Component1ReferencedMatingFeatureSetID'),
    0x00760090: ('US', '1', "Component 1 Referenced Mating Feature ID", '', 'Component1ReferencedMatingFeatureID'),
    0x007600A0: ('US', '1', "Component 2 Referenced ID", '', 'Component2ReferencedID'),
    0x007600B0: ('US', '1', "Component 2 Referenced Mating Feature Set ID", '', 'Component2ReferencedMatingFeatureSetID'),
    0x007600C0: ('US', '1', "Component 2 Referenced Mating Feature ID", '', 'Component2ReferencedMatingFeatureID'),
    0x00780001: ('LO', '1', "Implant Template Group Name", '', 'ImplantTemplateGroupName'),
    0x00780010: ('ST', '1', "Implant Template Group Description", '', 'ImplantTemplateGroupDescription'),
    0x00780020: ('LO', '1', "Implant Template Group Issuer", '', 'ImplantTemplateGroupIssuer'),
    0x00780024: ('LO', '1', "Implant Template Group Version", '', 'ImplantTemplateGroupVersion'),
    0x00780026: ('SQ', '1', "Replaced Implant Template Group Sequence", '', 'ReplacedImplantTemplateGroupSequence'),
    0x00780028: ('SQ', '1', "Implant Template Group Target Anatomy Sequence", '', 'ImplantTemplateGroupTargetAnatomySequence'),
    0x0078002A: ('SQ', '1', "Implant Template Group Members Sequence", '', 'ImplantTemplateGroupMembersSequence'),
    0x0078002E: ('US', '1', "Implant Template Group Member ID", '', 'ImplantTemplateGroupMemberID'),
    0x00780050: ('FD', '3', "3D Implant Template Group Member Matching Point", '', 'ThreeDImplantTemplateGroupMemberMatchingPoint'),
    0x00780060: ('FD', '9', "3D Implant Template Group Member Matching Axes", '', 'ThreeDImplantTemplateGroupMemberMatchingAxes'),
    0x00780070: ('SQ', '1', "Implant Template Group Member Matching 2D Coordinates Sequence", '', 'ImplantTemplateGroupMemberMatching2DCoordinatesSequence'),
    0x00780090: ('FD', '2', "2D Implant Template Group Member Matching Point", '', 'TwoDImplantTemplateGroupMemberMatchingPoint'),
    0x007800A0: ('FD', '4', "2D Implant Template Group Member Matching Axes", '', 'TwoDImplantTemplateGroupMemberMatchingAxes'),
    0x007800B0: ('SQ', '1', "Implant Template Group Variation Dimension Sequence", '', 'ImplantTemplateGroupVariationDimensionSequence'),
    0x007800B2: ('LO', '1', "Implant Template Group Variation Dimension Name", '', 'ImplantTemplateGroupVariationDimensionName'),
    0x007800B4: ('SQ', '1', "Implant Template Group Variation Dimension Rank Sequence", '', 'ImplantTemplateGroupVariationDimensionRankSequence'),
    0x007800B6: ('US', '1', "Referenced Implant Template Group Member ID", '', 'ReferencedImplantTemplateGroupMemberID'),
    0x007800B8: ('US', '1', "Implant Template Group Variation Dimension Rank", '', 'ImplantTemplateGroupVariationDimensionRank'),
    0x00800001: ('SQ', '1', "Surface Scan Acquisition Type Code Sequence", '', 'SurfaceScanAcquisitionTypeCodeSequence'),
    0x00800002: ('SQ', '1', "Surface Scan Mode Code Sequence", '', 'SurfaceScanModeCodeSequence'),
    0x00800003: ('SQ', '1', "Registration Method Code Sequence", '', 'RegistrationMethodCodeSequence'),
    0x00800004: ('FD', '1', "Shot Duration Time", '', 'ShotDurationTime'),
    0x00800005: ('FD', '1', "Shot Offset Time", '', 'ShotOffsetTime'),
    0x00800006: ('US', '1-n', "Surface Point Presentation Value Data", '', 'SurfacePointPresentationValueData'),
    0x00800007: ('US', '3-3n', "Surface Point Color CIELab Value Data", '', 'SurfacePointColorCIELabValueData'),
    0x00800008: ('SQ', '1', "UV Mapping Sequence", '', 'UVMappingSequence'),
    0x00800009: ('SH', '1', "Texture Label", '', 'TextureLabel'),
    0x00800010: ('OF', '1', "U Value Data", '', 'UValueData'),
    0x00800011: ('OF', '1', "V Value Data", '', 'VValueData'),
    0x00800012: ('SQ', '1', "Referenced Texture Sequence", '', 'ReferencedTextureSequence'),
    0x00800013: ('SQ', '1', "Referenced Surface Data Sequence", '', 'ReferencedSurfaceDataSequence'),
    0x00820001: ('CS', '1', "Assessment Summary", '', 'AssessmentSummary'),
    0x00820003: ('UT', '1', "Assessment Summary Description", '', 'AssessmentSummaryDescription'),
    0x00820004: ('SQ', '1', "Assessed SOP Instance Sequence", '', 'AssessedSOPInstanceSequence'),
    0x00820005: ('SQ', '1', "Referenced Comparison SOP Instance Sequence", '', 'ReferencedComparisonSOPInstanceSequence'),
    0x00820006: ('UL', '1', "Number of Assessment Observations", '', 'NumberOfAssessmentObservations'),
    0x00820007: ('SQ', '1', "Assessment Observations Sequence", '', 'AssessmentObservationsSequence'),
    0x00820008: ('CS', '1', "Observation Significance", '', 'ObservationSignificance'),
    0x0082000A: ('UT', '1', "Observation Description", '', 'ObservationDescription'),
    0x0082000C: ('SQ', '1', "Structured Constraint Observation Sequence", '', 'StructuredConstraintObservationSequence'),
    0x00820010: ('SQ', '1', "Assessed Attribute Value Sequence", '', 'AssessedAttributeValueSequence'),
    0x00820016: ('LO', '1', "Assessment Set ID", '', 'AssessmentSetID'),
    0x00820017: ('SQ', '1', "Assessment Requester Sequence", '', 'AssessmentRequesterSequence'),
    0x00820018: ('LO', '1', "Selector Attribute Name", '', 'SelectorAttributeName'),
    0x00820019: ('LO', '1', "Selector Attribute Keyword", '', 'SelectorAttributeKeyword'),
    0x00820021: ('SQ', '1', "Assessment Type Code Sequence", '', 'AssessmentTypeCodeSequence'),
    0x00820022: ('SQ', '1', "Observation Basis Code Sequence", '', 'ObservationBasisCodeSequence'),
    0x00820023: ('LO', '1', "Assessment Label", '', 'AssessmentLabel'),
    0x00820032: ('CS', '1', "Constraint Type", '', 'ConstraintType'),
    0x00820033: ('UT', '1', "Specification Selection Guidance", '', 'SpecificationSelectionGuidance'),
    0x00820034: ('SQ', '1', "Constraint Value Sequence", '', 'ConstraintValueSequence'),
    0x00820035: ('SQ', '1', "Recommended Default Value Sequence", '', 'RecommendedDefaultValueSequence'),
    0x00820036: ('CS', '1', "Constraint Violation Significance", '', 'ConstraintViolationSignificance'),
    0x00820037: ('UT', '1', "Constraint Violation Condition", '', 'ConstraintViolationCondition'),
    0x00820038: ('CS', '1', "Modifiable Constraint Flag", '', 'ModifiableConstraintFlag'),
    0x00880130: ('SH', '1', "Storage Media File-set ID", '', 'StorageMediaFileSetID'),
    0x00880140: ('UI', '1', "Storage Media File-set UID", '', 'StorageMediaFileSetUID'),
    0x00880200: ('SQ', '1', "Icon Image Sequence", '', 'IconImageSequence'),
    0x00880904: ('LO', '1', "Topic Title", 'Retired', 'TopicTitle'),
    0x00880906: ('ST', '1', "Topic Subject", 'Retired', 'TopicSubject'),
    0x00880910: ('LO', '1', "Topic Author", 'Retired', 'TopicAuthor'),
    0x00880912: ('LO', '1-32', "Topic Keywords", 'Retired', 'TopicKeywords'),
    0x01000410: ('CS', '1', "SOP Instance Status", '', 'SOPInstanceStatus'),
    0x01000420: ('DT', '1', "SOP Authorization DateTime", '', 'SOPAuthorizationDateTime'),
    0x01000424: ('LT', '1', "SOP Authorization Comment", '', 'SOPAuthorizationComment'),
    0x01000426: ('LO', '1', "Authorization Equipment Certification Number", '', 'AuthorizationEquipmentCertificationNumber'),
    0x04000005: ('US', '1', "MAC ID Number", '', 'MACIDNumber'),
    0x04000010: ('UI', '1', "MAC Calculation Transfer Syntax UID", '', 'MACCalculationTransferSyntaxUID'),
    0x04000015: ('CS', '1', "MAC Algorithm", '', 'MACAlgorithm'),
    0x04000020: ('AT', '1-n', "Data Elements Signed", '', 'DataElementsSigned'),
    0x04000100: ('UI', '1', "Digital Signature UID", '', 'DigitalSignatureUID'),
    0x04000105: ('DT', '1', "Digital Signature DateTime", '', 'DigitalSignatureDateTime'),
    0x04000110: ('CS', '1', "Certificate Type", '', 'CertificateType'),
    0x04000115: ('OB', '1', "Certificate of Signer", '', 'CertificateOfSigner'),
    0x04000120: ('OB', '1', "Signature", '', 'Signature'),
    0x04000305: ('CS', '1', "Certified Timestamp Type", '', 'CertifiedTimestampType'),
    0x04000310: ('OB', '1', "Certified Timestamp", '', 'CertifiedTimestamp'),
    0x04000315: ('FL', '1', "Retired-blank", 'Retired', ''),
    0x04000401: ('SQ', '1', "Digital Signature Purpose Code Sequence", '', 'DigitalSignaturePurposeCodeSequence'),
    0x04000402: ('SQ', '1', "Referenced Digital Signature Sequence", '', 'ReferencedDigitalSignatureSequence'),
    0x04000403: ('SQ', '1', "Referenced SOP Instance MAC Sequence", '', 'ReferencedSOPInstanceMACSequence'),
    0x04000404: ('OB', '1', "MAC", '', 'MAC'),
    0x04000500: ('SQ', '1', "Encrypted Attributes Sequence", '', 'EncryptedAttributesSequence'),
    0x04000510: ('UI', '1', "Encrypted Content Transfer Syntax UID", '', 'EncryptedContentTransferSyntaxUID'),
    0x04000520: ('OB', '1', "Encrypted Content", '', 'EncryptedContent'),
    0x04000550: ('SQ', '1', "Modified Attributes Sequence", '', 'ModifiedAttributesSequence'),
    0x04000551: ('SQ', '1', "Nonconforming Modified Attributes Sequence", '', 'NonconformingModifiedAttributesSequence'),
    0x04000552: ('OB', '1', "Nonconforming Data Element Value", '', 'NonconformingDataElementValue'),
    0x04000561: ('SQ', '1', "Original Attributes Sequence", '', 'OriginalAttributesSequence'),
    0x04000562: ('DT', '1', "Attribute Modification DateTime", '', 'AttributeModificationDateTime'),
    0x04000563: ('LO', '1', "Modifying System", '', 'ModifyingSystem'),
    0x04000564: ('LO', '1', "Source of Previous Values", '', 'SourceOfPreviousValues'),
    0x04000565: ('CS', '1', "Reason for the Attribute Modification", '', 'ReasonForTheAttributeModification'),
    0x04000600: ('CS', '1', "Instance Origin Status", '', 'InstanceOriginStatus'),
    0x20000010: ('IS', '1', "Number of Copies", '', 'NumberOfCopies'),
    0x2000001E: ('SQ', '1', "Printer Configuration Sequence", '', 'PrinterConfigurationSequence'),
    0x20000020: ('CS', '1', "Print Priority", '', 'PrintPriority'),
    0x20000030: ('CS', '1', "Medium Type", '', 'MediumType'),
    0x20000040: ('CS', '1', "Film Destination", '', 'FilmDestination'),
    0x20000050: ('LO', '1', "Film Session Label", '', 'FilmSessionLabel'),
    0x20000060: ('IS', '1', "Memory Allocation", '', 'MemoryAllocation'),
    0x20000061: ('IS', '1', "Maximum Memory Allocation", '', 'MaximumMemoryAllocation'),
    0x20000062: ('CS', '1', "Color Image Printing Flag", 'Retired', 'ColorImagePrintingFlag'),
    0x20000063: ('CS', '1', "Collation Flag", 'Retired', 'CollationFlag'),
    0x20000065: ('CS', '1', "Annotation Flag", 'Retired', 'AnnotationFlag'),
    0x20000067: ('CS', '1', "Image Overlay Flag", 'Retired', 'ImageOverlayFlag'),
    0x20000069: ('CS', '1', "Presentation LUT Flag", 'Retired', 'PresentationLUTFlag'),
    0x2000006A: ('CS', '1', "Image Box Presentation LUT Flag", 'Retired', 'ImageBoxPresentationLUTFlag'),
    0x200000A0: ('US', '1', "Memory Bit Depth", '', 'MemoryBitDepth'),
    0x200000A1: ('US', '1', "Printing Bit Depth", '', 'PrintingBitDepth'),
    0x200000A2: ('SQ', '1', "Media Installed Sequence", '', 'MediaInstalledSequence'),
    0x200000A4: ('SQ', '1', "Other Media Available Sequence", '', 'OtherMediaAvailableSequence'),
    0x200000A8: ('SQ', '1', "Supported Image Display Formats Sequence", '', 'SupportedImageDisplayFormatsSequence'),
    0x20000500: ('SQ', '1', "Referenced Film Box Sequence", '', 'ReferencedFilmBoxSequence'),
    0x20000510: ('SQ', '1', "Referenced Stored Print Sequence", 'Retired', 'ReferencedStoredPrintSequence'),
    0x20100010: ('ST', '1', "Image Display Format", '', 'ImageDisplayFormat'),
    0x20100030: ('CS', '1', "Annotation Display Format ID", '', 'AnnotationDisplayFormatID'),
    0x20100040: ('CS', '1', "Film Orientation", '', 'FilmOrientation'),
    0x20100050: ('CS', '1', "Film Size ID", '', 'FilmSizeID'),
    0x20100052: ('CS', '1', "Printer Resolution ID", '', 'PrinterResolutionID'),
    0x20100054: ('CS', '1', "Default Printer Resolution ID", '', 'DefaultPrinterResolutionID'),
    0x20100060: ('CS', '1', "Magnification Type", '', 'MagnificationType'),
    0x20100080: ('CS', '1', "Smoothing Type", '', 'SmoothingType'),
    0x201000A6: ('CS', '1', "Default Magnification Type", '', 'DefaultMagnificationType'),
    0x201000A7: ('CS', '1-n', "Other Magnification Types Available", '', 'OtherMagnificationTypesAvailable'),
    0x201000A8: ('CS', '1', "Default Smoothing Type", '', 'DefaultSmoothingType'),
    0x201000A9: ('CS', '1-n', "Other Smoothing Types Available", '', 'OtherSmoothingTypesAvailable'),
    0x20100100: ('CS', '1', "Border Density", '', 'BorderDensity'),
    0x20100110: ('CS', '1', "Empty Image Density", '', 'EmptyImageDensity'),
    0x20100120: ('US', '1', "Min Density", '', 'MinDensity'),
    0x20100130: ('US', '1', "Max Density", '', 'MaxDensity'),
    0x20100140: ('CS', '1', "Trim", '', 'Trim'),
    0x20100150: ('ST', '1', "Configuration Information", '', 'ConfigurationInformation'),
    0x20100152: ('LT', '1', "Configuration Information Description", '', 'ConfigurationInformationDescription'),
    0x20100154: ('IS', '1', "Maximum Collated Films", '', 'MaximumCollatedFilms'),
    0x2010015E: ('US', '1', "Illumination", '', 'Illumination'),
    0x20100160: ('US', '1', "Reflected Ambient Light", '', 'ReflectedAmbientLight'),
    0x20100376: ('DS', '2', "Printer Pixel Spacing", '', 'PrinterPixelSpacing'),
    0x20100500: ('SQ', '1', "Referenced Film Session Sequence", '', 'ReferencedFilmSessionSequence'),
    0x20100510: ('SQ', '1', "Referenced Image Box Sequence", '', 'ReferencedImageBoxSequence'),
    0x20100520: ('SQ', '1', "Referenced Basic Annotation Box Sequence", '', 'ReferencedBasicAnnotationBoxSequence'),
    0x20200010: ('US', '1', "Image Box Position", '', 'ImageBoxPosition'),
    0x20200020: ('CS', '1', "Polarity", '', 'Polarity'),
    0x20200030: ('DS', '1', "Requested Image Size", '', 'RequestedImageSize'),
    0x20200040: ('CS', '1', "Requested Decimate/Crop Behavior", '', 'RequestedDecimateCropBehavior'),
    0x20200050: ('CS', '1', "Requested Resolution ID", '', 'RequestedResolutionID'),
    0x202000A0: ('CS', '1', "Requested Image Size Flag", '', 'RequestedImageSizeFlag'),
    0x202000A2: ('CS', '1', "Decimate/Crop Result", '', 'DecimateCropResult'),
    0x20200110: ('SQ', '1', "Basic Grayscale Image Sequence", '', 'BasicGrayscaleImageSequence'),
    0x20200111: ('SQ', '1', "Basic Color Image Sequence", '', 'BasicColorImageSequence'),
    0x20200130: ('SQ', '1', "Referenced Image Overlay Box Sequence", 'Retired', 'ReferencedImageOverlayBoxSequence'),
    0x20200140: ('SQ', '1', "Referenced VOI LUT Box Sequence", 'Retired', 'ReferencedVOILUTBoxSequence'),
    0x20300010: ('US', '1', "Annotation Position", '', 'AnnotationPosition'),
    0x20300020: ('LO', '1', "Text String", '', 'TextString'),
    0x20400010: ('SQ', '1', "Referenced Overlay Plane Sequence", 'Retired', 'ReferencedOverlayPlaneSequence'),
    0x20400011: ('US', '1-99', "Referenced Overlay Plane Groups", 'Retired', 'ReferencedOverlayPlaneGroups'),
    0x20400020: ('SQ', '1', "Overlay Pixel Data Sequence", 'Retired', 'OverlayPixelDataSequence'),
    0x20400060: ('CS', '1', "Overlay Magnification Type", 'Retired', 'OverlayMagnificationType'),
    0x20400070: ('CS', '1', "Overlay Smoothing Type", 'Retired', 'OverlaySmoothingType'),
    0x20400072: ('CS', '1', "Overlay or Image Magnification", 'Retired', 'OverlayOrImageMagnification'),
    0x20400074: ('US', '1', "Magnify to Number of Columns", 'Retired', 'MagnifyToNumberOfColumns'),
    0x20400080: ('CS', '1', "Overlay Foreground Density", 'Retired', 'OverlayForegroundDensity'),
    0x20400082: ('CS', '1', "Overlay Background Density", 'Retired', 'OverlayBackgroundDensity'),
    0x20400090: ('CS', '1', "Overlay Mode", 'Retired', 'OverlayMode'),
    0x20400100: ('CS', '1', "Threshold Density", 'Retired', 'ThresholdDensity'),
    0x20400500: ('SQ', '1', "Referenced Image Box Sequence (Retired)", 'Retired', 'ReferencedImageBoxSequenceRetired'),
    0x20500010: ('SQ', '1', "Presentation LUT Sequence", '', 'PresentationLUTSequence'),
    0x20500020: ('CS', '1', "Presentation LUT Shape", '', 'PresentationLUTShape'),
    0x20500500: ('SQ', '1', "Referenced Presentation LUT Sequence", '', 'ReferencedPresentationLUTSequence'),
    0x21000010: ('SH', '1', "Print Job ID", 'Retired', 'PrintJobID'),
    0x21000020: ('CS', '1', "Execution Status", '', 'ExecutionStatus'),
    0x21000030: ('CS', '1', "Execution Status Info", '', 'ExecutionStatusInfo'),
    0x21000040: ('DA', '1', "Creation Date", '', 'CreationDate'),
    0x21000050: ('TM', '1', "Creation Time", '', 'CreationTime'),
    0x21000070: ('AE', '1', "Originator", '', 'Originator'),
    0x21000140: ('AE', '1', "Destination AE", '', 'DestinationAE'),
    0x21000160: ('SH', '1', "Owner ID", '', 'OwnerID'),
    0x21000170: ('IS', '1', "Number of Films", '', 'NumberOfFilms'),
    0x21000500: ('SQ', '1', "Referenced Print Job Sequence (Pull Stored Print)", 'Retired', 'ReferencedPrintJobSequencePullStoredPrint'),
    0x21100010: ('CS', '1', "Printer Status", '', 'PrinterStatus'),
    0x21100020: ('CS', '1', "Printer Status Info", '', 'PrinterStatusInfo'),
    0x21100030: ('LO', '1', "Printer Name", '', 'PrinterName'),
    0x21100099: ('SH', '1', "Print Queue ID", 'Retired', 'PrintQueueID'),
    0x21200010: ('CS', '1', "Queue Status", 'Retired', 'QueueStatus'),
    0x21200050: ('SQ', '1', "Print Job Description Sequence", 'Retired', 'PrintJobDescriptionSequence'),
    0x21200070: ('SQ', '1', "Referenced Print Job Sequence", 'Retired', 'ReferencedPrintJobSequence'),
    0x21300010: ('SQ', '1', "Print Management Capabilities Sequence", 'Retired', 'PrintManagementCapabilitiesSequence'),
    0x21300015: ('SQ', '1', "Printer Characteristics Sequence", 'Retired', 'PrinterCharacteristicsSequence'),
    0x21300030: ('SQ', '1', "Film Box Content Sequence", 'Retired', 'FilmBoxContentSequence'),
    0x21300040: ('SQ', '1', "Image Box Content Sequence", 'Retired', 'ImageBoxContentSequence'),
    0x21300050: ('SQ', '1', "Annotation Content Sequence", 'Retired', 'AnnotationContentSequence'),
    0x21300060: ('SQ', '1', "Image Overlay Box Content Sequence", 'Retired', 'ImageOverlayBoxContentSequence'),
    0x21300080: ('SQ', '1', "Presentation LUT Content Sequence", 'Retired', 'PresentationLUTContentSequence'),
    0x213000A0: ('SQ', '1', "Proposed Study Sequence", '', 'ProposedStudySequence'),
    0x213000C0: ('SQ', '1', "Original Image Sequence", '', 'OriginalImageSequence'),
    0x22000001: ('CS', '1', "Label Using Information Extracted From Instances", '', 'LabelUsingInformationExtractedFromInstances'),
    0x22000002: ('UT', '1', "Label Text", '', 'LabelText'),
    0x22000003: ('CS', '1', "Label Style Selection", '', 'LabelStyleSelection'),
    0x22000004: ('LT', '1', "Media Disposition", '', 'MediaDisposition'),
    0x22000005: ('LT', '1', "Barcode Value", '', 'BarcodeValue'),
    0x22000006: ('CS', '1', "Barcode Symbology", '', 'BarcodeSymbology'),
    0x22000007: ('CS', '1', "Allow Media Splitting", '', 'AllowMediaSplitting'),
    0x22000008: ('CS', '1', "Include Non-DICOM Objects", '', 'IncludeNonDICOMObjects'),
    0x22000009: ('CS', '1', "Include Display Application", '', 'IncludeDisplayApplication'),
    0x2200000A: ('CS', '1', "Preserve Composite Instances After Media Creation", '', 'PreserveCompositeInstancesAfterMediaCreation'),
    0x2200000B: ('US', '1', "Total Number of Pieces of Media Created", '', 'TotalNumberOfPiecesOfMediaCreated'),
    0x2200000C: ('LO', '1', "Requested Media Application Profile", '', 'RequestedMediaApplicationProfile'),
    0x2200000D: ('SQ', '1', "Referenced Storage Media Sequence", '', 'ReferencedStorageMediaSequence'),
    0x2200000E: ('AT', '1-n', "Failure Attributes", '', 'FailureAttributes'),
    0x2200000F: ('CS', '1', "Allow Lossy Compression", '', 'AllowLossyCompression'),
    0x22000020: ('CS', '1', "Request Priority", '', 'RequestPriority'),
    0x30020002: ('SH', '1', "RT Image Label", '', 'RTImageLabel'),
    0x30020003: ('LO', '1', "RT Image Name", '', 'RTImageName'),
    0x30020004: ('ST', '1', "RT Image Description", '', 'RTImageDescription'),
    0x3002000A: ('CS', '1', "Reported Values Origin", '', 'ReportedValuesOrigin'),
    0x3002000C: ('CS', '1', "RT Image Plane", '', 'RTImagePlane'),
    0x3002000D: ('DS', '3', "X-Ray Image Receptor Translation", '', 'XRayImageReceptorTranslation'),
    0x3002000E: ('DS', '1', "X-Ray Image Receptor Angle", '', 'XRayImageReceptorAngle'),
    0x30020010: ('DS', '6', "RT Image Orientation", '', 'RTImageOrientation'),
    0x30020011: ('DS', '2', "Image Plane Pixel Spacing", '', 'ImagePlanePixelSpacing'),
    0x30020012: ('DS', '2', "RT Image Position", '', 'RTImagePosition'),
    0x30020020: ('SH', '1', "Radiation Machine Name", '', 'RadiationMachineName'),
    0x30020022: ('DS', '1', "Radiation Machine SAD", '', 'RadiationMachineSAD'),
    0x30020024: ('DS', '1', "Radiation Machine SSD", '', 'RadiationMachineSSD'),
    0x30020026: ('DS', '1', "RT Image SID", '', 'RTImageSID'),
    0x30020028: ('DS', '1', "Source to Reference Object Distance", '', 'SourceToReferenceObjectDistance'),
    0x30020029: ('IS', '1', "Fraction Number", '', 'FractionNumber'),
    0x30020030: ('SQ', '1', "Exposure Sequence", '', 'ExposureSequence'),
    0x30020032: ('DS', '1', "Meterset Exposure", '', 'MetersetExposure'),
    0x30020034: ('DS', '4', "Diaphragm Position", '', 'DiaphragmPosition'),
    0x30020040: ('SQ', '1', "Fluence Map Sequence", '', 'FluenceMapSequence'),
    0x30020041: ('CS', '1', "Fluence Data Source", '', 'FluenceDataSource'),
    0x30020042: ('DS', '1', "Fluence Data Scale", '', 'FluenceDataScale'),
    0x30020050: ('SQ', '1', "Primary Fluence Mode Sequence", '', 'PrimaryFluenceModeSequence'),
    0x30020051: ('CS', '1', "Fluence Mode", '', 'FluenceMode'),
    0x30020052: ('SH', '1', "Fluence Mode ID", '', 'FluenceModeID'),
    0x30020100: ('IS', '1', "Selected Frame Number", '', 'SelectedFrameNumber'),
    0x30020101: ('SQ', '1', "Selected Frame Functional Groups Sequence", '', 'SelectedFrameFunctionalGroupsSequence'),
    0x30020102: ('SQ', '1', "RT Image Frame General Content Sequence", '', 'RTImageFrameGeneralContentSequence'),
    0x30020103: ('SQ', '1', "RT Image Frame Context Sequence", '', 'RTImageFrameContextSequence'),
    0x30020104: ('SQ', '1', "RT Image Scope Sequence", '', 'RTImageScopeSequence'),
    0x30020105: ('CS', '1', "Beam Modifier Coordinates Presence Flag", '', 'BeamModifierCoordinatesPresenceFlag'),
    0x30020106: ('FD', '1', "Start Cumulative Meterset", '', 'StartCumulativeMeterset'),
    0x30020107: ('FD', '1', "Stop Cumulative Meterset", '', 'StopCumulativeMeterset'),
    0x30020108: ('SQ', '1', "RT Acquisition Patient Position Sequence", '', 'RTAcquisitionPatientPositionSequence'),
    0x30020109: ('SQ', '1', "RT Image Frame Imaging Device Position Sequence", '', 'RTImageFrameImagingDevicePositionSequence'),
    0x3002010A: ('SQ', '1', "RT Image Frame kV Radiation Acquisition Sequence", '', 'RTImageFramekVRadiationAcquisitionSequence'),
    0x3002010B: ('SQ', '1', "RT Image Frame MV Radiation Acquisition Sequence", '', 'RTImageFrameMVRadiationAcquisitionSequence'),
    0x3002010C: ('SQ', '1', "RT Image Frame Radiation Acquisition Sequence", '', 'RTImageFrameRadiationAcquisitionSequence'),
    0x3002010D: ('SQ', '1', "Imaging Source Position Sequence", '', 'ImagingSourcePositionSequence'),
    0x3002010E: ('SQ', '1', "Image Receptor Position Sequence", '', 'ImageReceptorPositionSequence'),
    0x3002010F: ('FD', '16', "Device Position to Equipment Mapping Matrix", '', 'DevicePositionToEquipmentMappingMatrix'),
    0x30020110: ('SQ', '1', "Device Position Parameter Sequence", '', 'DevicePositionParameterSequence'),
    0x30020111: ('CS', '1', "Imaging Source Location Specification Type", '', 'ImagingSourceLocationSpecificationType'),
    0x30020112: ('SQ', '1', "Imaging Device Location Matrix Sequence", '', 'ImagingDeviceLocationMatrixSequence'),
    0x30020113: ('SQ', '1', "Imaging Device Location Parameter Sequence", '', 'ImagingDeviceLocationParameterSequence'),
    0x30020114: ('SQ', '1', "Imaging Aperture Sequence", '', 'ImagingApertureSequence'),
    0x30020115: ('CS', '1', "Imaging Aperture Specification Type", '', 'ImagingApertureSpecificationType'),
    0x30020116: ('US', '1', "Number of Acquisition Devices", '', 'NumberOfAcquisitionDevices'),
    0x30020117: ('SQ', '1', "Acquisition Device Sequence", '', 'AcquisitionDeviceSequence'),
    0x30020118: ('SQ', '1', "Acquisition Task Sequence", '', 'AcquisitionTaskSequence'),
    0x30020119: ('SQ', '1', "Acquisition Task Workitem Code Sequence", '', 'AcquisitionTaskWorkitemCodeSequence'),
    0x3002011A: ('SQ', '1', "Acquisition Subtask Sequence", '', 'AcquisitionSubtaskSequence'),
    0x3002011B: ('SQ', '1', "Subtask Workitem Code Sequence", '', 'SubtaskWorkitemCodeSequence'),
    0x3002011C: ('US', '1', "Acquisition Task Index", '', 'AcquisitionTaskIndex'),
    0x3002011D: ('US', '1', "Acquisition Subtask Index", '', 'AcquisitionSubtaskIndex'),
    0x3002011E: ('SQ', '1', "Referenced Baseline Parameters RT Radiation Instance Sequence", '', 'ReferencedBaselineParametersRTRadiationInstanceSequence'),
    0x3002011F: ('SQ', '1', "Position Acquisition Template Identification Sequence", '', 'PositionAcquisitionTemplateIdentificationSequence'),
    0x30020120: ('ST', '1', "Position Acquisition Template ID", '', 'PositionAcquisitionTemplateID'),
    0x30020121: ('LO', '1', "Position Acquisition Template Name", '', 'PositionAcquisitionTemplateName'),
    0x30020122: ('SQ', '1', "Position Acquisition Template Code Sequence", '', 'PositionAcquisitionTemplateCodeSequence'),
    0x30020123: ('LT', '1', "Position Acquisition Template Description", '', 'PositionAcquisitionTemplateDescription'),
    0x30020124: ('SQ', '1', "Acquisition Task Applicability Sequence", '', 'AcquisitionTaskApplicabilitySequence'),
    0x30020125: ('SQ', '1', "Projection Imaging Acquisition Parameter Sequence", '', 'ProjectionImagingAcquisitionParameterSequence'),
    0x30020126: ('SQ', '1', "CT Imaging Acquisition Parameter Sequence", '', 'CTImagingAcquisitionParameterSequence'),
    0x30020127: ('SQ', '1', "KV Imaging Generation Parameters Sequence", '', 'KVImagingGenerationParametersSequence'),
    0x30020128: ('SQ', '1', "MV Imaging Generation Parameters Sequence", '', 'MVImagingGenerationParametersSequence'),
    0x30020129: ('CS', '1', "Acquisition Signal Type", '', 'AcquisitionSignalType'),
    0x3002012A: ('CS', '1', "Acquisition Method", '', 'AcquisitionMethod'),
    0x3002012B: ('SQ', '1', "Scan Start Position Sequence", '', 'ScanStartPositionSequence'),
    0x3002012C: ('SQ', '1', "Scan Stop Position Sequence", '', 'ScanStopPositionSequence'),
    0x3002012D: ('FD', '1', "Imaging Source to Beam Modifier Definition Plane Distance", '', 'ImagingSourceToBeamModifierDefinitionPlaneDistance'),
    0x3002012E: ('CS', '1', "Scan Arc Type", '', 'ScanArcType'),
    0x3002012F: ('CS', '1', "Detector Positioning Type", '', 'DetectorPositioningType'),
    0x30020130: ('SQ', '1', "Additional RT Accessory Device Sequence", '', 'AdditionalRTAccessoryDeviceSequence'),
    0x30020131: ('SQ', '1', "Device-Specific Acquisition Parameter Sequence", '', 'DeviceSpecificAcquisitionParameterSequence'),
    0x30020132: ('SQ', '1', "Referenced Position Reference Instance Sequence", '', 'ReferencedPositionReferenceInstanceSequence'),
    0x30020133: ('SQ', '1', "Energy Derivation Code Sequence", '', 'EnergyDerivationCodeSequence'),
    0x30020134: ('FD', '1', "Maximum Cumulative Meterset Exposure", '', 'MaximumCumulativeMetersetExposure'),
    0x30020135: ('SQ', '1', "Acquisition Initiation Sequence", '', 'AcquisitionInitiationSequence'),
    0x30020136: ('SQ', '1', "RT Cone-Beam Imaging Geometry Sequence", '', 'RTConeBeamImagingGeometrySequence'),
    0x30040001: ('CS', '1', "DVH Type", '', 'DVHType'),
    0x30040002: ('CS', '1', "Dose Units", '', 'DoseUnits'),
    0x30040004: ('CS', '1', "Dose Type", '', 'DoseType'),
    0x30040005: ('CS', '1', "Spatial Transform of Dose", '', 'SpatialTransformOfDose'),
    0x30040006: ('LO', '1', "Dose Comment", '', 'DoseComment'),
    0x30040008: ('DS', '3', "Normalization Point", '', 'NormalizationPoint'),
    0x3004000A: ('CS', '1', "Dose Summation Type", '', 'DoseSummationType'),
    0x3004000C: ('DS', '2-n', "Grid Frame Offset Vector", '', 'GridFrameOffsetVector'),
    0x3004000E: ('DS', '1', "Dose Grid Scaling", '', 'DoseGridScaling'),
    0x30040010: ('SQ', '1', "RT Dose ROI Sequence", 'Retired', 'RTDoseROISequence'),
    0x30040012: ('DS', '1', "Dose Value", 'Retired', 'DoseValue'),
    0x30040014: ('CS', '1-3', "Tissue Heterogeneity Correction", '', 'TissueHeterogeneityCorrection'),
    0x30040040: ('DS', '3', "DVH Normalization Point", '', 'DVHNormalizationPoint'),
    0x30040042: ('DS', '1', "DVH Normalization Dose Value", '', 'DVHNormalizationDoseValue'),
    0x30040050: ('SQ', '1', "DVH Sequence", '', 'DVHSequence'),
    0x30040052: ('DS', '1', "DVH Dose Scaling", '', 'DVHDoseScaling'),
    0x30040054: ('CS', '1', "DVH Volume Units", '', 'DVHVolumeUnits'),
    0x30040056: ('IS', '1', "DVH Number of Bins", '', 'DVHNumberOfBins'),
    0x30040058: ('DS', '2-2n', "DVH Data", '', 'DVHData'),
    0x30040060: ('SQ', '1', "DVH Referenced ROI Sequence", '', 'DVHReferencedROISequence'),
    0x30040062: ('CS', '1', "DVH ROI Contribution Type", '', 'DVHROIContributionType'),
    0x30040070: ('DS', '1', "DVH Minimum Dose", '', 'DVHMinimumDose'),
    0x30040072: ('DS', '1', "DVH Maximum Dose", '', 'DVHMaximumDose'),
    0x30040074: ('DS', '1', "DVH Mean Dose", '', 'DVHMeanDose'),
    0x30060002: ('SH', '1', "Structure Set Label", '', 'StructureSetLabel'),
    0x30060004: ('LO', '1', "Structure Set Name", '', 'StructureSetName'),
    0x30060006: ('ST', '1', "Structure Set Description", '', 'StructureSetDescription'),
    0x30060008: ('DA', '1', "Structure Set Date", '', 'StructureSetDate'),
    0x30060009: ('TM', '1', "Structure Set Time", '', 'StructureSetTime'),
    0x30060010: ('SQ', '1', "Referenced Frame of Reference Sequence", '', 'ReferencedFrameOfReferenceSequence'),
    0x30060012: ('SQ', '1', "RT Referenced Study Sequence", '', 'RTReferencedStudySequence'),
    0x30060014: ('SQ', '1', "RT Referenced Series Sequence", '', 'RTReferencedSeriesSequence'),
    0x30060016: ('SQ', '1', "Contour Image Sequence", '', 'ContourImageSequence'),
    0x30060018: ('SQ', '1', "Predecessor Structure Set Sequence", '', 'PredecessorStructureSetSequence'),
    0x30060020: ('SQ', '1', "Structure Set ROI Sequence", '', 'StructureSetROISequence'),
    0x30060022: ('IS', '1', "ROI Number", '', 'ROINumber'),
    0x30060024: ('UI', '1', "Referenced Frame of Reference UID", '', 'ReferencedFrameOfReferenceUID'),
    0x30060026: ('LO', '1', "ROI Name", '', 'ROIName'),
    0x30060028: ('ST', '1', "ROI Description", '', 'ROIDescription'),
    0x3006002A: ('IS', '3', "ROI Display Color", '', 'ROIDisplayColor'),
    0x3006002C: ('DS', '1', "ROI Volume", '', 'ROIVolume'),
    0x3006002D: ('DT', '1', "ROI DateTime", '', 'ROIDateTime'),
    0x3006002E: ('DT', '1', "ROI Observation DateTime", '', 'ROIObservationDateTime'),
    0x30060030: ('SQ', '1', "RT Related ROI Sequence", '', 'RTRelatedROISequence'),
    0x30060033: ('CS', '1', "RT ROI Relationship", '', 'RTROIRelationship'),
    0x30060036: ('CS', '1', "ROI Generation Algorithm", '', 'ROIGenerationAlgorithm'),
    0x30060037: ('SQ', '1', "ROI Derivation Algorithm Identification Sequence", '', 'ROIDerivationAlgorithmIdentificationSequence'),
    0x30060038: ('LO', '1', "ROI Generation Description", '', 'ROIGenerationDescription'),
    0x30060039: ('SQ', '1', "ROI Contour Sequence", '', 'ROIContourSequence'),
    0x30060040: ('SQ', '1', "Contour Sequence", '', 'ContourSequence'),
    0x30060042: ('CS', '1', "Contour Geometric Type", '', 'ContourGeometricType'),
    0x30060044: ('DS', '1', "Contour Slab Thickness", 'Retired', 'ContourSlabThickness'),
    0x30060045: ('DS', '3', "Contour Offset Vector", 'Retired', 'ContourOffsetVector'),
    0x30060046: ('IS', '1', "Number of Contour Points", '', 'NumberOfContourPoints'),
    0x30060048: ('IS', '1', "Contour Number", '', 'ContourNumber'),
    0x30060049: ('IS', '1-n', "Attached Contours", 'Retired', 'AttachedContours'),
    0x3006004A: ('SQ', '1', "Source Pixel Planes Characteristics Sequence", '', 'SourcePixelPlanesCharacteristicsSequence'),
    0x3006004B: ('SQ', '1', "Source Series Sequence", '', 'SourceSeriesSequence'),
    0x3006004C: ('SQ', '1', "Source Series Information Sequence", '', 'SourceSeriesInformationSequence'),
    0x3006004D: ('SQ', '1', "ROI Creator Sequence", '', 'ROICreatorSequence'),
    0x3006004E: ('SQ', '1', "ROI Interpreter Sequence", '', 'ROIInterpreterSequence'),
    0x3006004F: ('SQ', '1', "ROI Observation Context Code Sequence", '', 'ROIObservationContextCodeSequence'),
    0x30060050: ('DS', '3-3n', "Contour Data", '', 'ContourData'),
    0x30060080: ('SQ', '1', "RT ROI Observations Sequence", '', 'RTROIObservationsSequence'),
    0x30060082: ('IS', '1', "Observation Number", '', 'ObservationNumber'),
    0x30060084: ('IS', '1', "Referenced ROI Number", '', 'ReferencedROINumber'),
    0x30060085: ('SH', '1', "ROI Observation Label", 'Retired', 'ROIObservationLabel'),
    0x30060086: ('SQ', '1', "RT ROI Identification Code Sequence", '', 'RTROIIdentificationCodeSequence'),
    0x30060088: ('ST', '1', "ROI Observation Description", 'Retired', 'ROIObservationDescription'),
    0x300600A0: ('SQ', '1', "Related RT ROI Observations Sequence", '', 'RelatedRTROIObservationsSequence'),
    0x300600A4: ('CS', '1', "RT ROI Interpreted Type", '', 'RTROIInterpretedType'),
    0x300600A6: ('PN', '1', "ROI Interpreter", '', 'ROIInterpreter'),
    0x300600B0: ('SQ', '1', "ROI Physical Properties Sequence", '', 'ROIPhysicalPropertiesSequence'),
    0x300600B2: ('CS', '1', "ROI Physical Property", '', 'ROIPhysicalProperty'),
    0x300600B4: ('DS', '1', "ROI Physical Property Value", '', 'ROIPhysicalPropertyValue'),
    0x300600B6: ('SQ', '1', "ROI Elemental Composition Sequence", '', 'ROIElementalCompositionSequence'),
    0x300600B7: ('US', '1', "ROI Elemental Composition Atomic Number", '', 'ROIElementalCompositionAtomicNumber'),
    0x300600B8: ('FL', '1', "ROI Elemental Composition Atomic Mass Fraction", '', 'ROIElementalCompositionAtomicMassFraction'),
    0x300600B9: ('SQ', '1', "Additional RT ROI Identification Code Sequence", 'Retired', 'AdditionalRTROIIdentificationCodeSequence'),
    0x300600C0: ('SQ', '1', "Frame of Reference Relationship Sequence", 'Retired', 'FrameOfReferenceRelationshipSequence'),
    0x300600C2: ('UI', '1', "Related Frame of Reference UID", 'Retired', 'RelatedFrameOfReferenceUID'),
    0x300600C4: ('CS', '1', "Frame of Reference Transformation Type", 'Retired', 'FrameOfReferenceTransformationType'),
    0x300600C6: ('DS', '16', "Frame of Reference Transformation Matrix", '', 'FrameOfReferenceTransformationMatrix'),
    0x300600C8: ('LO', '1', "Frame of Reference Transformation Comment", '', 'FrameOfReferenceTransformationComment'),
    0x300600C9: ('SQ', '1', "Patient Location Coordinates Sequence", '', 'PatientLocationCoordinatesSequence'),
    0x300600CA: ('SQ', '1', "Patient Location Coordinates Code Sequence", '', 'PatientLocationCoordinatesCodeSequence'),
    0x300600CB: ('SQ', '1', "Patient Support Position Sequence", '', 'PatientSupportPositionSequence'),
    0x30080010: ('SQ', '1', "Measured Dose Reference Sequence", '', 'MeasuredDoseReferenceSequence'),
    0x30080012: ('ST', '1', "Measured Dose Description", '', 'MeasuredDoseDescription'),
    0x30080014: ('CS', '1', "Measured Dose Type", '', 'MeasuredDoseType'),
    0x30080016: ('DS', '1', "Measured Dose Value", '', 'MeasuredDoseValue'),
    0x30080020: ('SQ', '1', "Treatment Session Beam Sequence", '', 'TreatmentSessionBeamSequence'),
    0x30080021: ('SQ', '1', "Treatment Session Ion Beam Sequence", '', 'TreatmentSessionIonBeamSequence'),
    0x30080022: ('IS', '1', "Current Fraction Number", '', 'CurrentFractionNumber'),
    0x30080024: ('DA', '1', "Treatment Control Point Date", '', 'TreatmentControlPointDate'),
    0x30080025: ('TM', '1', "Treatment Control Point Time", '', 'TreatmentControlPointTime'),
    0x3008002A: ('CS', '1', "Treatment Termination Status", '', 'TreatmentTerminationStatus'),
    0x3008002B: ('SH', '1', "Treatment Termination Code", 'Retired', 'TreatmentTerminationCode'),
    0x3008002C: ('CS', '1', "Treatment Verification Status", '', 'TreatmentVerificationStatus'),
    0x30080030: ('SQ', '1', "Referenced Treatment Record Sequence", '', 'ReferencedTreatmentRecordSequence'),
    0x30080032: ('DS', '1', "Specified Primary Meterset", '', 'SpecifiedPrimaryMeterset'),
    0x30080033: ('DS', '1', "Specified Secondary Meterset", '', 'SpecifiedSecondaryMeterset'),
    0x30080036: ('DS', '1', "Delivered Primary Meterset", '', 'DeliveredPrimaryMeterset'),
    0x30080037: ('DS', '1', "Delivered Secondary Meterset", '', 'DeliveredSecondaryMeterset'),
    0x3008003A: ('DS', '1', "Specified Treatment Time", '', 'SpecifiedTreatmentTime'),
    0x3008003B: ('DS', '1', "Delivered Treatment Time", '', 'DeliveredTreatmentTime'),
    0x30080040: ('SQ', '1', "Control Point Delivery Sequence", '', 'ControlPointDeliverySequence'),
    0x30080041: ('SQ', '1', "Ion Control Point Delivery Sequence", '', 'IonControlPointDeliverySequence'),
    0x30080042: ('DS', '1', "Specified Meterset", '', 'SpecifiedMeterset'),
    0x30080044: ('DS', '1', "Delivered Meterset", '', 'DeliveredMeterset'),
    0x30080045: ('FL', '1', "Meterset Rate Set", '', 'MetersetRateSet'),
    0x30080046: ('FL', '1', "Meterset Rate Delivered", '', 'MetersetRateDelivered'),
    0x30080047: ('FL', '1-n', "Scan Spot Metersets Delivered", '', 'ScanSpotMetersetsDelivered'),
    0x30080048: ('DS', '1', "Dose Rate Delivered", '', 'DoseRateDelivered'),
    0x30080050: ('SQ', '1', "Treatment Summary Calculated Dose Reference Sequence", '', 'TreatmentSummaryCalculatedDoseReferenceSequence'),
    0x30080052: ('DS', '1', "Cumulative Dose to Dose Reference", '', 'CumulativeDoseToDoseReference'),
    0x30080054: ('DA', '1', "First Treatment Date", '', 'FirstTreatmentDate'),
    0x30080056: ('DA', '1', "Most Recent Treatment Date", '', 'MostRecentTreatmentDate'),
    0x3008005A: ('IS', '1', "Number of Fractions Delivered", '', 'NumberOfFractionsDelivered'),
    0x30080060: ('SQ', '1', "Override Sequence", '', 'OverrideSequence'),
    0x30080061: ('AT', '1', "Parameter Sequence Pointer", '', 'ParameterSequencePointer'),
    0x30080062: ('AT', '1', "Override Parameter Pointer", '', 'OverrideParameterPointer'),
    0x30080063: ('IS', '1', "Parameter Item Index", '', 'ParameterItemIndex'),
    0x30080064: ('IS', '1', "Measured Dose Reference Number", '', 'MeasuredDoseReferenceNumber'),
    0x30080065: ('AT', '1', "Parameter Pointer", '', 'ParameterPointer'),
    0x30080066: ('ST', '1', "Override Reason", '', 'OverrideReason'),
    0x30080067: ('US', '1', "Parameter Value Number", '', 'ParameterValueNumber'),
    0x30080068: ('SQ', '1', "Corrected Parameter Sequence", '', 'CorrectedParameterSequence'),
    0x3008006A: ('FL', '1', "Correction Value", '', 'CorrectionValue'),
    0x30080070: ('SQ', '1', "Calculated Dose Reference Sequence", '', 'CalculatedDoseReferenceSequence'),
    0x30080072: ('IS', '1', "Calculated Dose Reference Number", '', 'CalculatedDoseReferenceNumber'),
    0x30080074: ('ST', '1', "Calculated Dose Reference Description", '', 'CalculatedDoseReferenceDescription'),
    0x30080076: ('DS', '1', "Calculated Dose Reference Dose Value", '', 'CalculatedDoseReferenceDoseValue'),
    0x30080078: ('DS', '1', "Start Meterset", '', 'StartMeterset'),
    0x3008007A: ('DS', '1', "End Meterset", '', 'EndMeterset'),
    0x30080080: ('SQ', '1', "Referenced Measured Dose Reference Sequence", '', 'ReferencedMeasuredDoseReferenceSequence'),
    0x30080082: ('IS', '1', "Referenced Measured Dose Reference Number", '', 'ReferencedMeasuredDoseReferenceNumber'),
    0x30080090: ('SQ', '1', "Referenced Calculated Dose Reference Sequence", '', 'ReferencedCalculatedDoseReferenceSequence'),
    0x30080092: ('IS', '1', "Referenced Calculated Dose Reference Number", '', 'ReferencedCalculatedDoseReferenceNumber'),
    0x300800A0: ('SQ', '1', "Beam Limiting Device Leaf Pairs Sequence", '', 'BeamLimitingDeviceLeafPairsSequence'),
    0x300800A1: ('SQ', '1', "Enhanced RT Beam Limiting Device Sequence", '', 'EnhancedRTBeamLimitingDeviceSequence'),
    0x300800A2: ('SQ', '1', "Enhanced RT Beam Limiting Opening Sequence", '', 'EnhancedRTBeamLimitingOpeningSequence'),
    0x300800A3: ('CS', '1', "Enhanced RT Beam Limiting Device Definition Flag", '', 'EnhancedRTBeamLimitingDeviceDefinitionFlag'),
    0x300800A4: ('FD', '2-2n', "Parallel RT Beam Delimiter Opening Extents", '', 'ParallelRTBeamDelimiterOpeningExtents'),
    0x300800B0: ('SQ', '1', "Recorded Wedge Sequence", '', 'RecordedWedgeSequence'),
    0x300800C0: ('SQ', '1', "Recorded Compensator Sequence", '', 'RecordedCompensatorSequence'),
    0x300800D0: ('SQ', '1', "Recorded Block Sequence", '', 'RecordedBlockSequence'),
    0x300800D1: ('SQ', '1', "Recorded Block Slab Sequence", '', 'RecordedBlockSlabSequence'),
    0x300800E0: ('SQ', '1', "Treatment Summary Measured Dose Reference Sequence", '', 'TreatmentSummaryMeasuredDoseReferenceSequence'),
    0x300800F0: ('SQ', '1', "Recorded Snout Sequence", '', 'RecordedSnoutSequence'),
    0x300800F2: ('SQ', '1', "Recorded Range Shifter Sequence", '', 'RecordedRangeShifterSequence'),
    0x300800F4: ('SQ', '1', "Recorded Lateral Spreading Device Sequence", '', 'RecordedLateralSpreadingDeviceSequence'),
    0x300800F6: ('SQ', '1', "Recorded Range Modulator Sequence", '', 'RecordedRangeModulatorSequence'),
    0x30080100: ('SQ', '1', "Recorded Source Sequence", '', 'RecordedSourceSequence'),
    0x30080105: ('LO', '1', "Source Serial Number", '', 'SourceSerialNumber'),
    0x30080110: ('SQ', '1', "Treatment Session Application Setup Sequence", '', 'TreatmentSessionApplicationSetupSequence'),
    0x30080116: ('CS', '1', "Application Setup Check", '', 'ApplicationSetupCheck'),
    0x30080120: ('SQ', '1', "Recorded Brachy Accessory Device Sequence", '', 'RecordedBrachyAccessoryDeviceSequence'),
    0x30080122: ('IS', '1', "Referenced Brachy Accessory Device Number", '', 'ReferencedBrachyAccessoryDeviceNumber'),
    0x30080130: ('SQ', '1', "Recorded Channel Sequence", '', 'RecordedChannelSequence'),
    0x30080132: ('DS', '1', "Specified Channel Total Time", '', 'SpecifiedChannelTotalTime'),
    0x30080134: ('DS', '1', "Delivered Channel Total Time", '', 'DeliveredChannelTotalTime'),
    0x30080136: ('IS', '1', "Specified Number of Pulses", '', 'SpecifiedNumberOfPulses'),
    0x30080138: ('IS', '1', "Delivered Number of Pulses", '', 'DeliveredNumberOfPulses'),
    0x3008013A: ('DS', '1', "Specified Pulse Repetition Interval", '', 'SpecifiedPulseRepetitionInterval'),
    0x3008013C: ('DS', '1', "Delivered Pulse Repetition Interval", '', 'DeliveredPulseRepetitionInterval'),
    0x30080140: ('SQ', '1', "Recorded Source Applicator Sequence", '', 'RecordedSourceApplicatorSequence'),
    0x30080142: ('IS', '1', "Referenced Source Applicator Number", '', 'ReferencedSourceApplicatorNumber'),
    0x30080150: ('SQ', '1', "Recorded Channel Shield Sequence", '', 'RecordedChannelShieldSequence'),
    0x30080152: ('IS', '1', "Referenced Channel Shield Number", '', 'ReferencedChannelShieldNumber'),
    0x30080160: ('SQ', '1', "Brachy Control Point Delivered Sequence", '', 'BrachyControlPointDeliveredSequence'),
    0x30080162: ('DA', '1', "Safe Position Exit Date", '', 'SafePositionExitDate'),
    0x30080164: ('TM', '1', "Safe Position Exit Time", '', 'SafePositionExitTime'),
    0x30080166: ('DA', '1', "Safe Position Return Date", '', 'SafePositionReturnDate'),
    0x30080168: ('TM', '1', "Safe Position Return Time", '', 'SafePositionReturnTime'),
    0x30080171: ('SQ', '1', "Pulse Specific Brachy Control Point Delivered Sequence", '', 'PulseSpecificBrachyControlPointDeliveredSequence'),
    0x30080172: ('US', '1', "Pulse Number", '', 'PulseNumber'),
    0x30080173: ('SQ', '1', "Brachy Pulse Control Point Delivered Sequence", '', 'BrachyPulseControlPointDeliveredSequence'),
    0x30080200: ('CS', '1', "Current Treatment Status", '', 'CurrentTreatmentStatus'),
    0x30080202: ('ST', '1', "Treatment Status Comment", '', 'TreatmentStatusComment'),
    0x30080220: ('SQ', '1', "Fraction Group Summary Sequence", '', 'FractionGroupSummarySequence'),
    0x30080223: ('IS', '1', "Referenced Fraction Number", '', 'ReferencedFractionNumber'),
    0x30080224: ('CS', '1', "Fraction Group Type", '', 'FractionGroupType'),
    0x30080230: ('CS', '1', "Beam Stopper Position", '', 'BeamStopperPosition'),
    0x30080240: ('SQ', '1', "Fraction Status Summary Sequence", '', 'FractionStatusSummarySequence'),
    0x30080250: ('DA', '1', "Treatment Date", '', 'TreatmentDate'),
    0x30080251: ('TM', '1', "Treatment Time", '', 'TreatmentTime'),
    0x300A0002: ('SH', '1', "RT Plan Label", '', 'RTPlanLabel'),
    0x300A0003: ('LO', '1', "RT Plan Name", '', 'RTPlanName'),
    0x300A0004: ('ST', '1', "RT Plan Description", '', 'RTPlanDescription'),
    0x300A0006: ('DA', '1', "RT Plan Date", '', 'RTPlanDate'),
    0x300A0007: ('TM', '1', "RT Plan Time", '', 'RTPlanTime'),
    0x300A0009: ('LO', '1-n', "Treatment Protocols", '', 'TreatmentProtocols'),
    0x300A000A: ('CS', '1', "Plan Intent", '', 'PlanIntent'),
    0x300A000B: ('LO', '1-n', "Treatment Sites", 'Retired', 'TreatmentSites'),
    0x300A000C: ('CS', '1', "RT Plan Geometry", '', 'RTPlanGeometry'),
    0x300A000E: ('ST', '1', "Prescription Description", '', 'PrescriptionDescription'),
    0x300A0010: ('SQ', '1', "Dose Reference Sequence", '', 'DoseReferenceSequence'),
    0x300A0012: ('IS', '1', "Dose Reference Number", '', 'DoseReferenceNumber'),
    0x300A0013: ('UI', '1', "Dose Reference UID", '', 'DoseReferenceUID'),
    0x300A0014: ('CS', '1', "Dose Reference Structure Type", '', 'DoseReferenceStructureType'),
    0x300A0015: ('CS', '1', "Nominal Beam Energy Unit", '', 'NominalBeamEnergyUnit'),
    0x300A0016: ('LO', '1', "Dose Reference Description", '', 'DoseReferenceDescription'),
    0x300A0018: ('DS', '3', "Dose Reference Point Coordinates", '', 'DoseReferencePointCoordinates'),
    0x300A001A: ('DS', '1', "Nominal Prior Dose", '', 'NominalPriorDose'),
    0x300A0020: ('CS', '1', "Dose Reference Type", '', 'DoseReferenceType'),
    0x300A0021: ('DS', '1', "Constraint Weight", '', 'ConstraintWeight'),
    0x300A0022: ('DS', '1', "Delivery Warning Dose", '', 'DeliveryWarningDose'),
    0x300A0023: ('DS', '1', "Delivery Maximum Dose", '', 'DeliveryMaximumDose'),
    0x300A0025: ('DS', '1', "Target Minimum Dose", '', 'TargetMinimumDose'),
    0x300A0026: ('DS', '1', "Target Prescription Dose", '', 'TargetPrescriptionDose'),
    0x300A0027: ('DS', '1', "Target Maximum Dose", '', 'TargetMaximumDose'),
    0x300A0028: ('DS', '1', "Target Underdose Volume Fraction", '', 'TargetUnderdoseVolumeFraction'),
    0x300A002A: ('DS', '1', "Organ at Risk Full-volume Dose", '', 'OrganAtRiskFullVolumeDose'),
    0x300A002B: ('DS', '1', "Organ at Risk Limit Dose", '', 'OrganAtRiskLimitDose'),
    0x300A002C: ('DS', '1', "Organ at Risk Maximum Dose", '', 'OrganAtRiskMaximumDose'),
    0x300A002D: ('DS', '1', "Organ at Risk Overdose Volume Fraction", '', 'OrganAtRiskOverdoseVolumeFraction'),
    0x300A0040: ('SQ', '1', "Tolerance Table Sequence", '', 'ToleranceTableSequence'),
    0x300A0042: ('IS', '1', "Tolerance Table Number", '', 'ToleranceTableNumber'),
    0x300A0043: ('SH', '1', "Tolerance Table Label", '', 'ToleranceTableLabel'),
    0x300A0044: ('DS', '1', "Gantry Angle Tolerance", '', 'GantryAngleTolerance'),
    0x300A0046: ('DS', '1', "Beam Limiting Device Angle Tolerance", '', 'BeamLimitingDeviceAngleTolerance'),
    0x300A0048: ('SQ', '1', "Beam Limiting Device Tolerance Sequence", '', 'BeamLimitingDeviceToleranceSequence'),
    0x300A004A: ('DS', '1', "Beam Limiting Device Position Tolerance", '', 'BeamLimitingDevicePositionTolerance'),
    0x300A004B: ('FL', '1', "Snout Position Tolerance", '', 'SnoutPositionTolerance'),
    0x300A004C: ('DS', '1', "Patient Support Angle Tolerance", '', 'PatientSupportAngleTolerance'),
    0x300A004E: ('DS', '1', "Table Top Eccentric Angle Tolerance", '', 'TableTopEccentricAngleTolerance'),
    0x300A004F: ('FL', '1', "Table Top Pitch Angle Tolerance", '', 'TableTopPitchAngleTolerance'),
    0x300A0050: ('FL', '1', "Table Top Roll Angle Tolerance", '', 'TableTopRollAngleTolerance'),
    0x300A0051: ('DS', '1', "Table Top Vertical Position Tolerance", '', 'TableTopVerticalPositionTolerance'),
    0x300A0052: ('DS', '1', "Table Top Longitudinal Position Tolerance", '', 'TableTopLongitudinalPositionTolerance'),
    0x300A0053: ('DS', '1', "Table Top Lateral Position Tolerance", '', 'TableTopLateralPositionTolerance'),
    0x300A0054: ('UI', '1', "Table Top Position Alignment UID", '', 'TableTopPositionAlignmentUID'),
    0x300A0055: ('CS', '1', "RT Plan Relationship", '', 'RTPlanRelationship'),
    0x300A0070: ('SQ', '1', "Fraction Group Sequence", '', 'FractionGroupSequence'),
    0x300A0071: ('IS', '1', "Fraction Group Number", '', 'FractionGroupNumber'),
    0x300A0072: ('LO', '1', "Fraction Group Description", '', 'FractionGroupDescription'),
    0x300A0078: ('IS', '1', "Number of Fractions Planned", '', 'NumberOfFractionsPlanned'),
    0x300A0079: ('IS', '1', "Number of Fraction Pattern Digits Per Day", '', 'NumberOfFractionPatternDigitsPerDay'),
    0x300A007A: ('IS', '1', "Repeat Fraction Cycle Length", '', 'RepeatFractionCycleLength'),
    0x300A007B: ('LT', '1', "Fraction Pattern", '', 'FractionPattern'),
    0x300A0080: ('IS', '1', "Number of Beams", '', 'NumberOfBeams'),
    0x300A0082: ('DS', '3', "Beam Dose Specification Point", 'Retired', 'BeamDoseSpecificationPoint'),
    0x300A0083: ('UI', '1', "Referenced Dose Reference UID", '', 'ReferencedDoseReferenceUID'),
    0x300A0084: ('DS', '1', "Beam Dose", '', 'BeamDose'),
    0x300A0086: ('DS', '1', "Beam Meterset", '', 'BeamMeterset'),
    0x300A0088: ('FL', '1', "Beam Dose Point Depth", '', 'BeamDosePointDepth'),
    0x300A0089: ('FL', '1', "Beam Dose Point Equivalent Depth", '', 'BeamDosePointEquivalentDepth'),
    0x300A008A: ('FL', '1', "Beam Dose Point SSD", '', 'BeamDosePointSSD'),
    0x300A008B: ('CS', '1', "Beam Dose Meaning", '', 'BeamDoseMeaning'),
    0x300A008C: ('SQ', '1', "Beam Dose Verification Control Point Sequence", '', 'BeamDoseVerificationControlPointSequence'),
    0x300A008D: ('FL', '1', "Average Beam Dose Point Depth", 'Retired', 'AverageBeamDosePointDepth'),
    0x300A008E: ('FL', '1', "Average Beam Dose Point Equivalent Depth", 'Retired', 'AverageBeamDosePointEquivalentDepth'),
    0x300A008F: ('FL', '1', "Average Beam Dose Point SSD", 'Retired', 'AverageBeamDosePointSSD'),
    0x300A0090: ('CS', '1', "Beam Dose Type", '', 'BeamDoseType'),
    0x300A0091: ('DS', '1', "Alternate Beam Dose", '', 'AlternateBeamDose'),
    0x300A0092: ('CS', '1', "Alternate Beam Dose Type", '', 'AlternateBeamDoseType'),
    0x300A0093: ('CS', '1', "Depth Value Averaging Flag", '', 'DepthValueAveragingFlag'),
    0x300A0094: ('DS', '1', "Beam Dose Point Source to External Contour Distance", '', 'BeamDosePointSourceToExternalContourDistance'),
    0x300A00A0: ('IS', '1', "Number of Brachy Application Setups", '', 'NumberOfBrachyApplicationSetups'),
    0x300A00A2: ('DS', '3', "Brachy Application Setup Dose Specification Point", '', 'BrachyApplicationSetupDoseSpecificationPoint'),
    0x300A00A4: ('DS', '1', "Brachy Application Setup Dose", '', 'BrachyApplicationSetupDose'),
    0x300A00B0: ('SQ', '1', "Beam Sequence", '', 'BeamSequence'),
    0x300A00B2: ('SH', '1', "Treatment Machine Name", '', 'TreatmentMachineName'),
    0x300A00B3: ('CS', '1', "Primary Dosimeter Unit", '', 'PrimaryDosimeterUnit'),
    0x300A00B4: ('DS', '1', "Source-Axis Distance", '', 'SourceAxisDistance'),
    0x300A00B6: ('SQ', '1', "Beam Limiting Device Sequence", '', 'BeamLimitingDeviceSequence'),
    0x300A00B8: ('CS', '1', "RT Beam Limiting Device Type", '', 'RTBeamLimitingDeviceType'),
    0x300A00BA: ('DS', '1', "Source to Beam Limiting Device Distance", '', 'SourceToBeamLimitingDeviceDistance'),
    0x300A00BB: ('FL', '1', "Isocenter to Beam Limiting Device Distance", '', 'IsocenterToBeamLimitingDeviceDistance'),
    0x300A00BC: ('IS', '1', "Number of Leaf/Jaw Pairs", '', 'NumberOfLeafJawPairs'),
    0x300A00BE: ('DS', '3-n', "Leaf Position Boundaries", '', 'LeafPositionBoundaries'),
    0x300A00C0: ('IS', '1', "Beam Number", '', 'BeamNumber'),
    0x300A00C2: ('LO', '1', "Beam Name", '', 'BeamName'),
    0x300A00C3: ('ST', '1', "Beam Description", '', 'BeamDescription'),
    0x300A00C4: ('CS', '1', "Beam Type", '', 'BeamType'),
    0x300A00C5: ('FD', '1', "Beam Delivery Duration Limit", '', 'BeamDeliveryDurationLimit'),
    0x300A00C6: ('CS', '1', "Radiation Type", '', 'RadiationType'),
    0x300A00C7: ('CS', '1', "High-Dose Technique Type", '', 'HighDoseTechniqueType'),
    0x300A00C8: ('IS', '1', "Reference Image Number", '', 'ReferenceImageNumber'),
    0x300A00CA: ('SQ', '1', "Planned Verification Image Sequence", '', 'PlannedVerificationImageSequence'),
    0x300A00CC: ('LO', '1-n', "Imaging Device-Specific Acquisition Parameters", '', 'ImagingDeviceSpecificAcquisitionParameters'),
    0x300A00CE: ('CS', '1', "Treatment Delivery Type", '', 'TreatmentDeliveryType'),
    0x300A00D0: ('IS', '1', "Number of Wedges", '', 'NumberOfWedges'),
    0x300A00D1: ('SQ', '1', "Wedge Sequence", '', 'WedgeSequence'),
    0x300A00D2: ('IS', '1', "Wedge Number", '', 'WedgeNumber'),
    0x300A00D3: ('CS', '1', "Wedge Type", '', 'WedgeType'),
    0x300A00D4: ('SH', '1', "Wedge ID", '', 'WedgeID'),
    0x300A00D5: ('IS', '1', "Wedge Angle", '', 'WedgeAngle'),
    0x300A00D6: ('DS', '1', "Wedge Factor", '', 'WedgeFactor'),
    0x300A00D7: ('FL', '1', "Total Wedge Tray Water-Equivalent Thickness", '', 'TotalWedgeTrayWaterEquivalentThickness'),
    0x300A00D8: ('DS', '1', "Wedge Orientation", '', 'WedgeOrientation'),
    0x300A00D9: ('FL', '1', "Isocenter to Wedge Tray Distance", '', 'IsocenterToWedgeTrayDistance'),
    0x300A00DA: ('DS', '1', "Source to Wedge Tray Distance", '', 'SourceToWedgeTrayDistance'),
    0x300A00DB: ('FL', '1', "Wedge Thin Edge Position", '', 'WedgeThinEdgePosition'),
    0x300A00DC: ('SH', '1', "Bolus ID", '', 'BolusID'),
    0x300A00DD: ('ST', '1', "Bolus Description", '', 'BolusDescription'),
    0x300A00DE: ('DS', '1', "Effective Wedge Angle", '', 'EffectiveWedgeAngle'),
    0x300A00E0: ('IS', '1', "Number of Compensators", '', 'NumberOfCompensators'),
    0x300A00E1: ('SH', '1', "Material ID", '', 'MaterialID'),
    0x300A00E2: ('DS', '1', "Total Compensator Tray Factor", '', 'TotalCompensatorTrayFactor'),
    0x300A00E3: ('SQ', '1', "Compensator Sequence", '', 'CompensatorSequence'),
    0x300A00E4: ('IS', '1', "Compensator Number", '', 'CompensatorNumber'),
    0x300A00E5: ('SH', '1', "Compensator ID", '', 'CompensatorID'),
    0x300A00E6: ('DS', '1', "Source to Compensator Tray Distance", '', 'SourceToCompensatorTrayDistance'),
    0x300A00E7: ('IS', '1', "Compensator Rows", '', 'CompensatorRows'),
    0x300A00E8: ('IS', '1', "Compensator Columns", '', 'CompensatorColumns'),
    0x300A00E9: ('DS', '2', "Compensator Pixel Spacing", '', 'CompensatorPixelSpacing'),
    0x300A00EA: ('DS', '2', "Compensator Position", '', 'CompensatorPosition'),
    0x300A00EB: ('DS', '1-n', "Compensator Transmission Data", '', 'CompensatorTransmissionData'),
    0x300A00EC: ('DS', '1-n', "Compensator Thickness Data", '', 'CompensatorThicknessData'),
    0x300A00ED: ('IS', '1', "Number of Boli", '', 'NumberOfBoli'),
    0x300A00EE: ('CS', '1', "Compensator Type", '', 'CompensatorType'),
    0x300A00EF: ('SH', '1', "Compensator Tray ID", '', 'CompensatorTrayID'),
    0x300A00F0: ('IS', '1', "Number of Blocks", '', 'NumberOfBlocks'),
    0x300A00F2: ('DS', '1', "Total Block Tray Factor", '', 'TotalBlockTrayFactor'),
    0x300A00F3: ('FL', '1', "Total Block Tray Water-Equivalent Thickness", '', 'TotalBlockTrayWaterEquivalentThickness'),
    0x300A00F4: ('SQ', '1', "Block Sequence", '', 'BlockSequence'),
    0x300A00F5: ('SH', '1', "Block Tray ID", '', 'BlockTrayID'),
    0x300A00F6: ('DS', '1', "Source to Block Tray Distance", '', 'SourceToBlockTrayDistance'),
    0x300A00F7: ('FL', '1', "Isocenter to Block Tray Distance", '', 'IsocenterToBlockTrayDistance'),
    0x300A00F8: ('CS', '1', "Block Type", '', 'BlockType'),
    0x300A00F9: ('LO', '1', "Accessory Code", '', 'AccessoryCode'),
    0x300A00FA: ('CS', '1', "Block Divergence", '', 'BlockDivergence'),
    0x300A00FB: ('CS', '1', "Block Mounting Position", '', 'BlockMountingPosition'),
    0x300A00FC: ('IS', '1', "Block Number", '', 'BlockNumber'),
    0x300A00FE: ('LO', '1', "Block Name", '', 'BlockName'),
    0x300A0100: ('DS', '1', "Block Thickness", '', 'BlockThickness'),
    0x300A0102: ('DS', '1', "Block Transmission", '', 'BlockTransmission'),
    0x300A0104: ('IS', '1', "Block Number of Points", '', 'BlockNumberOfPoints'),
    0x300A0106: ('DS', '2-2n', "Block Data", '', 'BlockData'),
    0x300A0107: ('SQ', '1', "Applicator Sequence", '', 'ApplicatorSequence'),
    0x300A0108: ('SH', '1', "Applicator ID", '', 'ApplicatorID'),
    0x300A0109: ('CS', '1', "Applicator Type", '', 'ApplicatorType'),
    0x300A010A: ('LO', '1', "Applicator Description", '', 'ApplicatorDescription'),
    0x300A010C: ('DS', '1', "Cumulative Dose Reference Coefficient", '', 'CumulativeDoseReferenceCoefficient'),
    0x300A010E: ('DS', '1', "Final Cumulative Meterset Weight", '', 'FinalCumulativeMetersetWeight'),
    0x300A0110: ('IS', '1', "Number of Control Points", '', 'NumberOfControlPoints'),
    0x300A0111: ('SQ', '1', "Control Point Sequence", '', 'ControlPointSequence'),
    0x300A0112: ('IS', '1', "Control Point Index", '', 'ControlPointIndex'),
    0x300A0114: ('DS', '1', "Nominal Beam Energy", '', 'NominalBeamEnergy'),
    0x300A0115: ('DS', '1', "Dose Rate Set", '', 'DoseRateSet'),
    0x300A0116: ('SQ', '1', "Wedge Position Sequence", '', 'WedgePositionSequence'),
    0x300A0118: ('CS', '1', "Wedge Position", '', 'WedgePosition'),
    0x300A011A: ('SQ', '1', "Beam Limiting Device Position Sequence", '', 'BeamLimitingDevicePositionSequence'),
    0x300A011C: ('DS', '2-2n', "Leaf/Jaw Positions", '', 'LeafJawPositions'),
    0x300A011E: ('DS', '1', "Gantry Angle", '', 'GantryAngle'),
    0x300A011F: ('CS', '1', "Gantry Rotation Direction", '', 'GantryRotationDirection'),
    0x300A0120: ('DS', '1', "Beam Limiting Device Angle", '', 'BeamLimitingDeviceAngle'),
    0x300A0121: ('CS', '1', "Beam Limiting Device Rotation Direction", '', 'BeamLimitingDeviceRotationDirection'),
    0x300A0122: ('DS', '1', "Patient Support Angle", '', 'PatientSupportAngle'),
    0x300A0123: ('CS', '1', "Patient Support Rotation Direction", '', 'PatientSupportRotationDirection'),
    0x300A0124: ('DS', '1', "Table Top Eccentric Axis Distance", '', 'TableTopEccentricAxisDistance'),
    0x300A0125: ('DS', '1', "Table Top Eccentric Angle", '', 'TableTopEccentricAngle'),
    0x300A0126: ('CS', '1', "Table Top Eccentric Rotation Direction", '', 'TableTopEccentricRotationDirection'),
    0x300A0128: ('DS', '1', "Table Top Vertical Position", '', 'TableTopVerticalPosition'),
    0x300A0129: ('DS', '1', "Table Top Longitudinal Position", '', 'TableTopLongitudinalPosition'),
    0x300A012A: ('DS', '1', "Table Top Lateral Position", '', 'TableTopLateralPosition'),
    0x300A012C: ('DS', '3', "Isocenter Position", '', 'IsocenterPosition'),
    0x300A012E: ('DS', '3', "Surface Entry Point", '', 'SurfaceEntryPoint'),
    0x300A0130: ('DS', '1', "Source to Surface Distance", '', 'SourceToSurfaceDistance'),
    0x300A0131: ('FL', '1', "Average Beam Dose Point Source to External Contour Distance", '', 'AverageBeamDosePointSourceToExternalContourDistance'),
    0x300A0132: ('FL', '1', "Source to External Contour Distance", '', 'SourceToExternalContourDistance'),
    0x300A0133: ('FL', '3', "External Contour Entry Point", '', 'ExternalContourEntryPoint'),
    0x300A0134: ('DS', '1', "Cumulative Meterset Weight", '', 'CumulativeMetersetWeight'),
    0x300A0135: ('OB', '1', "Retired-blank", 'Retired', ''),
    0x300A0140: ('FL', '1', "Table Top Pitch Angle", '', 'TableTopPitchAngle'),
    0x300A0142: ('CS', '1', "Table Top Pitch Rotation Direction", '', 'TableTopPitchRotationDirection'),
    0x300A0144: ('FL', '1', "Table Top Roll Angle", '', 'TableTopRollAngle'),
    0x300A0146: ('CS', '1', "Table Top Roll Rotation Direction", '', 'TableTopRollRotationDirection'),
    0x300A0148: ('FL', '1', "Head Fixation Angle", '', 'HeadFixationAngle'),
    0x300A014A: ('FL', '1', "Gantry Pitch Angle", '', 'GantryPitchAngle'),
    0x300A014C: ('CS', '1', "Gantry Pitch Rotation Direction", '', 'GantryPitchRotationDirection'),
    0x300A014E: ('FL', '1', "Gantry Pitch Angle Tolerance", '', 'GantryPitchAngleTolerance'),
    0x300A0150: ('CS', '1', "Fixation Eye", '', 'FixationEye'),
    0x300A0151: ('DS', '1', "Chair Head Frame Position", '', 'ChairHeadFramePosition'),
    0x300A0152: ('DS', '1', "Head Fixation Angle Tolerance", '', 'HeadFixationAngleTolerance'),
    0x300A0153: ('DS', '1', "Chair Head Frame Position Tolerance", '', 'ChairHeadFramePositionTolerance'),
    0x300A0154: ('DS', '1', "Fixation Light Azimuthal Angle Tolerance", '', 'FixationLightAzimuthalAngleTolerance'),
    0x300A0155: ('DS', '1', "Fixation Light Polar Angle Tolerance", '', 'FixationLightPolarAngleTolerance'),
    0x300A0180: ('SQ', '1', "Patient Setup Sequence", '', 'PatientSetupSequence'),
    0x300A0182: ('IS', '1', "Patient Setup Number", '', 'PatientSetupNumber'),
    0x300A0183: ('LO', '1', "Patient Setup Label", '', 'PatientSetupLabel'),
    0x300A0184: ('LO', '1', "Patient Additional Position", '', 'PatientAdditionalPosition'),
    0x300A0190: ('SQ', '1', "Fixation Device Sequence", '', 'FixationDeviceSequence'),
    0x300A0192: ('CS', '1', "Fixation Device Type", '', 'FixationDeviceType'),
    0x300A0194: ('SH', '1', "Fixation Device Label", '', 'FixationDeviceLabel'),
    0x300A0196: ('ST', '1', "Fixation Device Description", '', 'FixationDeviceDescription'),
    0x300A0198: ('SH', '1', "Fixation Device Position", '', 'FixationDevicePosition'),
    0x300A0199: ('FL', '1', "Fixation Device Pitch Angle", '', 'FixationDevicePitchAngle'),
    0x300A019A: ('FL', '1', "Fixation Device Roll Angle", '', 'FixationDeviceRollAngle'),
    0x300A01A0: ('SQ', '1', "Shielding Device Sequence", '', 'ShieldingDeviceSequence'),
    0x300A01A2: ('CS', '1', "Shielding Device Type", '', 'ShieldingDeviceType'),
    0x300A01A4: ('SH', '1', "Shielding Device Label", '', 'ShieldingDeviceLabel'),
    0x300A01A6: ('ST', '1', "Shielding Device Description", '', 'ShieldingDeviceDescription'),
    0x300A01A8: ('SH', '1', "Shielding Device Position", '', 'ShieldingDevicePosition'),
    0x300A01B0: ('CS', '1', "Setup Technique", '', 'SetupTechnique'),
    0x300A01B2: ('ST', '1', "Setup Technique Description", '', 'SetupTechniqueDescription'),
    0x300A01B4: ('SQ', '1', "Setup Device Sequence", '', 'SetupDeviceSequence'),
    0x300A01B6: ('CS', '1', "Setup Device Type", '', 'SetupDeviceType'),
    0x300A01B8: ('SH', '1', "Setup Device Label", '', 'SetupDeviceLabel'),
    0x300A01BA: ('ST', '1', "Setup Device Description", '', 'SetupDeviceDescription'),
    0x300A01BC: ('DS', '1', "Setup Device Parameter", '', 'SetupDeviceParameter'),
    0x300A01D0: ('ST', '1', "Setup Reference Description", '', 'SetupReferenceDescription'),
    0x300A01D2: ('DS', '1', "Table Top Vertical Setup Displacement", '', 'TableTopVerticalSetupDisplacement'),
    0x300A01D4: ('DS', '1', "Table Top Longitudinal Setup Displacement", '', 'TableTopLongitudinalSetupDisplacement'),
    0x300A01D6: ('DS', '1', "Table Top Lateral Setup Displacement", '', 'TableTopLateralSetupDisplacement'),
    0x300A0200: ('CS', '1', "Brachy Treatment Technique", '', 'BrachyTreatmentTechnique'),
    0x300A0202: ('CS', '1', "Brachy Treatment Type", '', 'BrachyTreatmentType'),
    0x300A0206: ('SQ', '1', "Treatment Machine Sequence", '', 'TreatmentMachineSequence'),
    0x300A0210: ('SQ', '1', "Source Sequence", '', 'SourceSequence'),
    0x300A0212: ('IS', '1', "Source Number", '', 'SourceNumber'),
    0x300A0214: ('CS', '1', "Source Type", '', 'SourceType'),
    0x300A0216: ('LO', '1', "Source Manufacturer", '', 'SourceManufacturer'),
    0x300A0218: ('DS', '1', "Active Source Diameter", '', 'ActiveSourceDiameter'),
    0x300A021A: ('DS', '1', "Active Source Length", '', 'ActiveSourceLength'),
    0x300A021B: ('SH', '1', "Source Model ID", '', 'SourceModelID'),
    0x300A021C: ('LO', '1', "Source Description", '', 'SourceDescription'),
    0x300A0222: ('DS', '1', "Source Encapsulation Nominal Thickness", '', 'SourceEncapsulationNominalThickness'),
    0x300A0224: ('DS', '1', "Source Encapsulation Nominal Transmission", '', 'SourceEncapsulationNominalTransmission'),
    0x300A0226: ('LO', '1', "Source Isotope Name", '', 'SourceIsotopeName'),
    0x300A0228: ('DS', '1', "Source Isotope Half Life", '', 'SourceIsotopeHalfLife'),
    0x300A0229: ('CS', '1', "Source Strength Units", '', 'SourceStrengthUnits'),
    0x300A022A: ('DS', '1', "Reference Air Kerma Rate", '', 'ReferenceAirKermaRate'),
    0x300A022B: ('DS', '1', "Source Strength", '', 'SourceStrength'),
    0x300A022C: ('DA', '1', "Source Strength Reference Date", '', 'SourceStrengthReferenceDate'),
    0x300A022E: ('TM', '1', "Source Strength Reference Time", '', 'SourceStrengthReferenceTime'),
    0x300A0230: ('SQ', '1', "Application Setup Sequence", '', 'ApplicationSetupSequence'),
    0x300A0232: ('CS', '1', "Application Setup Type", '', 'ApplicationSetupType'),
    0x300A0234: ('IS', '1', "Application Setup Number", '', 'ApplicationSetupNumber'),
    0x300A0236: ('LO', '1', "Application Setup Name", '', 'ApplicationSetupName'),
    0x300A0238: ('LO', '1', "Application Setup Manufacturer", '', 'ApplicationSetupManufacturer'),
    0x300A0240: ('IS', '1', "Template Number", '', 'TemplateNumber'),
    0x300A0242: ('SH', '1', "Template Type", '', 'TemplateType'),
    0x300A0244: ('LO', '1', "Template Name", '', 'TemplateName'),
    0x300A0250: ('DS', '1', "Total Reference Air Kerma", '', 'TotalReferenceAirKerma'),
    0x300A0260: ('SQ', '1', "Brachy Accessory Device Sequence", '', 'BrachyAccessoryDeviceSequence'),
    0x300A0262: ('IS', '1', "Brachy Accessory Device Number", '', 'BrachyAccessoryDeviceNumber'),
    0x300A0263: ('SH', '1', "Brachy Accessory Device ID", '', 'BrachyAccessoryDeviceID'),
    0x300A0264: ('CS', '1', "Brachy Accessory Device Type", '', 'BrachyAccessoryDeviceType'),
    0x300A0266: ('LO', '1', "Brachy Accessory Device Name", '', 'BrachyAccessoryDeviceName'),
    0x300A026A: ('DS', '1', "Brachy Accessory Device Nominal Thickness", '', 'BrachyAccessoryDeviceNominalThickness'),
    0x300A026C: ('DS', '1', "Brachy Accessory Device Nominal Transmission", '', 'BrachyAccessoryDeviceNominalTransmission'),
    0x300A0271: ('DS', '1', "Channel Effective Length", '', 'ChannelEffectiveLength'),
    0x300A0272: ('DS', '1', "Channel Inner Length", '', 'ChannelInnerLength'),
    0x300A0273: ('SH', '1', "Afterloader Channel ID", '', 'AfterloaderChannelID'),
    0x300A0274: ('DS', '1', "Source Applicator Tip Length", '', 'SourceApplicatorTipLength'),
    0x300A0280: ('SQ', '1', "Channel Sequence", '', 'ChannelSequence'),
    0x300A0282: ('IS', '1', "Channel Number", '', 'ChannelNumber'),
    0x300A0284: ('DS', '1', "Channel Length", '', 'ChannelLength'),
    0x300A0286: ('DS', '1', "Channel Total Time", '', 'ChannelTotalTime'),
    0x300A0288: ('CS', '1', "Source Movement Type", '', 'SourceMovementType'),
    0x300A028A: ('IS', '1', "Number of Pulses", '', 'NumberOfPulses'),
    0x300A028C: ('DS', '1', "Pulse Repetition Interval", '', 'PulseRepetitionInterval'),
    0x300A0290: ('IS', '1', "Source Applicator Number", '', 'SourceApplicatorNumber'),
    0x300A0291: ('SH', '1', "Source Applicator ID", '', 'SourceApplicatorID'),
    0x300A0292: ('CS', '1', "Source Applicator Type", '', 'SourceApplicatorType'),
    0x300A0294: ('LO', '1', "Source Applicator Name", '', 'SourceApplicatorName'),
    0x300A0296: ('DS', '1', "Source Applicator Length", '', 'SourceApplicatorLength'),
    0x300A0298: ('LO', '1', "Source Applicator Manufacturer", '', 'SourceApplicatorManufacturer'),
    0x300A029C: ('DS', '1', "Source Applicator Wall Nominal Thickness", '', 'SourceApplicatorWallNominalThickness'),
    0x300A029E: ('DS', '1', "Source Applicator Wall Nominal Transmission", '', 'SourceApplicatorWallNominalTransmission'),
    0x300A02A0: ('DS', '1', "Source Applicator Step Size", '', 'SourceApplicatorStepSize'),
    0x300A02A1: ('IS', '1', "Applicator Shape Referenced ROI Number", '', 'ApplicatorShapeReferencedROINumber'),
    0x300A02A2: ('IS', '1', "Transfer Tube Number", '', 'TransferTubeNumber'),
    0x300A02A4: ('DS', '1', "Transfer Tube Length", '', 'TransferTubeLength'),
    0x300A02B0: ('SQ', '1', "Channel Shield Sequence", '', 'ChannelShieldSequence'),
    0x300A02B2: ('IS', '1', "Channel Shield Number", '', 'ChannelShieldNumber'),
    0x300A02B3: ('SH', '1', "Channel Shield ID", '', 'ChannelShieldID'),
    0x300A02B4: ('LO', '1', "Channel Shield Name", '', 'ChannelShieldName'),
    0x300A02B8: ('DS', '1', "Channel Shield Nominal Thickness", '', 'ChannelShieldNominalThickness'),
    0x300A02BA: ('DS', '1', "Channel Shield Nominal Transmission", '', 'ChannelShieldNominalTransmission'),
    0x300A02C8: ('DS', '1', "Final Cumulative Time Weight", '', 'FinalCumulativeTimeWeight'),
    0x300A02D0: ('SQ', '1', "Brachy Control Point Sequence", '', 'BrachyControlPointSequence'),
    0x300A02D2: ('DS', '1', "Control Point Relative Position", '', 'ControlPointRelativePosition'),
    0x300A02D4: ('DS', '3', "Control Point 3D Position", '', 'ControlPoint3DPosition'),
    0x300A02D6: ('DS', '1', "Cumulative Time Weight", '', 'CumulativeTimeWeight'),
    0x300A02E0: ('CS', '1', "Compensator Divergence", '', 'CompensatorDivergence'),
    0x300A02E1: ('CS', '1', "Compensator Mounting Position", '', 'CompensatorMountingPosition'),
    0x300A02E2: ('DS', '1-n', "Source to Compensator Distance", '', 'SourceToCompensatorDistance'),
    0x300A02E3: ('FL', '1', "Total Compensator Tray Water-Equivalent Thickness", '', 'TotalCompensatorTrayWaterEquivalentThickness'),
    0x300A02E4: ('FL', '1', "Isocenter to Compensator Tray Distance", '', 'IsocenterToCompensatorTrayDistance'),
    0x300A02E5: ('FL', '1', "Compensator Column Offset", '', 'CompensatorColumnOffset'),
    0x300A02E6: ('FL', '1-n', "Isocenter to Compensator Distances", '', 'IsocenterToCompensatorDistances'),
    0x300A02E7: ('FL', '1', "Compensator Relative Stopping Power Ratio", '', 'CompensatorRelativeStoppingPowerRatio'),
    0x300A02E8: ('FL', '1', "Compensator Milling Tool Diameter", '', 'CompensatorMillingToolDiameter'),
    0x300A02EA: ('SQ', '1', "Ion Range Compensator Sequence", '', 'IonRangeCompensatorSequence'),
    0x300A02EB: ('LT', '1', "Compensator Description", '', 'CompensatorDescription'),
    0x300A0302: ('IS', '1', "Radiation Mass Number", '', 'RadiationMassNumber'),
    0x300A0304: ('IS', '1', "Radiation Atomic Number", '', 'RadiationAtomicNumber'),
    0x300A0306: ('SS', '1', "Radiation Charge State", '', 'RadiationChargeState'),
    0x300A0308: ('CS', '1', "Scan Mode", '', 'ScanMode'),
    0x300A0309: ('CS', '1', "Modulated Scan Mode Type", '', 'ModulatedScanModeType'),
    0x300A030A: ('FL', '2', "Virtual Source-Axis Distances", '', 'VirtualSourceAxisDistances'),
    0x300A030C: ('SQ', '1', "Snout Sequence", '', 'SnoutSequence'),
    0x300A030D: ('FL', '1', "Snout Position", '', 'SnoutPosition'),
    0x300A030F: ('SH', '1', "Snout ID", '', 'SnoutID'),
    0x300A0312: ('IS', '1', "Number of Range Shifters", '', 'NumberOfRangeShifters'),
    0x300A0314: ('SQ', '1', "Range Shifter Sequence", '', 'RangeShifterSequence'),
    0x300A0316: ('IS', '1', "Range Shifter Number", '', 'RangeShifterNumber'),
    0x300A0318: ('SH', '1', "Range Shifter ID", '', 'RangeShifterID'),
    0x300A0320: ('CS', '1', "Range Shifter Type", '', 'RangeShifterType'),
    0x300A0322: ('LO', '1', "Range Shifter Description", '', 'RangeShifterDescription'),
    0x300A0330: ('IS', '1', "Number of Lateral Spreading Devices", '', 'NumberOfLateralSpreadingDevices'),
    0x300A0332: ('SQ', '1', "Lateral Spreading Device Sequence", '', 'LateralSpreadingDeviceSequence'),
    0x300A0334: ('IS', '1', "Lateral Spreading Device Number", '', 'LateralSpreadingDeviceNumber'),
    0x300A0336: ('SH', '1', "Lateral Spreading Device ID", '', 'LateralSpreadingDeviceID'),
    0x300A0338: ('CS', '1', "Lateral Spreading Device Type", '', 'LateralSpreadingDeviceType'),
    0x300A033A: ('LO', '1', "Lateral Spreading Device Description", '', 'LateralSpreadingDeviceDescription'),
    0x300A033C: ('FL', '1', "Lateral Spreading Device Water Equivalent Thickness", '', 'LateralSpreadingDeviceWaterEquivalentThickness'),
    0x300A0340: ('IS', '1', "Number of Range Modulators", '', 'NumberOfRangeModulators'),
    0x300A0342: ('SQ', '1', "Range Modulator Sequence", '', 'RangeModulatorSequence'),
    0x300A0344: ('IS', '1', "Range Modulator Number", '', 'RangeModulatorNumber'),
    0x300A0346: ('SH', '1', "Range Modulator ID", '', 'RangeModulatorID'),
    0x300A0348: ('CS', '1', "Range Modulator Type", '', 'RangeModulatorType'),
    0x300A034A: ('LO', '1', "Range Modulator Description", '', 'RangeModulatorDescription'),
    0x300A034C: ('SH', '1', "Beam Current Modulation ID", '', 'BeamCurrentModulationID'),
    0x300A0350: ('CS', '1', "Patient Support Type", '', 'PatientSupportType'),
    0x300A0352: ('SH', '1', "Patient Support ID", '', 'PatientSupportID'),
    0x300A0354: ('LO', '1', "Patient Support Accessory Code", '', 'PatientSupportAccessoryCode'),
    0x300A0355: ('LO', '1', "Tray Accessory Code", '', 'TrayAccessoryCode'),
    0x300A0356: ('FL', '1', "Fixation Light Azimuthal Angle", '', 'FixationLightAzimuthalAngle'),
    0x300A0358: ('FL', '1', "Fixation Light Polar Angle", '', 'FixationLightPolarAngle'),
    0x300A035A: ('FL', '1', "Meterset Rate", '', 'MetersetRate'),
    0x300A0360: ('SQ', '1', "Range Shifter Settings Sequence", '', 'RangeShifterSettingsSequence'),
    0x300A0362: ('LO', '1', "Range Shifter Setting", '', 'RangeShifterSetting'),
    0x300A0364: ('FL', '1', "Isocenter to Range Shifter Distance", '', 'IsocenterToRangeShifterDistance'),
    0x300A0366: ('FL', '1', "Range Shifter Water Equivalent Thickness", '', 'RangeShifterWaterEquivalentThickness'),
    0x300A0370: ('SQ', '1', "Lateral Spreading Device Settings Sequence", '', 'LateralSpreadingDeviceSettingsSequence'),
    0x300A0372: ('LO', '1', "Lateral Spreading Device Setting", '', 'LateralSpreadingDeviceSetting'),
    0x300A0374: ('FL', '1', "Isocenter to Lateral Spreading Device Distance", '', 'IsocenterToLateralSpreadingDeviceDistance'),
    0x300A0380: ('SQ', '1', "Range Modulator Settings Sequence", '', 'RangeModulatorSettingsSequence'),
    0x300A0382: ('FL', '1', "Range Modulator Gating Start Value", '', 'RangeModulatorGatingStartValue'),
    0x300A0384: ('FL', '1', "Range Modulator Gating Stop Value", '', 'RangeModulatorGatingStopValue'),
    0x300A0386: ('FL', '1', "Range Modulator Gating Start Water Equivalent Thickness", '', 'RangeModulatorGatingStartWaterEquivalentThickness'),
    0x300A0388: ('FL', '1', "Range Modulator Gating Stop Water Equivalent Thickness", '', 'RangeModulatorGatingStopWaterEquivalentThickness'),
    0x300A038A: ('FL', '1', "Isocenter to Range Modulator Distance", '', 'IsocenterToRangeModulatorDistance'),
    0x300A038F: ('FL', '1-n', "Scan Spot Time Offset", '', 'ScanSpotTimeOffset'),
    0x300A0390: ('SH', '1', "Scan Spot Tune ID", '', 'ScanSpotTuneID'),
    0x300A0391: ('IS', '1-n', "Scan Spot Prescribed Indices", '', 'ScanSpotPrescribedIndices'),
    0x300A0392: ('IS', '1', "Number of Scan Spot Positions", '', 'NumberOfScanSpotPositions'),
    0x300A0393: ('CS', '1', "Scan Spot Reordered", '', 'ScanSpotReordered'),
    0x300A0394: ('FL', '1-n', "Scan Spot Position Map", '', 'ScanSpotPositionMap'),
    0x300A0395: ('CS', '1', "Scan Spot Reordering Allowed", '', 'ScanSpotReorderingAllowed'),
    0x300A0396: ('FL', '1-n', "Scan Spot Meterset Weights", '', 'ScanSpotMetersetWeights'),
    0x300A0398: ('FL', '2', "Scanning Spot Size", '', 'ScanningSpotSize'),
    0x300A0399: ('FL', '2-2n', "Scan Spot Sizes Delivered", '', 'ScanSpotSizesDelivered'),
    0x300A039A: ('IS', '1', "Number of Paintings", '', 'NumberOfPaintings'),
    0x300A03A0: ('SQ', '1', "Ion Tolerance Table Sequence", '', 'IonToleranceTableSequence'),
    0x300A03A2: ('SQ', '1', "Ion Beam Sequence", '', 'IonBeamSequence'),
    0x300A03A4: ('SQ', '1', "Ion Beam Limiting Device Sequence", '', 'IonBeamLimitingDeviceSequence'),
    0x300A03A6: ('SQ', '1', "Ion Block Sequence", '', 'IonBlockSequence'),
    0x300A03A8: ('SQ', '1', "Ion Control Point Sequence", '', 'IonControlPointSequence'),
    0x300A03AA: ('SQ', '1', "Ion Wedge Sequence", '', 'IonWedgeSequence'),
    0x300A03AC: ('SQ', '1', "Ion Wedge Position Sequence", '', 'IonWedgePositionSequence'),
    0x300A0401: ('SQ', '1', "Referenced Setup Image Sequence", '', 'ReferencedSetupImageSequence'),
    0x300A0402: ('ST', '1', "Setup Image Comment", '', 'SetupImageComment'),
    0x300A0410: ('SQ', '1', "Motion Synchronization Sequence", '', 'MotionSynchronizationSequence'),
    0x300A0412: ('FL', '3', "Control Point Orientation", '', 'ControlPointOrientation'),
    0x300A0420: ('SQ', '1', "General Accessory Sequence", '', 'GeneralAccessorySequence'),
    0x300A0421: ('SH', '1', "General Accessory ID", '', 'GeneralAccessoryID'),
    0x300A0422: ('ST', '1', "General Accessory Description", '', 'GeneralAccessoryDescription'),
    0x300A0423: ('CS', '1', "General Accessory Type", '', 'GeneralAccessoryType'),
    0x300A0424: ('IS', '1', "General Accessory Number", '', 'GeneralAccessoryNumber'),
    0x300A0425: ('FL', '1', "Source to General Accessory Distance", '', 'SourceToGeneralAccessoryDistance'),
    0x300A0426: ('DS', '1', "Isocenter to General Accessory Distance", '', 'IsocenterToGeneralAccessoryDistance'),
    0x300A0431: ('SQ', '1', "Applicator Geometry Sequence", '', 'ApplicatorGeometrySequence'),
    0x300A0432: ('CS', '1', "Applicator Aperture Shape", '', 'ApplicatorApertureShape'),
    0x300A0433: ('FL', '1', "Applicator Opening", '', 'ApplicatorOpening'),
    0x300A0434: ('FL', '1', "Applicator Opening X", '', 'ApplicatorOpeningX'),
    0x300A0435: ('FL', '1', "Applicator Opening Y", '', 'ApplicatorOpeningY'),
    0x300A0436: ('FL', '1', "Source to Applicator Mounting Position Distance", '', 'SourceToApplicatorMountingPositionDistance'),
    0x300A0440: ('IS', '1', "Number of Block Slab Items", '', 'NumberOfBlockSlabItems'),
    0x300A0441: ('SQ', '1', "Block Slab Sequence", '', 'BlockSlabSequence'),
    0x300A0442: ('DS', '1', "Block Slab Thickness", '', 'BlockSlabThickness'),
    0x300A0443: ('US', '1', "Block Slab Number", '', 'BlockSlabNumber'),
    0x300A0450: ('SQ', '1', "Device Motion Control Sequence", '', 'DeviceMotionControlSequence'),
    0x300A0451: ('CS', '1', "Device Motion Execution Mode", '', 'DeviceMotionExecutionMode'),
    0x300A0452: ('CS', '1', "Device Motion Observation Mode", '', 'DeviceMotionObservationMode'),
    0x300A0453: ('SQ', '1', "Device Motion Parameter Code Sequence", '', 'DeviceMotionParameterCodeSequence'),
    0x300A0501: ('FL', '1', "Distal Depth Fraction", '', 'DistalDepthFraction'),
    0x300A0502: ('FL', '1', "Distal Depth", '', 'DistalDepth'),
    0x300A0503: ('FL', '2', "Nominal Range Modulation Fractions", '', 'NominalRangeModulationFractions'),
    0x300A0504: ('FL', '2', "Nominal Range Modulated Region Depths", '', 'NominalRangeModulatedRegionDepths'),
    0x300A0505: ('SQ', '1', "Depth Dose Parameters Sequence", '', 'DepthDoseParametersSequence'),
    0x300A0506: ('SQ', '1', "Delivered Depth Dose Parameters Sequence", '', 'DeliveredDepthDoseParametersSequence'),
    0x300A0507: ('FL', '1', "Delivered Distal Depth Fraction", '', 'DeliveredDistalDepthFraction'),
    0x300A0508: ('FL', '1', "Delivered Distal Depth", '', 'DeliveredDistalDepth'),
    0x300A0509: ('FL', '2', "Delivered Nominal Range Modulation Fractions", '', 'DeliveredNominalRangeModulationFractions'),
    0x300A0510: ('FL', '2', "Delivered Nominal Range Modulated Region Depths", '', 'DeliveredNominalRangeModulatedRegionDepths'),
    0x300A0511: ('CS', '1', "Delivered Reference Dose Definition", '', 'DeliveredReferenceDoseDefinition'),
    0x300A0512: ('CS', '1', "Reference Dose Definition", '', 'ReferenceDoseDefinition'),
    0x300A0600: ('US', '1', "RT Control Point Index", '', 'RTControlPointIndex'),
    0x300A0601: ('US', '1', "Radiation Generation Mode Index", '', 'RadiationGenerationModeIndex'),
    0x300A0602: ('US', '1', "Referenced Defined Device Index", '', 'ReferencedDefinedDeviceIndex'),
    0x300A0603: ('US', '1', "Radiation Dose Identification Index", '', 'RadiationDoseIdentificationIndex'),
    0x300A0604: ('US', '1', "Number of RT Control Points", '', 'NumberOfRTControlPoints'),
    0x300A0605: ('US', '1', "Referenced Radiation Generation Mode Index", '', 'ReferencedRadiationGenerationModeIndex'),
    0x300A0606: ('US', '1', "Treatment Position Index", '', 'TreatmentPositionIndex'),
    0x300A0607: ('US', '1', "Referenced Device Index", '', 'ReferencedDeviceIndex'),
    0x300A0608: ('LO', '1', "Treatment Position Group Label", '', 'TreatmentPositionGroupLabel'),
    0x300A0609: ('UI', '1', "Treatment Position Group UID", '', 'TreatmentPositionGroupUID'),
    0x300A060A: ('SQ', '1', "Treatment Position Group Sequence", '', 'TreatmentPositionGroupSequence'),
    0x300A060B: ('US', '1', "Referenced Treatment Position Index", '', 'ReferencedTreatmentPositionIndex'),
    0x300A060C: ('US', '1', "Referenced Radiation Dose Identification Index", '', 'ReferencedRadiationDoseIdentificationIndex'),
    0x300A060D: ('FD', '1', "RT Accessory Holder Water-Equivalent Thickness", '', 'RTAccessoryHolderWaterEquivalentThickness'),
    0x300A060E: ('US', '1', "Referenced RT Accessory Holder Device Index", '', 'ReferencedRTAccessoryHolderDeviceIndex'),
    0x300A060F: ('CS', '1', "RT Accessory Holder Slot Existence Flag", '', 'RTAccessoryHolderSlotExistenceFlag'),
    0x300A0610: ('SQ', '1', "RT Accessory Holder Slot Sequence", '', 'RTAccessoryHolderSlotSequence'),
    0x300A0611: ('LO', '1', "RT Accessory Holder Slot ID", '', 'RTAccessoryHolderSlotID'),
    0x300A0612: ('FD', '1', "RT Accessory Holder Slot Distance", '', 'RTAccessoryHolderSlotDistance'),
    0x300A0613: ('FD', '1', "RT Accessory Slot Distance", '', 'RTAccessorySlotDistance'),
    0x300A0614: ('SQ', '1', "RT Accessory Holder Definition Sequence", '', 'RTAccessoryHolderDefinitionSequence'),
    0x300A0615: ('LO', '1', "RT Accessory Device Slot ID", '', 'RTAccessoryDeviceSlotID'),
    0x300A0616: ('SQ', '1', "RT Radiation Sequence", '', 'RTRadiationSequence'),
    0x300A0617: ('SQ', '1', "Radiation Dose Sequence", '', 'RadiationDoseSequence'),
    0x300A0618: ('SQ', '1', "Radiation Dose Identification Sequence", '', 'RadiationDoseIdentificationSequence'),
    0x300A0619: ('LO', '1', "Radiation Dose Identification Label", '', 'RadiationDoseIdentificationLabel'),
    0x300A061A: ('CS', '1', "Reference Dose Type", '', 'ReferenceDoseType'),
    0x300A061B: ('CS', '1', "Primary Dose Value Indicator", '', 'PrimaryDoseValueIndicator'),
    0x300A061C: ('SQ', '1', "Dose Values Sequence", '', 'DoseValuesSequence'),
    0x300A061D: ('CS', '1-n', "Dose Value Purpose", '', 'DoseValuePurpose'),
    0x300A061E: ('FD', '3', "Reference Dose Point Coordinates", '', 'ReferenceDosePointCoordinates'),
    0x300A061F: ('SQ', '1', "Radiation Dose Values Parameters Sequence", '', 'RadiationDoseValuesParametersSequence'),
    0x300A0620: ('SQ', '1', "Meterset to Dose Mapping Sequence", '', 'MetersetToDoseMappingSequence'),
    0x300A0621: ('SQ', '1', "Expected In-Vivo Measurement Values Sequence", '', 'ExpectedInVivoMeasurementValuesSequence'),
    0x300A0622: ('US', '1', "Expected In-Vivo Measurement Value Index", '', 'ExpectedInVivoMeasurementValueIndex'),
    0x300A0623: ('LO', '1', "Radiation Dose In-Vivo Measurement Label", '', 'RadiationDoseInVivoMeasurementLabel'),
    0x300A0624: ('FD', '2', "Radiation Dose Central Axis Displacement", '', 'RadiationDoseCentralAxisDisplacement'),
    0x300A0625: ('FD', '1', "Radiation Dose Value", '', 'RadiationDoseValue'),
    0x300A0626: ('FD', '1', "Radiation Dose Source to Skin Distance", '', 'RadiationDoseSourceToSkinDistance'),
    0x300A0627: ('FD', '3', "Radiation Dose Measurement Point Coordinates", '', 'RadiationDoseMeasurementPointCoordinates'),
    0x300A0628: ('FD', '1', "Radiation Dose Source to External Contour Distance", '', 'RadiationDoseSourceToExternalContourDistance'),
    0x300A0629: ('SQ', '1', "RT Tolerance Set Sequence", '', 'RTToleranceSetSequence'),
    0x300A062A: ('LO', '1', "RT Tolerance Set Label", '', 'RTToleranceSetLabel'),
    0x300A062B: ('SQ', '1', "Attribute Tolerance Values Sequence", '', 'AttributeToleranceValuesSequence'),
    0x300A062C: ('FD', '1', "Tolerance Value", '', 'ToleranceValue'),
    0x300A062D: ('SQ', '1', "Patient Support Position Tolerance Sequence", '', 'PatientSupportPositionToleranceSequence'),
    0x300A062E: ('FD', '1', "Treatment Time Limit", '', 'TreatmentTimeLimit'),
    0x300A062F: ('SQ', '1', "C-Arm Photon-Electron Control Point Sequence", '', 'CArmPhotonElectronControlPointSequence'),
    0x300A0630: ('SQ', '1', "Referenced RT Radiation Sequence", '', 'ReferencedRTRadiationSequence'),
    0x300A0631: ('SQ', '1', "Referenced RT Instance Sequence", '', 'ReferencedRTInstanceSequence'),
    0x300A0632: ('SQ', '1', "Referenced RT Patient Setup Sequence", 'Retired', 'ReferencedRTPatientSetupSequence'),
    0x300A0634: ('FD', '1', "Source to Patient Surface Distance", '', 'SourceToPatientSurfaceDistance'),
    0x300A0635: ('SQ', '1', "Treatment Machine Special Mode Code Sequence", '', 'TreatmentMachineSpecialModeCodeSequence'),
    0x300A0636: ('US', '1', "Intended Number of Fractions", '', 'IntendedNumberOfFractions'),
    0x300A0637: ('CS', '1', "RT Radiation Set Intent", '', 'RTRadiationSetIntent'),
    0x300A0638: ('CS', '1', "RT Radiation Physical and Geometric Content Detail Flag", '', 'RTRadiationPhysicalAndGeometricContentDetailFlag'),
    0x300A0639: ('CS', '1', "RT Record Flag", '', 'RTRecordFlag'),
    0x300A063A: ('SQ', '1', "Treatment Device Identification Sequence", '', 'TreatmentDeviceIdentificationSequence'),
    0x300A063B: ('SQ', '1', "Referenced RT Physician Intent Sequence", '', 'ReferencedRTPhysicianIntentSequence'),
    0x300A063C: ('FD', '1', "Cumulative Meterset", '', 'CumulativeMeterset'),
    0x300A063D: ('FD', '1', "Delivery Rate", '', 'DeliveryRate'),
    0x300A063E: ('SQ', '1', "Delivery Rate Unit Sequence", '', 'DeliveryRateUnitSequence'),
    0x300A063F: ('SQ', '1', "Treatment Position Sequence", '', 'TreatmentPositionSequence'),
    0x300A0640: ('FD', '1', "Radiation Source-Axis Distance", '', 'RadiationSourceAxisDistance'),
    0x300A0641: ('US', '1', "Number of RT Beam Limiting Devices", '', 'NumberOfRTBeamLimitingDevices'),
    0x300A0642: ('FD', '1', "RT Beam Limiting Device Proximal Distance", '', 'RTBeamLimitingDeviceProximalDistance'),
    0x300A0643: ('FD', '1', "RT Beam Limiting Device Distal Distance", '', 'RTBeamLimitingDeviceDistalDistance'),
    0x300A0644: ('SQ', '1', "Parallel RT Beam Delimiter Device Orientation Label Code Sequence", '', 'ParallelRTBeamDelimiterDeviceOrientationLabelCodeSequence'),
    0x300A0645: ('FD', '1', "Beam Modifier Orientation Angle", '', 'BeamModifierOrientationAngle'),
    0x300A0646: ('SQ', '1', "Fixed RT Beam Delimiter Device Sequence", '', 'FixedRTBeamDelimiterDeviceSequence'),
    0x300A0647: ('SQ', '1', "Parallel RT Beam Delimiter Device Sequence", '', 'ParallelRTBeamDelimiterDeviceSequence'),
    0x300A0648: ('US', '1', "Number of Parallel RT Beam Delimiters", '', 'NumberOfParallelRTBeamDelimiters'),
    0x300A0649: ('FD', '2-n', "Parallel RT Beam Delimiter Boundaries", '', 'ParallelRTBeamDelimiterBoundaries'),
    0x300A064A: ('FD', '2-n', "Parallel RT Beam Delimiter Positions", '', 'ParallelRTBeamDelimiterPositions'),
    0x300A064B: ('FD', '2', "RT Beam Limiting Device Offset", '', 'RTBeamLimitingDeviceOffset'),
    0x300A064C: ('SQ', '1', "RT Beam Delimiter Geometry Sequence", '', 'RTBeamDelimiterGeometrySequence'),
    0x300A064D: ('SQ', '1', "RT Beam Limiting Device Definition Sequence", '', 'RTBeamLimitingDeviceDefinitionSequence'),
    0x300A064E: ('CS', '1', "Parallel RT Beam Delimiter Opening Mode", '', 'ParallelRTBeamDelimiterOpeningMode'),
    0x300A064F: ('CS', '1-n', "Parallel RT Beam Delimiter Leaf Mounting Side", '', 'ParallelRTBeamDelimiterLeafMountingSide'),
    0x300A0650: ('UI', '1', "Patient Setup UID", 'Retired', 'PatientSetupUID'),
    0x300A0651: ('SQ', '1', "Wedge Definition Sequence", '', 'WedgeDefinitionSequence'),
    0x300A0652: ('FD', '1', "Radiation Beam Wedge Angle", '', 'RadiationBeamWedgeAngle'),
    0x300A0653: ('FD', '1', "Radiation Beam Wedge Thin Edge Distance", '', 'RadiationBeamWedgeThinEdgeDistance'),
    0x300A0654: ('FD', '1', "Radiation Beam Effective Wedge Angle", '', 'RadiationBeamEffectiveWedgeAngle'),
    0x300A0655: ('US', '1', "Number of Wedge Positions", '', 'NumberOfWedgePositions'),
    0x300A0656: ('SQ', '1', "RT Beam Limiting Device Opening Sequence", '', 'RTBeamLimitingDeviceOpeningSequence'),
    0x300A0657: ('US', '1', "Number of RT Beam Limiting Device Openings", '', 'NumberOfRTBeamLimitingDeviceOpenings'),
    0x300A0658: ('SQ', '1', "Radiation Dosimeter Unit Sequence", '', 'RadiationDosimeterUnitSequence'),
    0x300A0659: ('SQ', '1', "RT Device Distance Reference Location Code Sequence", '', 'RTDeviceDistanceReferenceLocationCodeSequence'),
    0x300A065A: ('SQ', '1', "Radiation Device Configuration and Commissioning Key Sequence", '', 'RadiationDeviceConfigurationAndCommissioningKeySequence'),
    0x300A065B: ('SQ', '1', "Patient Support Position Parameter Sequence", '', 'PatientSupportPositionParameterSequence'),
    0x300A065C: ('CS', '1', "Patient Support Position Specification Method", '', 'PatientSupportPositionSpecificationMethod'),
    0x300A065D: ('SQ', '1', "Patient Support Position Device Parameter Sequence", '', 'PatientSupportPositionDeviceParameterSequence'),
    0x300A065E: ('US', '1', "Device Order Index", '', 'DeviceOrderIndex'),
    0x300A065F: ('US', '1', "Patient Support Position Parameter Order Index", '', 'PatientSupportPositionParameterOrderIndex'),
    0x300A0660: ('SQ', '1', "Patient Support Position Device Tolerance Sequence", '', 'PatientSupportPositionDeviceToleranceSequence'),
    0x300A0661: ('US', '1', "Patient Support Position Tolerance Order Index", '', 'PatientSupportPositionToleranceOrderIndex'),
    0x300A0662: ('SQ', '1', "Compensator Definition Sequence", '', 'CompensatorDefinitionSequence'),
    0x300A0663: ('CS', '1', "Compensator Map Orientation", '', 'CompensatorMapOrientation'),
    0x300A0664: ('OF', '1', "Compensator Proximal Thickness Map", '', 'CompensatorProximalThicknessMap'),
    0x300A0665: ('OF', '1', "Compensator Distal Thickness Map", '', 'CompensatorDistalThicknessMap'),
    0x300A0666: ('FD', '1', "Compensator Base Plane Offset", '', 'CompensatorBasePlaneOffset'),
    0x300A0667: ('SQ', '1', "Compensator Shape Fabrication Code Sequence", '', 'CompensatorShapeFabricationCodeSequence'),
    0x300A0668: ('SQ', '1', "Compensator Shape Sequence", '', 'CompensatorShapeSequence'),
    0x300A0669: ('FD', '1', "Radiation Beam Compensator Milling Tool Diameter", '', 'RadiationBeamCompensatorMillingToolDiameter'),
    0x300A066A: ('SQ', '1', "Block Definition Sequence", '', 'BlockDefinitionSequence'),
    0x300A066B: ('OF', '1', "Block Edge Data", '', 'BlockEdgeData'),
    0x300A066C: ('CS', '1', "Block Orientation", '', 'BlockOrientation'),
    0x300A066D: ('FD', '1', "Radiation Beam Block Thickness", '', 'RadiationBeamBlockThickness'),
    0x300A066E: ('FD', '1', "Radiation Beam Block Slab Thickness", '', 'RadiationBeamBlockSlabThickness'),
    0x300A066F: ('SQ', '1', "Block Edge Data Sequence", '', 'BlockEdgeDataSequence'),
    0x300A0670: ('US', '1', "Number of RT Accessory Holders", '', 'NumberOfRTAccessoryHolders'),
    0x300A0671: ('SQ', '1', "General Accessory Definition Sequence", '', 'GeneralAccessoryDefinitionSequence'),
    0x300A0672: ('US', '1', "Number of General Accessories", '', 'NumberOfGeneralAccessories'),
    0x300A0673: ('SQ', '1', "Bolus Definition Sequence", '', 'BolusDefinitionSequence'),
    0x300A0674: ('US', '1', "Number of Boluses", '', 'NumberOfBoluses'),
    0x300A0675: ('UI', '1', "Equipment Frame of Reference UID", '', 'EquipmentFrameOfReferenceUID'),
    0x300A0676: ('ST', '1', "Equipment Frame of Reference Description", '', 'EquipmentFrameOfReferenceDescription'),
    0x300A0677: ('SQ', '1', "Equipment Reference Point Coordinates Sequence", '', 'EquipmentReferencePointCoordinatesSequence'),
    0x300A0678: ('SQ', '1', "Equipment Reference Point Code Sequence", '', 'EquipmentReferencePointCodeSequence'),
    0x300A0679: ('FD', '1', "RT Beam Limiting Device Angle", '', 'RTBeamLimitingDeviceAngle'),
    0x300A067A: ('FD', '1', "Source Roll Angle", '', 'SourceRollAngle'),
    0x300A067B: ('SQ', '1', "Radiation GenerationMode Sequence", '', 'RadiationGenerationModeSequence'),
    0x300A067C: ('SH', '1', "Radiation GenerationMode Label", '', 'RadiationGenerationModeLabel'),
    0x300A067D: ('ST', '1', "Radiation GenerationMode Description", '', 'RadiationGenerationModeDescription'),
    0x300A067E: ('SQ', '1', "Radiation GenerationMode Machine Code Sequence", '', 'RadiationGenerationModeMachineCodeSequence'),
    0x300A067F: ('SQ', '1', "Radiation Type Code Sequence", '', 'RadiationTypeCodeSequence'),
    0x300A0680: ('DS', '1', "Nominal Energy", '', 'NominalEnergy'),
    0x300A0681: ('DS', '1', "Minimum Nominal Energy", '', 'MinimumNominalEnergy'),
    0x300A0682: ('DS', '1', "Maximum Nominal Energy", '', 'MaximumNominalEnergy'),
    0x300A0683: ('SQ', '1', "Radiation Fluence Modifier Code Sequence", '', 'RadiationFluenceModifierCodeSequence'),
    0x300A0684: ('SQ', '1', "Energy Unit Code Sequence", '', 'EnergyUnitCodeSequence'),
    0x300A0685: ('US', '1', "Number of Radiation GenerationModes", '', 'NumberOfRadiationGenerationModes'),
    0x300A0686: ('SQ', '1', "Patient Support Devices Sequence", '', 'PatientSupportDevicesSequence'),
    0x300A0687: ('US', '1', "Number of Patient Support Devices", '', 'NumberOfPatientSupportDevices'),
    0x300A0688: ('FD', '1', "RT Beam Modifier Definition Distance", '', 'RTBeamModifierDefinitionDistance'),
    0x300A0689: ('SQ', '1', "Beam Area Limit Sequence", '', 'BeamAreaLimitSequence'),
    0x300A068A: ('SQ', '1', "Referenced RT Prescription Sequence", '', 'ReferencedRTPrescriptionSequence'),
    0x300A068B: ('CS', '1', "Dose Value Interpretation", '', 'DoseValueInterpretation'),
    0x300A0700: ('UI', '1', "Treatment Session UID", '', 'TreatmentSessionUID'),
    0x300A0701: ('CS', '1', "RT Radiation Usage", '', 'RTRadiationUsage'),
    0x300A0702: ('SQ', '1', "Referenced RT Radiation Set Sequence", '', 'ReferencedRTRadiationSetSequence'),
    0x300A0703: ('SQ', '1', "Referenced RT Radiation Record Sequence", '', 'ReferencedRTRadiationRecordSequence'),
    0x300A0704: ('US', '1', "RT Radiation Set Delivery Number", '', 'RTRadiationSetDeliveryNumber'),
    0x300A0705: ('US', '1', "Clinical Fraction Number", '', 'ClinicalFractionNumber'),
    0x300A0706: ('CS', '1', "RT Treatment Fraction Completion Status", '', 'RTTreatmentFractionCompletionStatus'),
    0x300A0707: ('CS', '1', "RT Radiation Set Usage", '', 'RTRadiationSetUsage'),
    0x300A0708: ('CS', '1', "Treatment Delivery Continuation Flag", '', 'TreatmentDeliveryContinuationFlag'),
    0x300A0709: ('CS', '1', "Treatment Record Content Origin", '', 'TreatmentRecordContentOrigin'),
    0x300A0714: ('CS', '1', "RT Treatment Termination Status", '', 'RTTreatmentTerminationStatus'),
    0x300A0715: ('SQ', '1', "RT Treatment Termination Reason Code Sequence", '', 'RTTreatmentTerminationReasonCodeSequence'),
    0x300A0716: ('SQ', '1', "Machine-Specific Treatment Termination Code Sequence", '', 'MachineSpecificTreatmentTerminationCodeSequence'),
    0x300A0722: ('SQ', '1', "RT Radiation Salvage Record Control Point Sequence", '', 'RTRadiationSalvageRecordControlPointSequence'),
    0x300A0723: ('CS', '1', "Starting Meterset Value Known Flag", '', 'StartingMetersetValueKnownFlag'),
    0x300A0730: ('ST', '1', "Treatment Termination Description", '', 'TreatmentTerminationDescription'),
    0x300A0731: ('SQ', '1', "Treatment Tolerance Violation Sequence", '', 'TreatmentToleranceViolationSequence'),
    0x300A0732: ('CS', '1', "Treatment Tolerance Violation Category", '', 'TreatmentToleranceViolationCategory'),
    0x300A0733: ('SQ', '1', "Treatment Tolerance Violation Attribute Sequence", '', 'TreatmentToleranceViolationAttributeSequence'),
    0x300A0734: ('ST', '1', "Treatment Tolerance Violation Description", '', 'TreatmentToleranceViolationDescription'),
    0x300A0735: ('ST', '1', "Treatment Tolerance Violation Identification", '', 'TreatmentToleranceViolationIdentification'),
    0x300A0736: ('DT', '1', "Treatment Tolerance Violation DateTime", '', 'TreatmentToleranceViolationDateTime'),
    0x300A073A: ('DT', '1', "Recorded RT Control Point DateTime", '', 'RecordedRTControlPointDateTime'),
    0x300A073B: ('US', '1', "Referenced Radiation RT Control Point Index", '', 'ReferencedRadiationRTControlPointIndex'),
    0x300A073E: ('SQ', '1', "Alternate Value Sequence", '', 'AlternateValueSequence'),
    0x300A073F: ('SQ', '1', "Confirmation Sequence", '', 'ConfirmationSequence'),
    0x300A0740: ('SQ', '1', "Interlock Sequence", '', 'InterlockSequence'),
    0x300A0741: ('DT', '1', "Interlock DateTime", '', 'InterlockDateTime'),
    0x300A0742: ('ST', '1', "Interlock Description", '', 'InterlockDescription'),
    0x300A0743: ('SQ', '1', "Interlock Originating Device Sequence", '', 'InterlockOriginatingDeviceSequence'),
    0x300A0744: ('SQ', '1', "Interlock Code Sequence", '', 'InterlockCodeSequence'),
    0x300A0745: ('SQ', '1', "Interlock Resolution Code Sequence", '', 'InterlockResolutionCodeSequence'),
    0x300A0746: ('SQ', '1', "Interlock Resolution User Sequence", '', 'InterlockResolutionUserSequence'),
    0x300A0760: ('DT', '1', "Override DateTime", '', 'OverrideDateTime'),
    0x300A0761: ('SQ', '1', "Treatment Tolerance Violation Type Code Sequence", '', 'TreatmentToleranceViolationTypeCodeSequence'),
    0x300A0762: ('SQ', '1', "Treatment Tolerance Violation Cause Code Sequence", '', 'TreatmentToleranceViolationCauseCodeSequence'),
    0x300A0772: ('SQ', '1', "Measured Meterset to Dose Mapping Sequence", '', 'MeasuredMetersetToDoseMappingSequence'),
    0x300A0773: ('US', '1', "Referenced Expected In-Vivo Measurement Value Index", '', 'ReferencedExpectedInVivoMeasurementValueIndex'),
    0x300A0774: ('SQ', '1', "Dose Measurement Device Code Sequence", '', 'DoseMeasurementDeviceCodeSequence'),
    0x300A0780: ('SQ', '1', "Additional Parameter Recording Instance Sequence", '', 'AdditionalParameterRecordingInstanceSequence'),
    0x300A0782: ('US', '1', "Retired-blank", 'Retired', ''),
    0x300A0783: ('ST', '1', "Interlock Origin Description", '', 'InterlockOriginDescription'),
    0x300A0784: ('SQ', '1', "RT Patient Position Scope Sequence", '', 'RTPatientPositionScopeSequence'),
    0x300A0785: ('UI', '1', "Referenced Treatment Position Group UID", '', 'ReferencedTreatmentPositionGroupUID'),
    0x300A0786: ('US', '1', "Radiation Order Index", '', 'RadiationOrderIndex'),
    0x300A0787: ('SQ', '1', "Omitted Radiation Sequence", '', 'OmittedRadiationSequence'),
    0x300A0788: ('SQ', '1', "Reason for Omission Code Sequence", '', 'ReasonForOmissionCodeSequence'),
    0x300A0789: ('SQ', '1', "RT Delivery Start Patient Position Sequence", '', 'RTDeliveryStartPatientPositionSequence'),
    0x300A078A: ('SQ', '1', "RT Treatment Preparation Patient Position Sequence", '', 'RTTreatmentPreparationPatientPositionSequence'),
    0x300A078B: ('SQ', '1', "Referenced RT Treatment Preparation Sequence", '', 'ReferencedRTTreatmentPreparationSequence'),
    0x300A078C: ('SQ', '1', "Referenced Patient Setup Photo Sequence", '', 'ReferencedPatientSetupPhotoSequence'),
    0x300A078D: ('SQ', '1', "Patient Treatment Preparation Method Code Sequence", '', 'PatientTreatmentPreparationMethodCodeSequence'),
    0x300A078E: ('LT', '1', "Patient Treatment Preparation Procedure Parameter Description", '', 'PatientTreatmentPreparationProcedureParameterDescription'),
    0x300A078F: ('SQ', '1', "Patient Treatment Preparation Device Sequence", '', 'PatientTreatmentPreparationDeviceSequence'),
    0x300A0790: ('SQ', '1', "Patient Treatment Preparation Procedure Sequence", '', 'PatientTreatmentPreparationProcedureSequence'),
    0x300A0791: ('SQ', '1', "Patient Treatment Preparation Procedure Code Sequence", '', 'PatientTreatmentPreparationProcedureCodeSequence'),
    0x300A0792: ('LT', '1', "Patient Treatment Preparation Method Description", '', 'PatientTreatmentPreparationMethodDescription'),
    0x300A0793: ('SQ', '1', "Patient Treatment Preparation Procedure Parameter Sequence", '', 'PatientTreatmentPreparationProcedureParameterSequence'),
    0x300A0794: ('LT', '1', "Patient Setup Photo Description", '', 'PatientSetupPhotoDescription'),
    0x300A0795: ('US', '1', "Patient Treatment Preparation Procedure Index", '', 'PatientTreatmentPreparationProcedureIndex'),
    0x300A0796: ('US', '1', "Referenced Patient Setup Procedure Index", '', 'ReferencedPatientSetupProcedureIndex'),
    0x300A0797: ('SQ', '1', "RT Radiation Task Sequence", '', 'RTRadiationTaskSequence'),
    0x300A0798: ('SQ', '1', "RT Patient Position Displacement Sequence", '', 'RTPatientPositionDisplacementSequence'),
    0x300A0799: ('SQ', '1', "RT Patient Position Sequence", '', 'RTPatientPositionSequence'),
    0x300A079A: ('LO', '1', "Displacement Reference Label", '', 'DisplacementReferenceLabel'),
    0x300A079B: ('FD', '16', "Displacement Matrix", '', 'DisplacementMatrix'),
    0x300A079C: ('SQ', '1', "Patient Support Displacement Sequence", '', 'PatientSupportDisplacementSequence'),
    0x300A079D: ('SQ', '1', "Displacement Reference Location Code Sequence", '', 'DisplacementReferenceLocationCodeSequence'),
    0x300A079E: ('CS', '1', "RT Radiation Set Delivery Usage", '', 'RTRadiationSetDeliveryUsage'),
    0x300A079F: ('SQ', '1', "Patient Treatment Preparation Sequence", '', 'PatientTreatmentPreparationSequence'),
    0x300A07A0: ('SQ', '1', "Patient to Equipment Relationship Sequence", '', 'PatientToEquipmentRelationshipSequence'),
    0x300A07A1: ('SQ', '1', "Imaging Equipment to Treatment Delivery Device Relationship Sequence", '', 'ImagingEquipmentToTreatmentDeliveryDeviceRelationshipSequence'),
    0x300C0002: ('SQ', '1', "Referenced RT Plan Sequence", '', 'ReferencedRTPlanSequence'),
    0x300C0004: ('SQ', '1', "Referenced Beam Sequence", '', 'ReferencedBeamSequence'),
    0x300C0006: ('IS', '1', "Referenced Beam Number", '', 'ReferencedBeamNumber'),
    0x300C0007: ('IS', '1', "Referenced Reference Image Number", '', 'ReferencedReferenceImageNumber'),
    0x300C0008: ('DS', '1', "Start Cumulative Meterset Weight", '', 'StartCumulativeMetersetWeight'),
    0x300C0009: ('DS', '1', "End Cumulative Meterset Weight", '', 'EndCumulativeMetersetWeight'),
    0x300C000A: ('SQ', '1', "Referenced Brachy Application Setup Sequence", '', 'ReferencedBrachyApplicationSetupSequence'),
    0x300C000C: ('IS', '1', "Referenced Brachy Application Setup Number", '', 'ReferencedBrachyApplicationSetupNumber'),
    0x300C000E: ('IS', '1', "Referenced Source Number", '', 'ReferencedSourceNumber'),
    0x300C0020: ('SQ', '1', "Referenced Fraction Group Sequence", '', 'ReferencedFractionGroupSequence'),
    0x300C0022: ('IS', '1', "Referenced Fraction Group Number", '', 'ReferencedFractionGroupNumber'),
    0x300C0040: ('SQ', '1', "Referenced Verification Image Sequence", '', 'ReferencedVerificationImageSequence'),
    0x300C0042: ('SQ', '1', "Referenced Reference Image Sequence", '', 'ReferencedReferenceImageSequence'),
    0x300C0050: ('SQ', '1', "Referenced Dose Reference Sequence", '', 'ReferencedDoseReferenceSequence'),
    0x300C0051: ('IS', '1', "Referenced Dose Reference Number", '', 'ReferencedDoseReferenceNumber'),
    0x300C0055: ('SQ', '1', "Brachy Referenced Dose Reference Sequence", '', 'BrachyReferencedDoseReferenceSequence'),
    0x300C0060: ('SQ', '1', "Referenced Structure Set Sequence", '', 'ReferencedStructureSetSequence'),
    0x300C006A: ('IS', '1', "Referenced Patient Setup Number", '', 'ReferencedPatientSetupNumber'),
    0x300C0080: ('SQ', '1', "Referenced Dose Sequence", '', 'ReferencedDoseSequence'),
    0x300C00A0: ('IS', '1', "Referenced Tolerance Table Number", '', 'ReferencedToleranceTableNumber'),
    0x300C00B0: ('SQ', '1', "Referenced Bolus Sequence", '', 'ReferencedBolusSequence'),
    0x300C00C0: ('IS', '1', "Referenced Wedge Number", '', 'ReferencedWedgeNumber'),
    0x300C00D0: ('IS', '1', "Referenced Compensator Number", '', 'ReferencedCompensatorNumber'),
    0x300C00E0: ('IS', '1', "Referenced Block Number", '', 'ReferencedBlockNumber'),
    0x300C00F0: ('IS', '1', "Referenced Control Point Index", '', 'ReferencedControlPointIndex'),
    0x300C00F2: ('SQ', '1', "Referenced Control Point Sequence", '', 'ReferencedControlPointSequence'),
    0x300C00F4: ('IS', '1', "Referenced Start Control Point Index", '', 'ReferencedStartControlPointIndex'),
    0x300C00F6: ('IS', '1', "Referenced Stop Control Point Index", '', 'ReferencedStopControlPointIndex'),
    0x300C0100: ('IS', '1', "Referenced Range Shifter Number", '', 'ReferencedRangeShifterNumber'),
    0x300C0102: ('IS', '1', "Referenced Lateral Spreading Device Number", '', 'ReferencedLateralSpreadingDeviceNumber'),
    0x300C0104: ('IS', '1', "Referenced Range Modulator Number", '', 'ReferencedRangeModulatorNumber'),
    0x300C0111: ('SQ', '1', "Omitted Beam Task Sequence", '', 'OmittedBeamTaskSequence'),
    0x300C0112: ('CS', '1', "Reason for Omission", '', 'ReasonForOmission'),
    0x300C0113: ('LO', '1', "Reason for Omission Description", '', 'ReasonForOmissionDescription'),
    0x300C0114: ('SQ', '1', "Prescription Overview Sequence", '', 'PrescriptionOverviewSequence'),
    0x300C0115: ('FL', '1', "Total Prescription Dose", '', 'TotalPrescriptionDose'),
    0x300C0116: ('SQ', '1', "Plan Overview Sequence", '', 'PlanOverviewSequence'),
    0x300C0117: ('US', '1', "Plan Overview Index", '', 'PlanOverviewIndex'),
    0x300C0118: ('US', '1', "Referenced Plan Overview Index", '', 'ReferencedPlanOverviewIndex'),
    0x300C0119: ('US', '1', "Number of Fractions Included", '', 'NumberOfFractionsIncluded'),
    0x300C0120: ('SQ', '1', "Dose Calibration Conditions Sequence", '', 'DoseCalibrationConditionsSequence'),
    0x300C0121: ('FD', '1', "Absorbed Dose to Meterset Ratio", '', 'AbsorbedDoseToMetersetRatio'),
    0x300C0122: ('FD', '2', "Delineated Radiation Field Size", '', 'DelineatedRadiationFieldSize'),
    0x300C0123: ('CS', '1', "Dose Calibration Conditions Verified Flag", '', 'DoseCalibrationConditionsVerifiedFlag'),
    0x300C0124: ('FD', '1', "Calibration Reference Point Depth", '', 'CalibrationReferencePointDepth'),
    0x300C0125: ('SQ', '1', "Gating Beam Hold Transition Sequence", '', 'GatingBeamHoldTransitionSequence'),
    0x300C0126: ('CS', '1', "Beam Hold Transition", '', 'BeamHoldTransition'),
    0x300C0127: ('DT', '1', "Beam Hold Transition DateTime", '', 'BeamHoldTransitionDateTime'),
    0x300C0128: ('SQ', '1', "Beam Hold Originating Device Sequence", '', 'BeamHoldOriginatingDeviceSequence'),
    0x300C0129: ('CS', '1', "Beam Hold Transition Trigger Source", '', 'BeamHoldTransitionTriggerSource'),
    0x300E0002: ('CS', '1', "Approval Status", '', 'ApprovalStatus'),
    0x300E0004: ('DA', '1', "Review Date", '', 'ReviewDate'),
    0x300E0005: ('TM', '1', "Review Time", '', 'ReviewTime'),
    0x300E0008: ('PN', '1', "Reviewer Name", '', 'ReviewerName'),
    0x30100001: ('SQ', '1', "Radiobiological Dose Effect Sequence", '', 'RadiobiologicalDoseEffectSequence'),
    0x30100002: ('CS', '1', "Radiobiological Dose Effect Flag", '', 'RadiobiologicalDoseEffectFlag'),
    0x30100003: ('SQ', '1', "Effective Dose Calculation Method Category Code Sequence", '', 'EffectiveDoseCalculationMethodCategoryCodeSequence'),
    0x30100004: ('SQ', '1', "Effective Dose Calculation Method Code Sequence", '', 'EffectiveDoseCalculationMethodCodeSequence'),
    0x30100005: ('LO', '1', "Effective Dose Calculation Method Description", '', 'EffectiveDoseCalculationMethodDescription'),
    0x30100006: ('UI', '1', "Conceptual Volume UID", '', 'ConceptualVolumeUID'),
    0x30100007: ('SQ', '1', "Originating SOP Instance Reference Sequence", '', 'OriginatingSOPInstanceReferenceSequence'),
    0x30100008: ('SQ', '1', "Conceptual Volume Constituent Sequence", '', 'ConceptualVolumeConstituentSequence'),
    0x30100009: ('SQ', '1', "Equivalent Conceptual Volume Instance Reference Sequence", '', 'EquivalentConceptualVolumeInstanceReferenceSequence'),
    0x3010000A: ('SQ', '1', "Equivalent Conceptual Volumes Sequence", '', 'EquivalentConceptualVolumesSequence'),
    0x3010000B: ('UI', '1', "Referenced Conceptual Volume UID", '', 'ReferencedConceptualVolumeUID'),
    0x3010000C: ('UT', '1', "Conceptual Volume Combination Expression", '', 'ConceptualVolumeCombinationExpression'),
    0x3010000D: ('US', '1', "Conceptual Volume Constituent Index", '', 'ConceptualVolumeConstituentIndex'),
    0x3010000E: ('CS', '1', "Conceptual Volume Combination Flag", '', 'ConceptualVolumeCombinationFlag'),
    0x3010000F: ('ST', '1', "Conceptual Volume Combination Description", '', 'ConceptualVolumeCombinationDescription'),
    0x30100010: ('CS', '1', "Conceptual Volume Segmentation Defined Flag", '', 'ConceptualVolumeSegmentationDefinedFlag'),
    0x30100011: ('SQ', '1', "Conceptual Volume Segmentation Reference Sequence", '', 'ConceptualVolumeSegmentationReferenceSequence'),
    0x30100012: ('SQ', '1', "Conceptual Volume Constituent Segmentation Reference Sequence", '', 'ConceptualVolumeConstituentSegmentationReferenceSequence'),
    0x30100013: ('UI', '1', "Constituent Conceptual Volume UID", '', 'ConstituentConceptualVolumeUID'),
    0x30100014: ('SQ', '1', "Derivation Conceptual Volume Sequence", '', 'DerivationConceptualVolumeSequence'),
    0x30100015: ('UI', '1', "Source Conceptual Volume UID", '', 'SourceConceptualVolumeUID'),
    0x30100016: ('SQ', '1', "Conceptual Volume Derivation Algorithm Sequence", '', 'ConceptualVolumeDerivationAlgorithmSequence'),
    0x30100017: ('ST', '1', "Conceptual Volume Description", '', 'ConceptualVolumeDescription'),
    0x30100018: ('SQ', '1', "Source Conceptual Volume Sequence", '', 'SourceConceptualVolumeSequence'),
    0x30100019: ('SQ', '1', "Author Identification Sequence", '', 'AuthorIdentificationSequence'),
    0x3010001A: ('LO', '1', "Manufacturer's Model Version", '', 'ManufacturerModelVersion'),
    0x3010001B: ('UC', '1', "Device Alternate Identifier", '', 'DeviceAlternateIdentifier'),
    0x3010001C: ('CS', '1', "Device Alternate Identifier Type", '', 'DeviceAlternateIdentifierType'),
    0x3010001D: ('LT', '1', "Device Alternate Identifier Format", '', 'DeviceAlternateIdentifierFormat'),
    0x3010001E: ('LO', '1', "Segmentation Creation Template Label", '', 'SegmentationCreationTemplateLabel'),
    0x3010001F: ('UI', '1', "Segmentation Template UID", '', 'SegmentationTemplateUID'),
    0x30100020: ('US', '1', "Referenced Segment Reference Index", '', 'ReferencedSegmentReferenceIndex'),
    0x30100021: ('SQ', '1', "Segment Reference Sequence", '', 'SegmentReferenceSequence'),
    0x30100022: ('US', '1', "Segment Reference Index", '', 'SegmentReferenceIndex'),
    0x30100023: ('SQ', '1', "Direct Segment Reference Sequence", '', 'DirectSegmentReferenceSequence'),
    0x30100024: ('SQ', '1', "Combination Segment Reference Sequence", '', 'CombinationSegmentReferenceSequence'),
    0x30100025: ('SQ', '1', "Conceptual Volume Sequence", '', 'ConceptualVolumeSequence'),
    0x30100026: ('SQ', '1', "Segmented RT Accessory Device Sequence", '', 'SegmentedRTAccessoryDeviceSequence'),
    0x30100027: ('SQ', '1', "Segment Characteristics Sequence", '', 'SegmentCharacteristicsSequence'),
    0x30100028: ('SQ', '1', "Related Segment Characteristics Sequence", '', 'RelatedSegmentCharacteristicsSequence'),
    0x30100029: ('US', '1', "Segment Characteristics Precedence", '', 'SegmentCharacteristicsPrecedence'),
    0x3010002A: ('SQ', '1', "RT Segment Annotation Sequence", '', 'RTSegmentAnnotationSequence'),
    0x3010002B: ('SQ', '1', "Segment Annotation Category Code Sequence", '', 'SegmentAnnotationCategoryCodeSequence'),
    0x3010002C: ('SQ', '1', "Segment Annotation Type Code Sequence", '', 'SegmentAnnotationTypeCodeSequence'),
    0x3010002D: ('LO', '1', "Device Label", '', 'DeviceLabel'),
    0x3010002E: ('SQ', '1', "Device Type Code Sequence", '', 'DeviceTypeCodeSequence'),
    0x3010002F: ('SQ', '1', "Segment Annotation Type Modifier Code Sequence", '', 'SegmentAnnotationTypeModifierCodeSequence'),
    0x30100030: ('SQ', '1', "Patient Equipment Relationship Code Sequence", '', 'PatientEquipmentRelationshipCodeSequence'),
    0x30100031: ('UI', '1', "Referenced Fiducials UID", '', 'ReferencedFiducialsUID'),
    0x30100032: ('SQ', '1', "Patient Treatment Orientation Sequence", '', 'PatientTreatmentOrientationSequence'),
    0x30100033: ('SH', '1', "User Content Label", '', 'UserContentLabel'),
    0x30100034: ('LO', '1', "User Content Long Label", '', 'UserContentLongLabel'),
    0x30100035: ('SH', '1', "Entity Label", '', 'EntityLabel'),
    0x30100036: ('LO', '1', "Entity Name", '', 'EntityName'),
    0x30100037: ('ST', '1', "Entity Description", '', 'EntityDescription'),
    0x30100038: ('LO', '1', "Entity Long Label", '', 'EntityLongLabel'),
    0x30100039: ('US', '1', "Device Index", '', 'DeviceIndex'),
    0x3010003A: ('US', '1', "RT Treatment Phase Index", '', 'RTTreatmentPhaseIndex'),
    0x3010003B: ('UI', '1', "RT Treatment Phase UID", '', 'RTTreatmentPhaseUID'),
    0x3010003C: ('US', '1', "RT Prescription Index", '', 'RTPrescriptionIndex'),
    0x3010003D: ('US', '1', "RT Segment Annotation Index", '', 'RTSegmentAnnotationIndex'),
    0x3010003E: ('US', '1', "Basis RT Treatment Phase Index", '', 'BasisRTTreatmentPhaseIndex'),
    0x3010003F: ('US', '1', "Related RT Treatment Phase Index", '', 'RelatedRTTreatmentPhaseIndex'),
    0x30100040: ('US', '1', "Referenced RT Treatment Phase Index", '', 'ReferencedRTTreatmentPhaseIndex'),
    0x30100041: ('US', '1', "Referenced RT Prescription Index", '', 'ReferencedRTPrescriptionIndex'),
    0x30100042: ('US', '1', "Referenced Parent RT Prescription Index", '', 'ReferencedParentRTPrescriptionIndex'),
    0x30100043: ('ST', '1', "Manufacturer's Device Identifier", '', 'ManufacturerDeviceIdentifier'),
    0x30100044: ('SQ', '1', "Instance-Level Referenced Performed Procedure Step Sequence", '', 'InstanceLevelReferencedPerformedProcedureStepSequence'),
    0x30100045: ('CS', '1', "RT Treatment Phase Intent Presence Flag", '', 'RTTreatmentPhaseIntentPresenceFlag'),
    0x30100046: ('CS', '1', "Radiotherapy Treatment Type", '', 'RadiotherapyTreatmentType'),
    0x30100047: ('CS', '1-n', "Teletherapy Radiation Type", '', 'TeletherapyRadiationType'),
    0x30100048: ('CS', '1-n', "Brachytherapy Source Type", '', 'BrachytherapySourceType'),
    0x30100049: ('SQ', '1', "Referenced RT Treatment Phase Sequence", '', 'ReferencedRTTreatmentPhaseSequence'),
    0x3010004A: ('SQ', '1', "Referenced Direct Segment Instance Sequence", '', 'ReferencedDirectSegmentInstanceSequence'),
    0x3010004B: ('SQ', '1', "Intended RT Treatment Phase Sequence", '', 'IntendedRTTreatmentPhaseSequence'),
    0x3010004C: ('DA', '1', "Intended Phase Start Date", '', 'IntendedPhaseStartDate'),
    0x3010004D: ('DA', '1', "Intended Phase End Date", '', 'IntendedPhaseEndDate'),
    0x3010004E: ('SQ', '1', "RT Treatment Phase Interval Sequence", '', 'RTTreatmentPhaseIntervalSequence'),
    0x3010004F: ('CS', '1', "Temporal Relationship Interval Anchor", '', 'TemporalRelationshipIntervalAnchor'),
    0x30100050: ('FD', '1', "Minimum Number of Interval Days", '', 'MinimumNumberOfIntervalDays'),
    0x30100051: ('FD', '1', "Maximum Number of Interval Days", '', 'MaximumNumberOfIntervalDays'),
    0x30100052: ('UI', '1-n', "Pertinent SOP Classes in Study", '', 'PertinentSOPClassesInStudy'),
    0x30100053: ('UI', '1-n', "Pertinent SOP Classes in Series", '', 'PertinentSOPClassesInSeries'),
    0x30100054: ('LO', '1', "RT Prescription Label", '', 'RTPrescriptionLabel'),
    0x30100055: ('SQ', '1', "RT Physician Intent Predecessor Sequence", '', 'RTPhysicianIntentPredecessorSequence'),
    0x30100056: ('LO', '1', "RT Treatment Approach Label", '', 'RTTreatmentApproachLabel'),
    0x30100057: ('SQ', '1', "RT Physician Intent Sequence", '', 'RTPhysicianIntentSequence'),
    0x30100058: ('US', '1', "RT Physician Intent Index", '', 'RTPhysicianIntentIndex'),
    0x30100059: ('CS', '1', "RT Treatment Intent Type", '', 'RTTreatmentIntentType'),
    0x3010005A: ('UT', '1', "RT Physician Intent Narrative", '', 'RTPhysicianIntentNarrative'),
    0x3010005B: ('SQ', '1', "RT Protocol Code Sequence", '', 'RTProtocolCodeSequence'),
    0x3010005C: ('ST', '1', "Reason for Superseding", '', 'ReasonForSuperseding'),
    0x3010005D: ('SQ', '1', "RT Diagnosis Code Sequence", '', 'RTDiagnosisCodeSequence'),
    0x3010005E: ('US', '1', "Referenced RT Physician Intent Index", '', 'ReferencedRTPhysicianIntentIndex'),
    0x3010005F: ('SQ', '1', "RT Physician Intent Input Instance Sequence", '', 'RTPhysicianIntentInputInstanceSequence'),
    0x30100060: ('SQ', '1', "RT Anatomic Prescription Sequence", '', 'RTAnatomicPrescriptionSequence'),
    0x30100061: ('UT', '1', "Prior Treatment Dose Description", '', 'PriorTreatmentDoseDescription'),
    0x30100062: ('SQ', '1', "Prior Treatment Reference Sequence", '', 'PriorTreatmentReferenceSequence'),
    0x30100063: ('CS', '1', "Dosimetric Objective Evaluation Scope", '', 'DosimetricObjectiveEvaluationScope'),
    0x30100064: ('SQ', '1', "Therapeutic Role Category Code Sequence", '', 'TherapeuticRoleCategoryCodeSequence'),
    0x30100065: ('SQ', '1', "Therapeutic Role Type Code Sequence", '', 'TherapeuticRoleTypeCodeSequence'),
    0x30100066: ('US', '1', "Conceptual Volume Optimization Precedence", '', 'ConceptualVolumeOptimizationPrecedence'),
    0x30100067: ('SQ', '1', "Conceptual Volume Category Code Sequence", '', 'ConceptualVolumeCategoryCodeSequence'),
    0x30100068: ('CS', '1', "Conceptual Volume Blocking Constraint", '', 'ConceptualVolumeBlockingConstraint'),
    0x30100069: ('SQ', '1', "Conceptual Volume Type Code Sequence", '', 'ConceptualVolumeTypeCodeSequence'),
    0x3010006A: ('SQ', '1', "Conceptual Volume Type Modifier Code Sequence", '', 'ConceptualVolumeTypeModifierCodeSequence'),
    0x3010006B: ('SQ', '1', "RT Prescription Sequence", '', 'RTPrescriptionSequence'),
    0x3010006C: ('SQ', '1', "Dosimetric Objective Sequence", '', 'DosimetricObjectiveSequence'),
    0x3010006D: ('SQ', '1', "Dosimetric Objective Type Code Sequence", '', 'DosimetricObjectiveTypeCodeSequence'),
    0x3010006E: ('UI', '1', "Dosimetric Objective UID", '', 'DosimetricObjectiveUID'),
    0x3010006F: ('UI', '1', "Referenced Dosimetric Objective UID", '', 'ReferencedDosimetricObjectiveUID'),
    0x30100070: ('SQ', '1', "Dosimetric Objective Parameter Sequence", '', 'DosimetricObjectiveParameterSequence'),
    0x30100071: ('SQ', '1', "Referenced Dosimetric Objectives Sequence", '', 'ReferencedDosimetricObjectivesSequence'),
    0x30100073: ('CS', '1', "Absolute Dosimetric Objective Flag", '', 'AbsoluteDosimetricObjectiveFlag'),
    0x30100074: ('FD', '1', "Dosimetric Objective Weight", '', 'DosimetricObjectiveWeight'),
    0x30100075: ('CS', '1', "Dosimetric Objective Purpose", '', 'DosimetricObjectivePurpose'),
    0x30100076: ('SQ', '1', "Planning Input Information Sequence", '', 'PlanningInputInformationSequence'),
    0x30100077: ('LO', '1', "Treatment Site", '', 'TreatmentSite'),
    0x30100078: ('SQ', '1', "Treatment Site Code Sequence", '', 'TreatmentSiteCodeSequence'),
    0x30100079: ('SQ', '1', "Fraction Pattern Sequence", '', 'FractionPatternSequence'),
    0x3010007A: ('UT', '1', "Treatment Technique Notes", '', 'TreatmentTechniqueNotes'),
    0x3010007B: ('UT', '1', "Prescription Notes", '', 'PrescriptionNotes'),
    0x3010007C: ('IS', '1', "Number of Interval Fractions", '', 'NumberOfIntervalFractions'),
    0x3010007D: ('US', '1', "Number of Fractions", '', 'NumberOfFractions'),
    0x3010007E: ('US', '1', "Intended Delivery Duration", '', 'IntendedDeliveryDuration'),
    0x3010007F: ('UT', '1', "Fractionation Notes", '', 'FractionationNotes'),
    0x30100080: ('SQ', '1', "RT Treatment Technique Code Sequence", '', 'RTTreatmentTechniqueCodeSequence'),
    0x30100081: ('SQ', '1', "Prescription Notes Sequence", '', 'PrescriptionNotesSequence'),
    0x30100082: ('SQ', '1', "Fraction-Based Relationship Sequence", '', 'FractionBasedRelationshipSequence'),
    0x30100083: ('CS', '1', "Fraction-Based Relationship Interval Anchor", '', 'FractionBasedRelationshipIntervalAnchor'),
    0x30100084: ('FD', '1', "Minimum Hours between Fractions", '', 'MinimumHoursBetweenFractions'),
    0x30100085: ('TM', '1-n', "Intended Fraction Start Time", '', 'IntendedFractionStartTime'),
    0x30100086: ('LT', '1', "Intended Start Day of Week", '', 'IntendedStartDayOfWeek'),
    0x30100087: ('SQ', '1', "Weekday Fraction Pattern Sequence", '', 'WeekdayFractionPatternSequence'),
    0x30100088: ('SQ', '1', "Delivery Time Structure Code Sequence", '', 'DeliveryTimeStructureCodeSequence'),
    0x30100089: ('SQ', '1', "Treatment Site Modifier Code Sequence", '', 'TreatmentSiteModifierCodeSequence'),
    0x30100090: ('CS', '1', "Robotic Base Location Indicator", 'Retired', 'RoboticBaseLocationIndicator'),
    0x30100091: ('SQ', '1', "Robotic Path Node Set Code Sequence", '', 'RoboticPathNodeSetCodeSequence'),
    0x30100092: ('UL', '1', "Robotic Node Identifier", '', 'RoboticNodeIdentifier'),
    0x30100093: ('FD', '3', "RT Treatment Source Coordinates", '', 'RTTreatmentSourceCoordinates'),
    0x30100094: ('FD', '1', "Radiation Source Coordinate SystemYaw Angle", '', 'RadiationSourceCoordinateSystemYawAngle'),
    0x30100095: ('FD', '1', "Radiation Source Coordinate SystemRoll Angle", '', 'RadiationSourceCoordinateSystemRollAngle'),
    0x30100096: ('FD', '1', "Radiation Source Coordinate System Pitch Angle", '', 'RadiationSourceCoordinateSystemPitchAngle'),
    0x30100097: ('SQ', '1', "Robotic Path Control Point Sequence", '', 'RoboticPathControlPointSequence'),
    0x30100098: ('SQ', '1', "Tomotherapeutic Control Point Sequence", '', 'TomotherapeuticControlPointSequence'),
    0x30100099: ('FD', '1-n', "Tomotherapeutic Leaf Open Durations", '', 'TomotherapeuticLeafOpenDurations'),
    0x3010009A: ('FD', '1-n', "Tomotherapeutic Leaf Initial Closed Durations", '', 'TomotherapeuticLeafInitialClosedDurations'),
    0x301000A0: ('SQ', '1', "Conceptual Volume Identification Sequence", '', 'ConceptualVolumeIdentificationSequence'),
    0x40000010: ('LT', '1', "Arbitrary", 'Retired', 'Arbitrary'),
    0x40004000: ('LT', '1', "Text Comments", 'Retired', 'TextComments'),
    0x40080040: ('SH', '1', "Results ID", 'Retired', 'ResultsID'),
    0x40080042: ('LO', '1', "Results ID Issuer", 'Retired', 'ResultsIDIssuer'),
    0x40080050: ('SQ', '1', "Referenced Interpretation Sequence", 'Retired', 'ReferencedInterpretationSequence'),
    0x400800FF: ('CS', '1', "Report Production Status (Trial)", 'Retired', 'ReportProductionStatusTrial'),
    0x40080100: ('DA', '1', "Interpretation Recorded Date", 'Retired', 'InterpretationRecordedDate'),
    0x40080101: ('TM', '1', "Interpretation Recorded Time", 'Retired', 'InterpretationRecordedTime'),
    0x40080102: ('PN', '1', "Interpretation Recorder", 'Retired', 'InterpretationRecorder'),
    0x40080103: ('LO', '1', "Reference to Recorded Sound", 'Retired', 'ReferenceToRecordedSound'),
    0x40080108: ('DA', '1', "Interpretation Transcription Date", 'Retired', 'InterpretationTranscriptionDate'),
    0x40080109: ('TM', '1', "Interpretation Transcription Time", 'Retired', 'InterpretationTranscriptionTime'),
    0x4008010A: ('PN', '1', "Interpretation Transcriber", 'Retired', 'InterpretationTranscriber'),
    0x4008010B: ('ST', '1', "Interpretation Text", 'Retired', 'InterpretationText'),
    0x4008010C: ('PN', '1', "Interpretation Author", 'Retired', 'InterpretationAuthor'),
    0x40080111: ('SQ', '1', "Interpretation Approver Sequence", 'Retired', 'InterpretationApproverSequence'),
    0x40080112: ('DA', '1', "Interpretation Approval Date", 'Retired', 'InterpretationApprovalDate'),
    0x40080113: ('TM', '1', "Interpretation Approval Time", 'Retired', 'InterpretationApprovalTime'),
    0x40080114: ('PN', '1', "Physician Approving Interpretation", 'Retired', 'PhysicianApprovingInterpretation'),
    0x40080115: ('LT', '1', "Interpretation Diagnosis Description", 'Retired', 'InterpretationDiagnosisDescription'),
    0x40080117: ('SQ', '1', "Interpretation Diagnosis Code Sequence", 'Retired', 'InterpretationDiagnosisCodeSequence'),
    0x40080118: ('SQ', '1', "Results Distribution List Sequence", 'Retired', 'ResultsDistributionListSequence'),
    0x40080119: ('PN', '1', "Distribution Name", 'Retired', 'DistributionName'),
    0x4008011A: ('LO', '1', "Distribution Address", 'Retired', 'DistributionAddress'),
    0x40080200: ('SH', '1', "Interpretation ID", 'Retired', 'InterpretationID'),
    0x40080202: ('LO', '1', "Interpretation ID Issuer", 'Retired', 'InterpretationIDIssuer'),
    0x40080210: ('CS', '1', "Interpretation Type ID", 'Retired', 'InterpretationTypeID'),
    0x40080212: ('CS', '1', "Interpretation Status ID", 'Retired', 'InterpretationStatusID'),
    0x40080300: ('ST', '1', "Impressions", 'Retired', 'Impressions'),
    0x40084000: ('ST', '1', "Results Comments", 'Retired', 'ResultsComments'),
    0x40100001: ('CS', '1', "Low Energy Detectors", '', 'LowEnergyDetectors'),
    0x40100002: ('CS', '1', "High Energy Detectors", '', 'HighEnergyDetectors'),
    0x40100004: ('SQ', '1', "Detector Geometry Sequence", '', 'DetectorGeometrySequence'),
    0x40101001: ('SQ', '1', "Threat ROI Voxel Sequence", '', 'ThreatROIVoxelSequence'),
    0x40101004: ('FL', '3', "Threat ROI Base", '', 'ThreatROIBase'),
    0x40101005: ('FL', '3', "Threat ROI Extents", '', 'ThreatROIExtents'),
    0x40101006: ('OB', '1', "Threat ROI Bitmap", '', 'ThreatROIBitmap'),
    0x40101007: ('SH', '1', "Route Segment ID", '', 'RouteSegmentID'),
    0x40101008: ('CS', '1', "Gantry Type", '', 'GantryType'),
    0x40101009: ('CS', '1', "OOI Owner Type", '', 'OOIOwnerType'),
    0x4010100A: ('SQ', '1', "Route Segment Sequence", '', 'RouteSegmentSequence'),
    0x40101010: ('US', '1', "Potential Threat Object ID", '', 'PotentialThreatObjectID'),
    0x40101011: ('SQ', '1', "Threat Sequence", '', 'ThreatSequence'),
    0x40101012: ('CS', '1', "Threat Category", '', 'ThreatCategory'),
    0x40101013: ('LT', '1', "Threat Category Description", '', 'ThreatCategoryDescription'),
    0x40101014: ('CS', '1', "ATD Ability Assessment", '', 'ATDAbilityAssessment'),
    0x40101015: ('CS', '1', "ATD Assessment Flag", '', 'ATDAssessmentFlag'),
    0x40101016: ('FL', '1', "ATD Assessment Probability", '', 'ATDAssessmentProbability'),
    0x40101017: ('FL', '1', "Mass", '', 'Mass'),
    0x40101018: ('FL', '1', "Density", '', 'Density'),
    0x40101019: ('FL', '1', "Z Effective", '', 'ZEffective'),
    0x4010101A: ('SH', '1', "Boarding Pass ID", '', 'BoardingPassID'),
    0x4010101B: ('FL', '3', "Center of Mass", '', 'CenterOfMass'),
    0x4010101C: ('FL', '3', "Center of PTO", '', 'CenterOfPTO'),
    0x4010101D: ('FL', '6-n', "Bounding Polygon", '', 'BoundingPolygon'),
    0x4010101E: ('SH', '1', "Route Segment Start Location ID", '', 'RouteSegmentStartLocationID'),
    0x4010101F: ('SH', '1', "Route Segment End Location ID", '', 'RouteSegmentEndLocationID'),
    0x40101020: ('CS', '1', "Route Segment Location ID Type", '', 'RouteSegmentLocationIDType'),
    0x40101021: ('CS', '1-n', "Abort Reason", '', 'AbortReason'),
    0x40101023: ('FL', '1', "Volume of PTO", '', 'VolumeOfPTO'),
    0x40101024: ('CS', '1', "Abort Flag", '', 'AbortFlag'),
    0x40101025: ('DT', '1', "Route Segment Start Time", '', 'RouteSegmentStartTime'),
    0x40101026: ('DT', '1', "Route Segment End Time", '', 'RouteSegmentEndTime'),
    0x40101027: ('CS', '1', "TDR Type", '', 'TDRType'),
    0x40101028: ('CS', '1', "International Route Segment", '', 'InternationalRouteSegment'),
    0x40101029: ('LO', '1-n', "Threat Detection Algorithm and Version", '', 'ThreatDetectionAlgorithmAndVersion'),
    0x4010102A: ('SH', '1', "Assigned Location", '', 'AssignedLocation'),
    0x4010102B: ('DT', '1', "Alarm Decision Time", '', 'AlarmDecisionTime'),
    0x40101031: ('CS', '1', "Alarm Decision", '', 'AlarmDecision'),
    0x40101033: ('US', '1', "Number of Total Objects", '', 'NumberOfTotalObjects'),
    0x40101034: ('US', '1', "Number of Alarm Objects", '', 'NumberOfAlarmObjects'),
    0x40101037: ('SQ', '1', "PTO Representation Sequence", '', 'PTORepresentationSequence'),
    0x40101038: ('SQ', '1', "ATD Assessment Sequence", '', 'ATDAssessmentSequence'),
    0x40101039: ('CS', '1', "TIP Type", '', 'TIPType'),
    0x4010103A: ('CS', '1', "DICOS Version", '', 'DICOSVersion'),
    0x40101041: ('DT', '1', "OOI Owner Creation Time", '', 'OOIOwnerCreationTime'),
    0x40101042: ('CS', '1', "OOI Type", '', 'OOIType'),
    0x40101043: ('FL', '3', "OOI Size", '', 'OOISize'),
    0x40101044: ('CS', '1', "Acquisition Status", '', 'AcquisitionStatus'),
    0x40101045: ('SQ', '1', "Basis Materials Code Sequence", '', 'BasisMaterialsCodeSequence'),
    0x40101046: ('CS', '1', "Phantom Type", '', 'PhantomType'),
    0x40101047: ('SQ', '1', "OOI Owner Sequence", '', 'OOIOwnerSequence'),
    0x40101048: ('CS', '1', "Scan Type", '', 'ScanType'),
    0x40101051: ('LO', '1', "Itinerary ID", '', 'ItineraryID'),
    0x40101052: ('SH', '1', "Itinerary ID Type", '', 'ItineraryIDType'),
    0x40101053: ('LO', '1', "Itinerary ID Assigning Authority", '', 'ItineraryIDAssigningAuthority'),
    0x40101054: ('SH', '1', "Route ID", '', 'RouteID'),
    0x40101055: ('SH', '1', "Route ID Assigning Authority", '', 'RouteIDAssigningAuthority'),
    0x40101056: ('CS', '1', "Inbound Arrival Type", '', 'InboundArrivalType'),
    0x40101058: ('SH', '1', "Carrier ID", '', 'CarrierID'),
    0x40101059: ('CS', '1', "Carrier ID Assigning Authority", '', 'CarrierIDAssigningAuthority'),
    0x40101060: ('FL', '3', "Source Orientation", '', 'SourceOrientation'),
    0x40101061: ('FL', '3', "Source Position", '', 'SourcePosition'),
    0x40101062: ('FL', '1', "Belt Height", '', 'BeltHeight'),
    0x40101064: ('SQ', '1', "Algorithm Routing Code Sequence", '', 'AlgorithmRoutingCodeSequence'),
    0x40101067: ('CS', '1', "Transport Classification", '', 'TransportClassification'),
    0x40101068: ('LT', '1', "OOI Type Descriptor", '', 'OOITypeDescriptor'),
    0x40101069: ('FL', '1', "Total Processing Time", '', 'TotalProcessingTime'),
    0x4010106C: ('OB', '1', "Detector Calibration Data", '', 'DetectorCalibrationData'),
    0x4010106D: ('CS', '1', "Additional Screening Performed", '', 'AdditionalScreeningPerformed'),
    0x4010106E: ('CS', '1', "Additional Inspection Selection Criteria", '', 'AdditionalInspectionSelectionCriteria'),
    0x4010106F: ('SQ', '1', "Additional Inspection Method Sequence", '', 'AdditionalInspectionMethodSequence'),
    0x40101070: ('CS', '1', "AIT Device Type", '', 'AITDeviceType'),
    0x40101071: ('SQ', '1', "QR Measurements Sequence", '', 'QRMeasurementsSequence'),
    0x40101072: ('SQ', '1', "Target Material Sequence", '', 'TargetMaterialSequence'),
    0x40101073: ('FD', '1', "SNR Threshold", '', 'SNRThreshold'),
    0x40101075: ('DS', '1', "Image Scale Representation", '', 'ImageScaleRepresentation'),
    0x40101076: ('SQ', '1', "Referenced PTO Sequence", '', 'ReferencedPTOSequence'),
    0x40101077: ('SQ', '1', "Referenced TDR Instance Sequence", '', 'ReferencedTDRInstanceSequence'),
    0x40101078: ('ST', '1', "PTO Location Description", '', 'PTOLocationDescription'),
    0x40101079: ('SQ', '1', "Anomaly Locator Indicator Sequence", '', 'AnomalyLocatorIndicatorSequence'),
    0x4010107A: ('FL', '3', "Anomaly Locator Indicator", '', 'AnomalyLocatorIndicator'),
    0x4010107B: ('SQ', '1', "PTO Region Sequence", '', 'PTORegionSequence'),
    0x4010107C: ('CS', '1', "Inspection Selection Criteria", '', 'InspectionSelectionCriteria'),
    0x4010107D: ('SQ', '1', "Secondary Inspection Method Sequence", '', 'SecondaryInspectionMethodSequence'),
    0x4010107E: ('DS', '6', "PRCS to RCS Orientation", '', 'PRCSToRCSOrientation'),
    0x4FFE0001: ('SQ', '1', "MAC Parameters Sequence", '', 'MACParametersSequence'),
    0x52009229: ('SQ', '1', "Shared Functional Groups Sequence", '', 'SharedFunctionalGroupsSequence'),
    0x52009230: ('SQ', '1', "Per-Frame Functional Groups Sequence", '', 'PerFrameFunctionalGroupsSequence'),
    0x54000100: ('SQ', '1', "Waveform Sequence", '', 'WaveformSequence'),
    0x54000110: ('OB or OW', '1', "Channel Minimum Value", '', 'ChannelMinimumValue'),
    0x54000112: ('OB or OW', '1', "Channel Maximum Value", '', 'ChannelMaximumValue'),
    0x54001004: ('US', '1', "Waveform Bits Allocated", '', 'WaveformBitsAllocated'),
    0x54001006: ('CS', '1', "Waveform Sample Interpretation", '', 'WaveformSampleInterpretation'),
    0x5400100A: ('OB or OW', '1', "Waveform Padding Value", '', 'WaveformPaddingValue'),
    0x54001010: ('OB or OW', '1', "Waveform Data", '', 'WaveformData'),
    0x56000010: ('OF', '1', "First Order Phase Correction Angle", '', 'FirstOrderPhaseCorrectionAngle'),
    0x56000020: ('OF', '1', "Spectroscopy Data", '', 'SpectroscopyData'),
    0x7FE00001: ('OV', '1', "Extended Offset Table", '', 'ExtendedOffsetTable'),
    0x7FE00002: ('OV', '1', "Extended Offset Table Lengths", '', 'ExtendedOffsetTableLengths'),
    0x7FE00003: ('UV', '1', "Encapsulated Pixel Data Value Total Length", '', 'EncapsulatedPixelDataValueTotalLength'),
    0x7FE00008: ('OF', '1', "Float Pixel Data", '', 'FloatPixelData'),
    0x7FE00009: ('OD', '1', "Double Float Pixel Data", '', 'DoubleFloatPixelData'),
    0x7FE00010: ('OB or OW', '1', "Pixel Data", '', 'PixelData'),
    0x7FE00020: ('OW', '1', "Coefficients SDVN", 'Retired', 'CoefficientsSDVN'),
    0x7FE00030: ('OW', '1', "Coefficients SDHN", 'Retired', 'CoefficientsSDHN'),
    0x7FE00040: ('OW', '1', "Coefficients SDDN", 'Retired', 'CoefficientsSDDN'),
    0xFFFAFFFA: ('SQ', '1', "Digital Signatures Sequence", '', 'DigitalSignaturesSequence'),
    0xFFFCFFFC: ('OB', '1', "Data Set Trailing Padding", '', 'DataSetTrailingPadding'),
    0xFFFEE000: ('NONE', '1', "Item", '', 'Item'),
    0xFFFEE00D: ('NONE', '1', "Item Delimitation Item", '', 'ItemDelimitationItem'),
    0xFFFEE0DD: ('NONE', '1', "Sequence Delimitation Item", '', 'SequenceDelimitationItem')
}

RepeatersDictionary: dict[str, tuple[str, str, str, str, str]] = {
    '002031xx': ('CS', '1-n', "Source Image IDs", 'Retired', 'SourceImageIDs'),
    '002804x0': ('US', '1', "Rows For Nth Order Coefficients", 'Retired', 'RowsForNthOrderCoefficients'),
    '002804x1': ('US', '1', "Columns For Nth Order Coefficients", 'Retired', 'ColumnsForNthOrderCoefficients'),
    '002804x2': ('LO', '1-n', "Coefficient Coding", 'Retired', 'CoefficientCoding'),
    '002804x3': ('AT', '1-n', "Coefficient Coding Pointers", 'Retired', 'CoefficientCodingPointers'),
    '002808x0': ('CS', '1-n', "Code Label", 'Retired', 'CodeLabel'),
    '002808x2': ('US', '1', "Number of Tables", 'Retired', 'NumberOfTables'),
    '002808x3': ('AT', '1-n', "Code Table Location", 'Retired', 'CodeTableLocation'),
    '002808x4': ('US', '1', "Bits For Code Word", 'Retired', 'BitsForCodeWord'),
    '002808x8': ('AT', '1-n', "Image Data Location", 'Retired', 'ImageDataLocation'),
    '1000xxx0': ('US', '3', "Escape Triplet", 'Retired', 'EscapeTriplet'),
    '1000xxx1': ('US', '3', "Run Length Triplet", 'Retired', 'RunLengthTriplet'),
    '1000xxx2': ('US', '1', "Huffman Table Size", 'Retired', 'HuffmanTableSize'),
    '1000xxx3': ('US', '3', "Huffman Table Triplet", 'Retired', 'HuffmanTableTriplet'),
    '1000xxx4': ('US', '1', "Shift Table Size", 'Retired', 'ShiftTableSize'),
    '1000xxx5': ('US', '3', "Shift Table Triplet", 'Retired', 'ShiftTableTriplet'),
    '1010xxxx': ('US', '1-n', "Zonal Map", 'Retired', 'ZonalMap'),
    '50xx0005': ('US', '1', "Curve Dimensions", 'Retired', 'CurveDimensions'),
    '50xx0010': ('US', '1', "Number of Points", 'Retired', 'NumberOfPoints'),
    '50xx0020': ('CS', '1', "Type of Data", 'Retired', 'TypeOfData'),
    '50xx0022': ('LO', '1', "Curve Description", 'Retired', 'CurveDescription'),
    '50xx0030': ('SH', '1-n', "Axis Units", 'Retired', 'AxisUnits'),
    '50xx0040': ('SH', '1-n', "Axis Labels", 'Retired', 'AxisLabels'),
    '50xx0103': ('US', '1', "Data Value Representation", 'Retired', 'DataValueRepresentation'),
    '50xx0104': ('US', '1-n', "Minimum Coordinate Value", 'Retired', 'MinimumCoordinateValue'),
    '50xx0105': ('US', '1-n', "Maximum Coordinate Value", 'Retired', 'MaximumCoordinateValue'),
    '50xx0106': ('SH', '1-n', "Curve Range", 'Retired', 'CurveRange'),
    '50xx0110': ('US', '1-n', "Curve Data Descriptor", 'Retired', 'CurveDataDescriptor'),
    '50xx0112': ('US', '1-n', "Coordinate Start Value", 'Retired', 'CoordinateStartValue'),
    '50xx0114': ('US', '1-n', "Coordinate Step Value", 'Retired', 'CoordinateStepValue'),
    '50xx1001': ('CS', '1', "Curve Activation Layer", 'Retired', 'CurveActivationLayer'),
    '50xx2000': ('US', '1', "Audio Type", 'Retired', 'AudioType'),
    '50xx2002': ('US', '1', "Audio Sample Format", 'Retired', 'AudioSampleFormat'),
    '50xx2004': ('US', '1', "Number of Channels", 'Retired', 'NumberOfChannels'),
    '50xx2006': ('UL', '1', "Number of Samples", 'Retired', 'NumberOfSamples'),
    '50xx2008': ('UL', '1', "Sample Rate", 'Retired', 'SampleRate'),
    '50xx200A': ('UL', '1', "Total Time", 'Retired', 'TotalTime'),
    '50xx200C': ('OB or OW', '1', "Audio Sample Data", 'Retired', 'AudioSampleData'),
    '50xx200E': ('LT', '1', "Audio Comments", 'Retired', 'AudioComments'),
    '50xx2500': ('LO', '1', "Curve Label", 'Retired', 'CurveLabel'),
    '50xx2600': ('SQ', '1', "Curve Referenced Overlay Sequence", 'Retired', 'CurveReferencedOverlaySequence'),
    '50xx2610': ('US', '1', "Curve Referenced Overlay Group", 'Retired', 'CurveReferencedOverlayGroup'),
    '50xx3000': ('OB or OW', '1', "Curve Data", 'Retired', 'CurveData'),
    '60xx0010': ('US', '1', "Overlay Rows", '', 'OverlayRows'),
    '60xx0011': ('US', '1', "Overlay Columns", '', 'OverlayColumns'),
    '60xx0012': ('US', '1', "Overlay Planes", 'Retired', 'OverlayPlanes'),
    '60xx0015': ('IS', '1', "Number of Frames in Overlay", '', 'NumberOfFramesInOverlay'),
    '60xx0022': ('LO', '1', "Overlay Description", '', 'OverlayDescription'),
    '60xx0040': ('CS', '1', "Overlay Type", '', 'OverlayType'),
    '60xx0045': ('LO', '1', "Overlay Subtype", '', 'OverlaySubtype'),
    '60xx0050': ('SS', '2', "Overlay Origin", '', 'OverlayOrigin'),
    '60xx0051': ('US', '1', "Image Frame Origin", '', 'ImageFrameOrigin'),
    '60xx0052': ('US', '1', "Overlay Plane Origin", 'Retired', 'OverlayPlaneOrigin'),
    '60xx0060': ('CS', '1', "Overlay Compression Code", 'Retired', 'OverlayCompressionCode'),
    '60xx0061': ('SH', '1', "Overlay Compression Originator", 'Retired', 'OverlayCompressionOriginator'),
    '60xx0062': ('SH', '1', "Overlay Compression Label", 'Retired', 'OverlayCompressionLabel'),
    '60xx0063': ('CS', '1', "Overlay Compression Description", 'Retired', 'OverlayCompressionDescription'),
    '60xx0066': ('AT', '1-n', "Overlay Compression Step Pointers", 'Retired', 'OverlayCompressionStepPointers'),
    '60xx0068': ('US', '1', "Overlay Repeat Interval", 'Retired', 'OverlayRepeatInterval'),
    '60xx0069': ('US', '1', "Overlay Bits Grouped", 'Retired', 'OverlayBitsGrouped'),
    '60xx0100': ('US', '1', "Overlay Bits Allocated", '', 'OverlayBitsAllocated'),
    '60xx0102': ('US', '1', "Overlay Bit Position", '', 'OverlayBitPosition'),
    '60xx0110': ('CS', '1', "Overlay Format", 'Retired', 'OverlayFormat'),
    '60xx0200': ('US', '1', "Overlay Location", 'Retired', 'OverlayLocation'),
    '60xx0800': ('CS', '1-n', "Overlay Code Label", 'Retired', 'OverlayCodeLabel'),
    '60xx0802': ('US', '1', "Overlay Number of Tables", 'Retired', 'OverlayNumberOfTables'),
    '60xx0803': ('AT', '1-n', "Overlay Code Table Location", 'Retired', 'OverlayCodeTableLocation'),
    '60xx0804': ('US', '1', "Overlay Bits For Code Word", 'Retired', 'OverlayBitsForCodeWord'),
    '60xx1001': ('CS', '1', "Overlay Activation Layer", '', 'OverlayActivationLayer'),
    '60xx1100': ('US', '1', "Overlay Descriptor - Gray", 'Retired', 'OverlayDescriptorGray'),
    '60xx1101': ('US', '1', "Overlay Descriptor - Red", 'Retired', 'OverlayDescriptorRed'),
    '60xx1102': ('US', '1', "Overlay Descriptor - Green", 'Retired', 'OverlayDescriptorGreen'),
    '60xx1103': ('US', '1', "Overlay Descriptor - Blue", 'Retired', 'OverlayDescriptorBlue'),
    '60xx1200': ('US', '1-n', "Overlays - Gray", 'Retired', 'OverlaysGray'),
    '60xx1201': ('US', '1-n', "Overlays - Red", 'Retired', 'OverlaysRed'),
    '60xx1202': ('US', '1-n', "Overlays - Green", 'Retired', 'OverlaysGreen'),
    '60xx1203': ('US', '1-n', "Overlays - Blue", 'Retired', 'OverlaysBlue'),
    '60xx1301': ('IS', '1', "ROI Area", '', 'ROIArea'),
    '60xx1302': ('DS', '1', "ROI Mean", '', 'ROIMean'),
    '60xx1303': ('DS', '1', "ROI Standard Deviation", '', 'ROIStandardDeviation'),
    '60xx1500': ('LO', '1', "Overlay Label", '', 'OverlayLabel'),
    '60xx3000': ('OB or OW', '1', "Overlay Data", '', 'OverlayData'),
    '60xx4000': ('LT', '1', "Overlay Comments", 'Retired', 'OverlayComments'),
    '7Fxx0010': ('OB or OW', '1', "Variable Pixel Data", 'Retired', 'VariablePixelData'),
    '7Fxx0011': ('US', '1', "Variable Next Data Group", 'Retired', 'VariableNextDataGroup'),
    '7Fxx0020': ('OW', '1', "Variable Coefficients SDVN", 'Retired', 'VariableCoefficientsSDVN'),
    '7Fxx0030': ('OW', '1', "Variable Coefficients SDHN", 'Retired', 'VariableCoefficientsSDHN'),
    '7Fxx0040': ('OW', '1', "Variable Coefficients SDDN", 'Retired', 'VariableCoefficientsSDDN')
}
