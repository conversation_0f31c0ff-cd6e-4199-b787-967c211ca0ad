{"693_J2KR.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/693_J2KR.dcm", "693_UNCI.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/693_UNCI.dcm", "693_UNCR.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/693_UNCR.dcm", "bad_sequence.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/bad_sequence.dcm", "color-pl.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/color-pl.dcm", "color-px.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/color-px.dcm", "color3d_jpeg_baseline.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/color3d_jpeg_baseline.dcm", "eCT_Supplemental.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/eCT_Supplemental.dcm", "emri_small.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/emri_small.dcm", "emri_small_big_endian.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/emri_small_big_endian.dcm", "emri_small_jpeg_2k_lossless.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/emri_small_jpeg_2k_lossless.dcm", "emri_small_jpeg_2k_lossless_too_short.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/emri_small_jpeg_2k_lossless_too_short.dcm", "emri_small_jpeg_ls_lossless.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/emri_small_jpeg_ls_lossless.dcm", "emri_small_RLE.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/emri_small_RLE.dcm", "explicit_VR-UN.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/explicit_VR-UN.dcm", "gdcm-US-ALOKA-16.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/gdcm-US-ALOKA-16.dcm", "gdcm-US-ALOKA-16_big.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/gdcm-US-ALOKA-16_big.dcm", "JPEG-LL.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/JPEG-LL.dcm", "JPEG2000_UNC.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/JPEG2000_UNC.dcm", "JPGLosslessP14SV1_1s_1f_8b.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/JPGLosslessP14SV1_1s_1f_8b.dcm", "liver.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/liver.dcm", "liver_expb.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/liver_expb.dcm", "liver_nonbyte_aligned.dcm": "https://github.com/pydicom/pydicom-data/raw/8da482f208401d63cd63f3f4efc41b6856ef36c7/data_store/data/liver_nonbyte_aligned.dcm", "mlut_18.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/mlut_18.dcm", "MR-SIEMENS-DICOM-WithOverlays.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/MR-SIEMENS-DICOM-WithOverlays.dcm", "MR2_J2KI.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/MR2_J2KI.dcm", "MR2_J2KR.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/MR2_J2KR.dcm", "MR2_UNCI.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/MR2_UNCI.dcm", "MR2_UNCR.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/MR2_UNCR.dcm", "OBXXXX1A.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/OBXXXX1A.dcm", "OBXXXX1A_2frame.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/OBXXXX1A_2frame.dcm", "OBXXXX1A_expb.dcm": "https://github.com/pydicom/pydicom-data/raw/ce9de30a2c871f3949d5a90957753b30a29e7293/data_store/data/OBXXXX1A_expb.dcm", "OBXXXX1A_expb_2frame.dcm": "https://github.com/pydicom/pydicom-data/raw/ce9de30a2c871f3949d5a90957753b30a29e7293/data_store/data/OBXXXX1A_expb_2frame.dcm", "OBXXXX1A_rle.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/OBXXXX1A_rle.dcm", "OBXXXX1A_rle_2frame.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/OBXXXX1A_rle_2frame.dcm", "OT-PAL-8-face.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/OT-PAL-8-face.dcm", "RG1_J2KI.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/RG1_J2KI.dcm", "RG1_J2KR.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/RG1_J2KR.dcm", "RG1_UNCI.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/RG1_UNCI.dcm", "RG1_UNCR.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/RG1_UNCR.dcm", "RG3_J2KI.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/RG3_J2KI.dcm", "RG3_J2KR.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/RG3_J2KR.dcm", "RG3_UNCI.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/RG3_UNCI.dcm", "RG3_UNCR.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/RG3_UNCR.dcm", "SC_rgb.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb.dcm", "SC_rgb_16bit.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_16bit.dcm", "SC_rgb_16bit_2frame.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_16bit_2frame.dcm", "SC_rgb_2frame.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_2frame.dcm", "SC_rgb_32bit.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_32bit.dcm", "SC_rgb_32bit_2frame.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_32bit_2frame.dcm", "SC_rgb_dcmtk_ebcr_dcmd.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_dcmtk_ebcr_dcmd.dcm", "SC_rgb_dcmtk_ebcyn1_dcmd.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_dcmtk_ebcyn1_dcmd.dcm", "SC_rgb_dcmtk_ebcyn2_dcmd.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_dcmtk_ebcyn2_dcmd.dcm", "SC_rgb_dcmtk_ebcynp_dcmd.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_dcmtk_ebcynp_dcmd.dcm", "SC_rgb_dcmtk_ebcys2_dcmd.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_dcmtk_ebcys2_dcmd.dcm", "SC_rgb_dcmtk_ebcys4_dcmd.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_dcmtk_ebcys4_dcmd.dcm", "SC_rgb_expb.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_expb.dcm", "SC_rgb_expb_16bit.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_expb_16bit.dcm", "SC_rgb_expb_16bit_2frame.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_expb_16bit_2frame.dcm", "SC_rgb_expb_2frame.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_expb_2frame.dcm", "SC_rgb_expb_32bit.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_expb_32bit.dcm", "SC_rgb_expb_32bit_2frame.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_expb_32bit_2frame.dcm", "SC_rgb_gdcm2k_uncompressed.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_rgb_gdcm2k_uncompressed.dcm", "SC_ybr_full_uncompressed.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/SC_ybr_full_uncompressed.dcm", "US1_J2KI.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/US1_J2KI.dcm", "US1_J2KR.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/US1_J2KR.dcm", "US1_UNCI.dcm": "https://github.com/pydicom/pydicom-data/raw/8c2e1c744cb8c5ac96558cbedd6741dc16602055/data/US1_UNCI.dcm", "US1_UNCR.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/US1_UNCR.dcm", "vlut_04.dcm": "https://github.com/pydicom/pydicom-data/raw/39a2eb31815eec435dc26c322c27aec5cfcbddb6/data/vlut_04.dcm", "HTJ2KLossless_08_RGB.dcm": "https://github.com/pydicom/pydicom-data/raw/9bde4d24b2d4a666fa8c4e93c8bccb2a775ea536/data_store/data/HTJ2KLossless_08_RGB.dcm", "HTJ2K_08_RGB.dcm": "https://github.com/pydicom/pydicom-data/raw/9bde4d24b2d4a666fa8c4e93c8bccb2a775ea536/data_store/data/HTJ2K_08_RGB.dcm", "JLSL_RGB_ILV0.dcm": "https://github.com/pydicom/pydicom-data/raw/7358d21f75fa8cdc8ae4e0bb02f7612c52a140ec/data_store/data/JLSL_RGB_ILV0.dcm", "JLSL_RGB_ILV1.dcm": "https://github.com/pydicom/pydicom-data/raw/7358d21f75fa8cdc8ae4e0bb02f7612c52a140ec/data_store/data/JLSL_RGB_ILV1.dcm", "JLSL_RGB_ILV2.dcm": "https://github.com/pydicom/pydicom-data/raw/7358d21f75fa8cdc8ae4e0bb02f7612c52a140ec/data_store/data/JLSL_RGB_ILV2.dcm", "JLSN_RGB_ILV0.dcm": "https://github.com/pydicom/pydicom-data/raw/7358d21f75fa8cdc8ae4e0bb02f7612c52a140ec/data_store/data/JLSN_RGB_ILV0.dcm", "JLSL_08_07_0_1F.dcm": "https://github.com/pydicom/pydicom-data/raw/831d25636b0aff13916975c493b20d6fadc01d2e/data_store/data/JLSL_08_07_0_1F.dcm", "JLSL_16_15_1_1F.dcm": "https://github.com/pydicom/pydicom-data/raw/eaaec0d930f5e578db5e56892da57a8e63af71d7/data_store/data/JLSL_16_15_1_1F.dcm", "parametric_map_float.dcm": "https://github.com/pydicom/pydicom-data/raw/812f8edacbb6a1f3606ff4a9c16c54b831e9fd3b/data_store/data/parametric_map_float.dcm", "parametric_map_double_float.dcm": "https://github.com/pydicom/pydicom-data/raw/812f8edacbb6a1f3606ff4a9c16c54b831e9fd3b/data_store/data/parametric_map_double_float.dcm"}