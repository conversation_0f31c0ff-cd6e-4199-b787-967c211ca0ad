"""DICOM UID dictionary auto-generated by generate_uid_dict.py"""
# Each dict entry is UID: (Name, Type, Info, Retired, Keyword)
UID_dictionary = {
    '1.2.840.10008.1.1': ('Verification SOP Class', 'SOP Class', '', '', 'Verification'),
    '1.2.840.10008.1.2': ('Implicit VR Little Endian', 'Transfer Syntax', 'Default Transfer Syntax for DICOM', '', 'ImplicitVRLittleEndian'),
    '1.2.840.10008.1.2.1': ('Explicit VR Little Endian', 'Transfer Syntax', '', '', 'ExplicitVRLittleEndian'),
    '1.2.840.10008.1.2.1.98': ('Encapsulated Uncompressed Explicit VR Little Endian', 'Transfer Syntax', '', '', 'EncapsulatedUncompressedExplicitVRLittleEndian'),
    '1.2.840.10008.1.2.1.99': ('Deflated Explicit VR Little Endian', 'Transfer Syntax', '', '', 'DeflatedExplicitVRLittleEndian'),
    '1.2.840.10008.1.2.2': ('Explicit VR Big Endian', 'Transfer Syntax', '', 'Retired', 'ExplicitVRBigEndian'),
    '1.2.840.10008.1.2.4.50': ('JPEG Baseline (Process 1)', 'Transfer Syntax', 'Default Transfer Syntax for Lossy JPEG 8 Bit Image Compression', '', 'JPEGBaseline8Bit'),
    '1.2.840.10008.1.2.4.51': ('JPEG Extended (Process 2 and 4)', 'Transfer Syntax', 'Default Transfer Syntax for Lossy JPEG 12 Bit Image Compression (Process 4 only)', '', 'JPEGExtended12Bit'),
    '1.2.840.10008.1.2.4.52': ('JPEG Extended (Process 3 and 5)', 'Transfer Syntax', '', 'Retired', 'JPEGExtended35'),
    '1.2.840.10008.1.2.4.53': ('JPEG Spectral Selection, Non-Hierarchical (Process 6 and 8)', 'Transfer Syntax', '', 'Retired', 'JPEGSpectralSelectionNonHierarchical68'),
    '1.2.840.10008.1.2.4.54': ('JPEG Spectral Selection, Non-Hierarchical (Process 7 and 9)', 'Transfer Syntax', '', 'Retired', 'JPEGSpectralSelectionNonHierarchical79'),
    '1.2.840.10008.1.2.4.55': ('JPEG Full Progression, Non-Hierarchical (Process 10 and 12)', 'Transfer Syntax', '', 'Retired', 'JPEGFullProgressionNonHierarchical1012'),
    '1.2.840.10008.1.2.4.56': ('JPEG Full Progression, Non-Hierarchical (Process 11 and 13)', 'Transfer Syntax', '', 'Retired', 'JPEGFullProgressionNonHierarchical1113'),
    '1.2.840.10008.1.2.4.57': ('JPEG Lossless, Non-Hierarchical (Process 14)', 'Transfer Syntax', '', '', 'JPEGLossless'),
    '1.2.840.10008.1.2.4.58': ('JPEG Lossless, Non-Hierarchical (Process 15)', 'Transfer Syntax', '', 'Retired', 'JPEGLosslessNonHierarchical15'),
    '1.2.840.10008.1.2.4.59': ('JPEG Extended, Hierarchical (Process 16 and 18)', 'Transfer Syntax', '', 'Retired', 'JPEGExtendedHierarchical1618'),
    '1.2.840.10008.1.2.4.60': ('JPEG Extended, Hierarchical (Process 17 and 19)', 'Transfer Syntax', '', 'Retired', 'JPEGExtendedHierarchical1719'),
    '1.2.840.10008.1.2.4.61': ('JPEG Spectral Selection, Hierarchical (Process 20 and 22)', 'Transfer Syntax', '', 'Retired', 'JPEGSpectralSelectionHierarchical2022'),
    '1.2.840.10008.1.2.4.62': ('JPEG Spectral Selection, Hierarchical (Process 21 and 23)', 'Transfer Syntax', '', 'Retired', 'JPEGSpectralSelectionHierarchical2123'),
    '1.2.840.10008.1.2.4.63': ('JPEG Full Progression, Hierarchical (Process 24 and 26)', 'Transfer Syntax', '', 'Retired', 'JPEGFullProgressionHierarchical2426'),
    '1.2.840.10008.1.2.4.64': ('JPEG Full Progression, Hierarchical (Process 25 and 27)', 'Transfer Syntax', '', 'Retired', 'JPEGFullProgressionHierarchical2527'),
    '1.2.840.10008.1.2.4.65': ('JPEG Lossless, Hierarchical (Process 28)', 'Transfer Syntax', '', 'Retired', 'JPEGLosslessHierarchical28'),
    '1.2.840.10008.1.2.4.66': ('JPEG Lossless, Hierarchical (Process 29)', 'Transfer Syntax', '', 'Retired', 'JPEGLosslessHierarchical29'),
    '1.2.840.10008.1.2.4.70': ('JPEG Lossless, Non-Hierarchical, First-Order Prediction (Process 14 [Selection Value 1])', 'Transfer Syntax', 'Default Transfer Syntax for Lossless JPEG Image Compression', '', 'JPEGLosslessSV1'),
    '1.2.840.10008.1.2.4.80': ('JPEG-LS Lossless Image Compression', 'Transfer Syntax', '', '', 'JPEGLSLossless'),
    '1.2.840.10008.1.2.4.81': ('JPEG-LS Lossy (Near-Lossless) Image Compression', 'Transfer Syntax', '', '', 'JPEGLSNearLossless'),
    '1.2.840.10008.1.2.4.90': ('JPEG 2000 Image Compression (Lossless Only)', 'Transfer Syntax', '', '', 'JPEG2000Lossless'),
    '1.2.840.10008.********': ('JPEG 2000 Image Compression', 'Transfer Syntax', '', '', 'JPEG2000'),
    '1.2.840.10008.********': ('JPEG 2000 Part 2 Multi-component Image Compression (Lossless Only)', 'Transfer Syntax', '', '', 'JPEG2000MCLossless'),
    '1.2.840.10008.********': ('JPEG 2000 Part 2 Multi-component Image Compression', 'Transfer Syntax', '', '', 'JPEG2000MC'),
    '1.2.840.10008.********': ('JPIP Referenced', 'Transfer Syntax', '', '', 'JPIPReferenced'),
    '1.2.840.10008.********': ('JPIP Referenced Deflate', 'Transfer Syntax', '', '', 'JPIPReferencedDeflate'),
    '1.2.840.10008.*********': ('MPEG2 Main Profile / Main Level', 'Transfer Syntax', '', '', 'MPEG2MPML'),
    '1.2.840.10008.*********.1': ('Fragmentable MPEG2 Main Profile / Main Level', 'Transfer Syntax', '', '', 'MPEG2MPMLF'),
    '1.2.840.10008.*********': ('MPEG2 Main Profile / High Level', 'Transfer Syntax', '', '', 'MPEG2MPHL'),
    '1.2.840.10008.*********.1': ('Fragmentable MPEG2 Main Profile / High Level', 'Transfer Syntax', '', '', 'MPEG2MPHLF'),
    '1.2.840.10008.*********': ('MPEG-4 AVC/H.264 High Profile / Level 4.1', 'Transfer Syntax', '', '', 'MPEG4HP41'),
    '1.2.840.10008.*********.1': ('Fragmentable MPEG-4 AVC/H.264 High Profile / Level 4.1', 'Transfer Syntax', '', '', 'MPEG4HP41F'),
    '1.2.840.10008.*********': ('MPEG-4 AVC/H.264 BD-compatible High Profile / Level 4.1', 'Transfer Syntax', '', '', 'MPEG4HP41BD'),
    '1.2.840.10008.*********.1': ('Fragmentable MPEG-4 AVC/H.264 BD-compatible High Profile / Level 4.1', 'Transfer Syntax', '', '', 'MPEG4HP41BDF'),
    '1.2.840.10008.1.2.4.104': ('MPEG-4 AVC/H.264 High Profile / Level 4.2 For 2D Video', 'Transfer Syntax', '', '', 'MPEG4HP422D'),
    '1.2.840.10008.1.2.4.104.1': ('Fragmentable MPEG-4 AVC/H.264 High Profile / Level 4.2 For 2D Video', 'Transfer Syntax', '', '', 'MPEG4HP422DF'),
    '1.2.840.10008.1.2.4.105': ('MPEG-4 AVC/H.264 High Profile / Level 4.2 For 3D Video', 'Transfer Syntax', '', '', 'MPEG4HP423D'),
    '1.2.840.10008.1.2.4.105.1': ('Fragmentable MPEG-4 AVC/H.264 High Profile / Level 4.2 For 3D Video', 'Transfer Syntax', '', '', 'MPEG4HP423DF'),
    '1.2.840.10008.1.2.4.106': ('MPEG-4 AVC/H.264 Stereo High Profile / Level 4.2', 'Transfer Syntax', '', '', 'MPEG4HP42STEREO'),
    '1.2.840.10008.1.2.4.106.1': ('Fragmentable MPEG-4 AVC/H.264 Stereo High Profile / Level 4.2', 'Transfer Syntax', '', '', 'MPEG4HP42STEREOF'),
    '1.2.840.10008.1.2.4.107': ('HEVC/H.265 Main Profile / Level 5.1', 'Transfer Syntax', '', '', 'HEVCMP51'),
    '1.2.840.10008.1.2.4.108': ('HEVC/H.265 Main 10 Profile / Level 5.1', 'Transfer Syntax', '', '', 'HEVCM10P51'),
    '1.2.840.10008.1.2.4.110': ('JPEG XL Lossless', 'Transfer Syntax', '', '', 'JPEGXLLossless'),
    '1.2.840.10008.1.2.4.111': ('JPEG XL JPEG Recompression', 'Transfer Syntax', '', '', 'JPEGXLJPEGRecompression'),
    '1.2.840.10008.1.2.4.112': ('JPEG XL', 'Transfer Syntax', '', '', 'JPEGXL'),
    '1.2.840.10008.*********': ('High-Throughput JPEG 2000 Image Compression (Lossless Only)', 'Transfer Syntax', '', '', 'HTJ2KLossless'),
    '1.2.840.10008.*********': ('High-Throughput JPEG 2000 with RPCL Options Image Compression (Lossless Only)', 'Transfer Syntax', '', '', 'HTJ2KLosslessRPCL'),
    '1.2.840.10008.*********': ('High-Throughput JPEG 2000 Image Compression', 'Transfer Syntax', '', '', 'HTJ2K'),
    '1.2.840.10008.*********': ('JPIP HTJ2K Referenced', 'Transfer Syntax', '', '', 'JPIPHTJ2KReferenced'),
    '1.2.840.10008.*********': ('JPIP HTJ2K Referenced Deflate', 'Transfer Syntax', '', '', 'JPIPHTJ2KReferencedDeflate'),
    '1.2.840.10008.1.2.5': ('RLE Lossless', 'Transfer Syntax', '', '', 'RLELossless'),
    '1.2.840.10008.*******': ('RFC 2557 MIME encapsulation', 'Transfer Syntax', '', 'Retired', 'RFC2557MIMEEncapsulation'),
    '1.2.840.10008.*******': ('XML Encoding', 'Transfer Syntax', '', 'Retired', 'XMLEncoding'),
    '1.2.840.10008.*******': ('SMPTE ST 2110-20 Uncompressed Progressive Active Video', 'Transfer Syntax', '', '', 'SMPTEST211020UncompressedProgressiveActiveVideo'),
    '1.2.840.10008.*******': ('SMPTE ST 2110-20 Uncompressed Interlaced Active Video', 'Transfer Syntax', '', '', 'SMPTEST211020UncompressedInterlacedActiveVideo'),
    '1.2.840.10008.*******': ('SMPTE ST 2110-30 PCM Digital Audio', 'Transfer Syntax', '', '', 'SMPTEST211030PCMDigitalAudio'),
    '1.2.840.10008.*******': ('Deflated Image Frame Compression', 'Transfer Syntax', '', '', 'DeflatedImageFrameCompression'),
    '1.2.840.10008.1.3.10': ('Media Storage Directory Storage', 'SOP Class', '', '', 'MediaStorageDirectoryStorage'),
    '1.2.840.10008.1.5.1': ('Hot Iron Color Palette SOP Instance', 'Well-known SOP Instance', '', '', 'HotIronPalette'),
    '1.2.840.10008.1.5.2': ('PET Color Palette SOP Instance', 'Well-known SOP Instance', '', '', 'PETPalette'),
    '1.2.840.10008.1.5.3': ('Hot Metal Blue Color Palette SOP Instance', 'Well-known SOP Instance', '', '', 'HotMetalBluePalette'),
    '1.2.840.10008.1.5.4': ('PET 20 Step Color Palette SOP Instance', 'Well-known SOP Instance', '', '', 'PET20StepPalette'),
    '1.2.840.10008.1.5.5': ('Spring Color Palette SOP Instance', 'Well-known SOP Instance', '', '', 'SpringPalette'),
    '1.2.840.10008.1.5.6': ('Summer Color Palette SOP Instance', 'Well-known SOP Instance', '', '', 'SummerPalette'),
    '1.2.840.10008.1.5.7': ('Fall Color Palette SOP Instance', 'Well-known SOP Instance', '', '', 'FallPalette'),
    '1.2.840.10008.1.5.8': ('Winter Color Palette SOP Instance', 'Well-known SOP Instance', '', '', 'WinterPalette'),
    '1.2.840.10008.1.9': ('Basic Study Content Notification SOP Class', 'SOP Class', '', 'Retired', 'BasicStudyContentNotification'),
    '1.2.840.10008.1.20': ('Papyrus 3 Implicit VR Little Endian', 'Transfer Syntax', '(2015c)', 'Retired', 'Papyrus3ImplicitVRLittleEndian'),
    '1.2.840.10008.1.20.1': ('Storage Commitment Push Model SOP Class', 'SOP Class', '', '', 'StorageCommitmentPushModel'),
    '1.2.840.10008.1.20.1.1': ('Storage Commitment Push Model SOP Instance', 'Well-known SOP Instance', '', '', 'StorageCommitmentPushModelInstance'),
    '1.2.840.10008.1.20.2': ('Storage Commitment Pull Model SOP Class', 'SOP Class', '', 'Retired', 'StorageCommitmentPullModel'),
    '1.2.840.10008.1.20.2.1': ('Storage Commitment Pull Model SOP Instance', 'Well-known SOP Instance', '', 'Retired', 'StorageCommitmentPullModelInstance'),
    '1.2.840.10008.1.40': ('Procedural Event Logging SOP Class', 'SOP Class', '', '', 'ProceduralEventLogging'),
    '1.2.840.10008.1.40.1': ('Procedural Event Logging SOP Instance', 'Well-known SOP Instance', '', '', 'ProceduralEventLoggingInstance'),
    '1.2.840.10008.1.42': ('Substance Administration Logging SOP Class', 'SOP Class', '', '', 'SubstanceAdministrationLogging'),
    '1.2.840.10008.1.42.1': ('Substance Administration Logging SOP Instance', 'Well-known SOP Instance', '', '', 'SubstanceAdministrationLoggingInstance'),
    '1.2.840.10008.2.6.1': ('DICOM UID Registry', 'DICOM UIDs as a Coding Scheme', '', '', 'DCMUID'),
    '1.2.840.10008.2.16.4': ('DICOM Controlled Terminology', 'Coding Scheme', '', '', 'DCM'),
    '1.2.840.10008.2.16.5': ('Adult Mouse Anatomy Ontology', 'Coding Scheme', '', '', 'MA'),
    '1.2.840.10008.2.16.6': ('Uberon Ontology', 'Coding Scheme', '', '', 'UBERON'),
    '1.2.840.10008.2.16.7': ('Integrated Taxonomic Information System (ITIS) Taxonomic Serial Number (TSN)', 'Coding Scheme', '', '', 'ITIS_TSN'),
    '1.2.840.10008.2.16.8': ('Mouse Genome Initiative (MGI)', 'Coding Scheme', '', '', 'MGI'),
    '1.2.840.10008.2.16.9': ('PubChem Compound CID', 'Coding Scheme', '', '', 'PUBCHEM_CID'),
    '1.2.840.10008.2.16.10': ('Dublin Core', 'Coding Scheme', '', '', 'DC'),
    '1.2.840.10008.2.16.11': ('New York University Melanoma Clinical Cooperative Group', 'Coding Scheme', '', '', 'NYUMCCG'),
    '1.2.840.10008.2.16.12': ('Mayo Clinic Non-radiological Images Specific Body Structure Anatomical Surface Region Guide', 'Coding Scheme', '', '', 'MAYONRISBSASRG'),
    '1.2.840.10008.2.16.13': ('Image Biomarker Standardisation Initiative', 'Coding Scheme', '', '', 'IBSI'),
    '1.2.840.10008.2.16.14': ('Radiomics Ontology', 'Coding Scheme', '', '', 'RO'),
    '1.2.840.10008.2.16.15': ('RadElement', 'Coding Scheme', '', '', 'RADELEMENT'),
    '1.2.840.10008.2.16.16': ('ICD-11', 'Coding Scheme', '', '', 'I11'),
    '1.2.840.10008.2.16.17': ('Unified numbering system (UNS) for metals and alloys', 'Coding Scheme', '', '', 'UNS'),
    '1.2.840.10008.2.16.18': ('Research Resource Identification', 'Coding Scheme', '', '', 'RRID'),
    '1.2.840.10008.3.1.1.1': ('DICOM Application Context Name', 'Application Context Name', '', '', 'DICOMApplicationContext'),
    '1.2.840.10008.3.1.2.1.1': ('Detached Patient Management SOP Class', 'SOP Class', '', 'Retired', 'DetachedPatientManagement'),
    '1.2.840.10008.3.1.2.1.4': ('Detached Patient Management Meta SOP Class', 'Meta SOP Class', '', 'Retired', 'DetachedPatientManagementMeta'),
    '1.2.840.10008.3.1.2.2.1': ('Detached Visit Management SOP Class', 'SOP Class', '', 'Retired', 'DetachedVisitManagement'),
    '1.2.840.10008.3.1.2.3.1': ('Detached Study Management SOP Class', 'SOP Class', '', 'Retired', 'DetachedStudyManagement'),
    '1.2.840.10008.3.1.2.3.2': ('Study Component Management SOP Class', 'SOP Class', '', 'Retired', 'StudyComponentManagement'),
    '1.2.840.10008.3.1.2.3.3': ('Modality Performed Procedure Step SOP Class', 'SOP Class', '', '', 'ModalityPerformedProcedureStep'),
    '1.2.840.10008.3.1.2.3.4': ('Modality Performed Procedure Step Retrieve SOP Class', 'SOP Class', '', '', 'ModalityPerformedProcedureStepRetrieve'),
    '1.2.840.10008.3.1.2.3.5': ('Modality Performed Procedure Step Notification SOP Class', 'SOP Class', '', '', 'ModalityPerformedProcedureStepNotification'),
    '1.2.840.10008.3.1.2.5.1': ('Detached Results Management SOP Class', 'SOP Class', '', 'Retired', 'DetachedResultsManagement'),
    '1.2.840.10008.3.1.2.5.4': ('Detached Results Management Meta SOP Class', 'Meta SOP Class', '', 'Retired', 'DetachedResultsManagementMeta'),
    '1.2.840.10008.3.1.2.5.5': ('Detached Study Management Meta SOP Class', 'Meta SOP Class', '', 'Retired', 'DetachedStudyManagementMeta'),
    '1.2.840.10008.3.*******': ('Detached Interpretation Management SOP Class', 'SOP Class', '', 'Retired', 'DetachedInterpretationManagement'),
    '1.2.840.10008.4.2': ('Storage Service Class', 'Service Class', '', '', 'Storage'),
    '1.2.840.10008.5.1.1.1': ('Basic Film Session SOP Class', 'SOP Class', '', '', 'BasicFilmSession'),
    '1.2.840.10008.5.1.1.2': ('Basic Film Box SOP Class', 'SOP Class', '', '', 'BasicFilmBox'),
    '1.2.840.10008.5.1.1.4': ('Basic Grayscale Image Box SOP Class', 'SOP Class', '', '', 'BasicGrayscaleImageBox'),
    '1.2.840.10008.5.1.1.4.1': ('Basic Color Image Box SOP Class', 'SOP Class', '', '', 'BasicColorImageBox'),
    '1.2.840.10008.5.1.1.4.2': ('Referenced Image Box SOP Class', 'SOP Class', '', 'Retired', 'ReferencedImageBox'),
    '1.2.840.10008.5.1.1.9': ('Basic Grayscale Print Management Meta SOP Class', 'Meta SOP Class', '', '', 'BasicGrayscalePrintManagementMeta'),
    '1.2.840.10008.5.1.1.9.1': ('Referenced Grayscale Print Management Meta SOP Class', 'Meta SOP Class', '', 'Retired', 'ReferencedGrayscalePrintManagementMeta'),
    '1.2.840.10008.5.1.1.14': ('Print Job SOP Class', 'SOP Class', '', '', 'PrintJob'),
    '1.2.840.10008.5.1.1.15': ('Basic Annotation Box SOP Class', 'SOP Class', '', '', 'BasicAnnotationBox'),
    '1.2.840.10008.5.1.1.16': ('Printer SOP Class', 'SOP Class', '', '', 'Printer'),
    '1.2.840.10008.5.1.1.16.376': ('Printer Configuration Retrieval SOP Class', 'SOP Class', '', '', 'PrinterConfigurationRetrieval'),
    '1.2.840.10008.5.1.1.17': ('Printer SOP Instance', 'Well-known SOP Instance', '', '', 'PrinterInstance'),
    '1.2.840.10008.5.1.1.17.376': ('Printer Configuration Retrieval SOP Instance', 'Well-known SOP Instance', '', '', 'PrinterConfigurationRetrievalInstance'),
    '1.2.840.10008.5.1.1.18': ('Basic Color Print Management Meta SOP Class', 'Meta SOP Class', '', '', 'BasicColorPrintManagementMeta'),
    '1.2.840.10008.5.1.1.18.1': ('Referenced Color Print Management Meta SOP Class', 'Meta SOP Class', '', 'Retired', 'ReferencedColorPrintManagementMeta'),
    '1.2.840.10008.5.1.1.22': ('VOI LUT Box SOP Class', 'SOP Class', '', '', 'VOILUTBox'),
    '1.2.840.10008.5.1.1.23': ('Presentation LUT SOP Class', 'SOP Class', '', '', 'PresentationLUT'),
    '1.2.840.10008.5.1.1.24': ('Image Overlay Box SOP Class', 'SOP Class', '', 'Retired', 'ImageOverlayBox'),
    '1.2.840.10008.5.1.1.24.1': ('Basic Print Image Overlay Box SOP Class', 'SOP Class', '', 'Retired', 'BasicPrintImageOverlayBox'),
    '1.2.840.10008.5.1.1.25': ('Print Queue SOP Instance', 'Well-known SOP Instance', '', 'Retired', 'PrintQueueInstance'),
    '1.2.840.10008.5.1.1.26': ('Print Queue Management SOP Class', 'SOP Class', '', 'Retired', 'PrintQueueManagement'),
    '1.2.840.10008.5.1.1.27': ('Stored Print Storage SOP Class', 'SOP Class', '', 'Retired', 'StoredPrintStorage'),
    '1.2.840.10008.5.1.1.29': ('Hardcopy Grayscale Image Storage SOP Class', 'SOP Class', '', 'Retired', 'HardcopyGrayscaleImageStorage'),
    '1.2.840.10008.5.1.1.30': ('Hardcopy Color Image Storage SOP Class', 'SOP Class', '', 'Retired', 'HardcopyColorImageStorage'),
    '1.2.840.10008.5.1.1.31': ('Pull Print Request SOP Class', 'SOP Class', '', 'Retired', 'PullPrintRequest'),
    '1.2.840.10008.5.1.1.32': ('Pull Stored Print Management Meta SOP Class', 'Meta SOP Class', '', 'Retired', 'PullStoredPrintManagementMeta'),
    '1.2.840.10008.5.1.1.33': ('Media Creation Management SOP Class UID', 'SOP Class', '', '', 'MediaCreationManagement'),
    '1.2.840.10008.5.1.1.40': ('Display System SOP Class', 'SOP Class', '', '', 'DisplaySystem'),
    '1.2.840.10008.5.1.1.40.1': ('Display System SOP Instance', 'Well-known SOP Instance', '', '', 'DisplaySystemInstance'),
    '1.2.840.10008.*******.1.1': ('Computed Radiography Image Storage', 'SOP Class', '', '', 'ComputedRadiographyImageStorage'),
    '1.2.840.10008.*******.1.1.1': ('Digital X-Ray Image Storage - For Presentation', 'SOP Class', '', '', 'DigitalXRayImageStorageForPresentation'),
    '1.2.840.10008.*******.1.1.1.1': ('Digital X-Ray Image Storage - For Processing', 'SOP Class', '', '', 'DigitalXRayImageStorageForProcessing'),
    '1.2.840.10008.*******.1.1.2': ('Digital Mammography X-Ray Image Storage - For Presentation', 'SOP Class', '', '', 'DigitalMammographyXRayImageStorageForPresentation'),
    '1.2.840.10008.*******.1.1.2.1': ('Digital Mammography X-Ray Image Storage - For Processing', 'SOP Class', '', '', 'DigitalMammographyXRayImageStorageForProcessing'),
    '1.2.840.10008.*******.1.1.3': ('Digital Intra-Oral X-Ray Image Storage - For Presentation', 'SOP Class', '', '', 'DigitalIntraOralXRayImageStorageForPresentation'),
    '1.2.840.10008.*******.1.1.3.1': ('Digital Intra-Oral X-Ray Image Storage - For Processing', 'SOP Class', '', '', 'DigitalIntraOralXRayImageStorageForProcessing'),
    '1.2.840.10008.*******.1.2': ('CT Image Storage', 'SOP Class', '', '', 'CTImageStorage'),
    '1.2.840.10008.*******.1.2.1': ('Enhanced CT Image Storage', 'SOP Class', '', '', 'EnhancedCTImageStorage'),
    '1.2.840.10008.*******.1.2.2': ('Legacy Converted Enhanced CT Image Storage', 'SOP Class', '', '', 'LegacyConvertedEnhancedCTImageStorage'),
    '1.2.840.10008.*******.1.3': ('Ultrasound Multi-frame Image Storage', 'SOP Class', '', 'Retired', 'UltrasoundMultiFrameImageStorageRetired'),
    '1.2.840.10008.*******.1.3.1': ('Ultrasound Multi-frame Image Storage', 'SOP Class', '', '', 'UltrasoundMultiFrameImageStorage'),
    '1.2.840.10008.*******.1.4': ('MR Image Storage', 'SOP Class', '', '', 'MRImageStorage'),
    '1.2.840.10008.*******.1.4.1': ('Enhanced MR Image Storage', 'SOP Class', '', '', 'EnhancedMRImageStorage'),
    '1.2.840.10008.*******.1.4.2': ('MR Spectroscopy Storage', 'SOP Class', '', '', 'MRSpectroscopyStorage'),
    '1.2.840.10008.*******.1.4.3': ('Enhanced MR Color Image Storage', 'SOP Class', '', '', 'EnhancedMRColorImageStorage'),
    '1.2.840.10008.*******.1.4.4': ('Legacy Converted Enhanced MR Image Storage', 'SOP Class', '', '', 'LegacyConvertedEnhancedMRImageStorage'),
    '1.2.840.10008.*******.1.5': ('Nuclear Medicine Image Storage', 'SOP Class', '', 'Retired', 'NuclearMedicineImageStorageRetired'),
    '1.2.840.10008.*******.1.6': ('Ultrasound Image Storage', 'SOP Class', '', 'Retired', 'UltrasoundImageStorageRetired'),
    '1.2.840.10008.*******.1.6.1': ('Ultrasound Image Storage', 'SOP Class', '', '', 'UltrasoundImageStorage'),
    '1.2.840.10008.*******.1.6.2': ('Enhanced US Volume Storage', 'SOP Class', '', '', 'EnhancedUSVolumeStorage'),
    '1.2.840.10008.*******.1.6.3': ('Photoacoustic Image Storage', 'SOP Class', '', '', 'PhotoacousticImageStorage'),
    '1.2.840.10008.*******.1.7': ('Secondary Capture Image Storage', 'SOP Class', '', '', 'SecondaryCaptureImageStorage'),
    '1.2.840.10008.*******.1.7.1': ('Multi-frame Single Bit Secondary Capture Image Storage', 'SOP Class', '', '', 'MultiFrameSingleBitSecondaryCaptureImageStorage'),
    '1.2.840.10008.*******.1.7.2': ('Multi-frame Grayscale Byte Secondary Capture Image Storage', 'SOP Class', '', '', 'MultiFrameGrayscaleByteSecondaryCaptureImageStorage'),
    '1.2.840.10008.*******.1.7.3': ('Multi-frame Grayscale Word Secondary Capture Image Storage', 'SOP Class', '', '', 'MultiFrameGrayscaleWordSecondaryCaptureImageStorage'),
    '1.2.840.10008.*******.1.7.4': ('Multi-frame True Color Secondary Capture Image Storage', 'SOP Class', '', '', 'MultiFrameTrueColorSecondaryCaptureImageStorage'),
    '1.2.840.10008.*******.1.8': ('Standalone Overlay Storage', 'SOP Class', '', 'Retired', 'StandaloneOverlayStorage'),
    '1.2.840.10008.*******.1.9': ('Standalone Curve Storage', 'SOP Class', '', 'Retired', 'StandaloneCurveStorage'),
    '1.2.840.10008.*******.1.9.1': ('Waveform Storage - Trial', 'SOP Class', '', 'Retired', 'WaveformStorageTrial'),
    '1.2.840.10008.*******.1.9.1.1': ('12-lead ECG Waveform Storage', 'SOP Class', '', '', 'TwelveLeadECGWaveformStorage'),
    '1.2.840.10008.*******.1.9.1.2': ('General ECG Waveform Storage', 'SOP Class', '', '', 'GeneralECGWaveformStorage'),
    '1.2.840.10008.*******.1.9.1.3': ('Ambulatory ECG Waveform Storage', 'SOP Class', '', '', 'AmbulatoryECGWaveformStorage'),
    '1.2.840.10008.*******.1.9.1.4': ('General 32-bit ECG Waveform Storage', 'SOP Class', '', '', 'General32bitECGWaveformStorage'),
    '1.2.840.10008.*******.1.9.2.1': ('Hemodynamic Waveform Storage', 'SOP Class', '', '', 'HemodynamicWaveformStorage'),
    '1.2.840.10008.*******.1.9.3.1': ('Cardiac Electrophysiology Waveform Storage', 'SOP Class', '', '', 'CardiacElectrophysiologyWaveformStorage'),
    '1.2.840.10008.*******.1.9.4.1': ('Basic Voice Audio Waveform Storage', 'SOP Class', '', '', 'BasicVoiceAudioWaveformStorage'),
    '1.2.840.10008.*******.1.9.4.2': ('General Audio Waveform Storage', 'SOP Class', '', '', 'GeneralAudioWaveformStorage'),
    '1.2.840.10008.*******.1.9.5.1': ('Arterial Pulse Waveform Storage', 'SOP Class', '', '', 'ArterialPulseWaveformStorage'),
    '1.2.840.10008.*******.1.9.6.1': ('Respiratory Waveform Storage', 'SOP Class', '', '', 'RespiratoryWaveformStorage'),
    '1.2.840.10008.*******.1.9.6.2': ('Multi-channel Respiratory Waveform Storage', 'SOP Class', '', '', 'MultichannelRespiratoryWaveformStorage'),
    '1.2.840.10008.*******.1.9.7.1': ('Routine Scalp Electroencephalogram Waveform Storage', 'SOP Class', '', '', 'RoutineScalpElectroencephalogramWaveformStorage'),
    '1.2.840.10008.*******.1.9.7.2': ('Electromyogram Waveform Storage', 'SOP Class', '', '', 'ElectromyogramWaveformStorage'),
    '1.2.840.10008.*******.1.9.7.3': ('Electrooculogram Waveform Storage', 'SOP Class', '', '', 'ElectrooculogramWaveformStorage'),
    '1.2.840.10008.*******.1.9.7.4': ('Sleep Electroencephalogram Waveform Storage', 'SOP Class', '', '', 'SleepElectroencephalogramWaveformStorage'),
    '1.2.840.10008.*******.1.9.8.1': ('Body Position Waveform Storage', 'SOP Class', '', '', 'BodyPositionWaveformStorage'),
    '1.2.840.10008.*******.1.10': ('Standalone Modality LUT Storage', 'SOP Class', '', 'Retired', 'StandaloneModalityLUTStorage'),
    '1.2.840.10008.*******.1.11': ('Standalone VOI LUT Storage', 'SOP Class', '', 'Retired', 'StandaloneVOILUTStorage'),
    '1.2.840.10008.*******.1.11.1': ('Grayscale Softcopy Presentation State Storage', 'SOP Class', '', '', 'GrayscaleSoftcopyPresentationStateStorage'),
    '1.2.840.10008.*******.1.11.2': ('Color Softcopy Presentation State Storage', 'SOP Class', '', '', 'ColorSoftcopyPresentationStateStorage'),
    '1.2.840.10008.*******.1.11.3': ('Pseudo-Color Softcopy Presentation State Storage', 'SOP Class', '', '', 'PseudoColorSoftcopyPresentationStateStorage'),
    '1.2.840.10008.*******.1.11.4': ('Blending Softcopy Presentation State Storage', 'SOP Class', '', '', 'BlendingSoftcopyPresentationStateStorage'),
    '1.2.840.10008.*******.1.11.5': ('XA/XRF Grayscale Softcopy Presentation State Storage', 'SOP Class', '', '', 'XAXRFGrayscaleSoftcopyPresentationStateStorage'),
    '1.2.840.10008.*******.1.11.6': ('Grayscale Planar MPR Volumetric Presentation State Storage', 'SOP Class', '', '', 'GrayscalePlanarMPRVolumetricPresentationStateStorage'),
    '1.2.840.10008.*******.1.11.7': ('Compositing Planar MPR Volumetric Presentation State Storage', 'SOP Class', '', '', 'CompositingPlanarMPRVolumetricPresentationStateStorage'),
    '1.2.840.10008.*******.1.11.8': ('Advanced Blending Presentation State Storage', 'SOP Class', '', '', 'AdvancedBlendingPresentationStateStorage'),
    '1.2.840.10008.*******.1.11.9': ('Volume Rendering Volumetric Presentation State Storage', 'SOP Class', '', '', 'VolumeRenderingVolumetricPresentationStateStorage'),
    '1.2.840.10008.*******.1.11.10': ('Segmented Volume Rendering Volumetric Presentation State Storage', 'SOP Class', '', '', 'SegmentedVolumeRenderingVolumetricPresentationStateStorage'),
    '1.2.840.10008.*******.1.11.11': ('Multiple Volume Rendering Volumetric Presentation State Storage', 'SOP Class', '', '', 'MultipleVolumeRenderingVolumetricPresentationStateStorage'),
    '1.2.840.10008.*******.1.11.12': ('Variable Modality LUT Softcopy Presentation State Storage', 'SOP Class', '', '', 'VariableModalityLUTSoftcopyPresentationStateStorage'),
    '1.2.840.10008.*******.1.12.1': ('X-Ray Angiographic Image Storage', 'SOP Class', '', '', 'XRayAngiographicImageStorage'),
    '1.2.840.10008.*******.********': ('Enhanced XA Image Storage', 'SOP Class', '', '', 'EnhancedXAImageStorage'),
    '1.2.840.10008.*******.1.12.2': ('X-Ray Radiofluoroscopic Image Storage', 'SOP Class', '', '', 'XRayRadiofluoroscopicImageStorage'),
    '1.2.840.10008.*******.********': ('Enhanced XRF Image Storage', 'SOP Class', '', '', 'EnhancedXRFImageStorage'),
    '1.2.840.10008.*******.1.12.3': ('X-Ray Angiographic Bi-Plane Image Storage', 'SOP Class', '', 'Retired', 'XRayAngiographicBiPlaneImageStorage'),
    '1.2.840.10008.*******.1.12.77': ('', 'SOP Class', '(2015c)', 'Retired', ''),
    '1.2.840.10008.*******.********': ('X-Ray 3D Angiographic Image Storage', 'SOP Class', '', '', 'XRay3DAngiographicImageStorage'),
    '1.2.840.10008.*******.********': ('X-Ray 3D Craniofacial Image Storage', 'SOP Class', '', '', 'XRay3DCraniofacialImageStorage'),
    '1.2.840.10008.*******.********': ('Breast Tomosynthesis Image Storage', 'SOP Class', '', '', 'BreastTomosynthesisImageStorage'),
    '1.2.840.10008.*******.********': ('Breast Projection X-Ray Image Storage - For Presentation', 'SOP Class', '', '', 'BreastProjectionXRayImageStorageForPresentation'),
    '1.2.840.10008.*******.********': ('Breast Projection X-Ray Image Storage - For Processing', 'SOP Class', '', '', 'BreastProjectionXRayImageStorageForProcessing'),
    '1.2.840.10008.*******.1.14.1': ('Intravascular Optical Coherence Tomography Image Storage - For Presentation', 'SOP Class', '', '', 'IntravascularOpticalCoherenceTomographyImageStorageForPresentation'),
    '1.2.840.10008.*******.1.14.2': ('Intravascular Optical Coherence Tomography Image Storage - For Processing', 'SOP Class', '', '', 'IntravascularOpticalCoherenceTomographyImageStorageForProcessing'),
    '1.2.840.10008.*******.1.20': ('Nuclear Medicine Image Storage', 'SOP Class', '', '', 'NuclearMedicineImageStorage'),
    '1.2.840.10008.*******.1.30': ('Parametric Map Storage', 'SOP Class', '', '', 'ParametricMapStorage'),
    '1.2.840.10008.*******.1.40': ('', 'SOP Class', '(2015c)', 'Retired', ''),
    '1.2.840.10008.*******.1.66': ('Raw Data Storage', 'SOP Class', '', '', 'RawDataStorage'),
    '1.2.840.10008.*******.1.66.1': ('Spatial Registration Storage', 'SOP Class', '', '', 'SpatialRegistrationStorage'),
    '1.2.840.10008.*******.1.66.2': ('Spatial Fiducials Storage', 'SOP Class', '', '', 'SpatialFiducialsStorage'),
    '1.2.840.10008.*******.1.66.3': ('Deformable Spatial Registration Storage', 'SOP Class', '', '', 'DeformableSpatialRegistrationStorage'),
    '1.2.840.10008.*******.1.66.4': ('Segmentation Storage', 'SOP Class', '', '', 'SegmentationStorage'),
    '1.2.840.10008.*******.1.66.5': ('Surface Segmentation Storage', 'SOP Class', '', '', 'SurfaceSegmentationStorage'),
    '1.2.840.10008.*******.1.66.6': ('Tractography Results Storage', 'SOP Class', '', '', 'TractographyResultsStorage'),
    '1.2.840.10008.*******.1.66.7': ('Label Map Segmentation Storage', 'SOP Class', '', '', 'LabelMapSegmentationStorage'),
    '1.2.840.10008.*******.1.66.8': ('Height Map Segmentation Storage', 'SOP Class', '', '', 'HeightMapSegmentationStorage'),
    '1.2.840.10008.*******.1.67': ('Real World Value Mapping Storage', 'SOP Class', '', '', 'RealWorldValueMappingStorage'),
    '1.2.840.10008.*******.1.68.1': ('Surface Scan Mesh Storage', 'SOP Class', '', '', 'SurfaceScanMeshStorage'),
    '1.2.840.10008.*******.1.68.2': ('Surface Scan Point Cloud Storage', 'SOP Class', '', '', 'SurfaceScanPointCloudStorage'),
    '1.2.840.10008.*******.1.77.1': ('VL Image Storage - Trial', 'SOP Class', '', 'Retired', 'VLImageStorageTrial'),
    '1.2.840.10008.*******.1.77.2': ('VL Multi-frame Image Storage - Trial', 'SOP Class', '', 'Retired', 'VLMultiFrameImageStorageTrial'),
    '1.2.840.10008.*******.1.77.1.1': ('VL Endoscopic Image Storage', 'SOP Class', '', '', 'VLEndoscopicImageStorage'),
    '1.2.840.10008.*******.1.77.1.1.1': ('Video Endoscopic Image Storage', 'SOP Class', '', '', 'VideoEndoscopicImageStorage'),
    '1.2.840.10008.*******.1.77.1.2': ('VL Microscopic Image Storage', 'SOP Class', '', '', 'VLMicroscopicImageStorage'),
    '1.2.840.10008.*******.1.77.1.2.1': ('Video Microscopic Image Storage', 'SOP Class', '', '', 'VideoMicroscopicImageStorage'),
    '1.2.840.10008.*******.1.77.1.3': ('VL Slide-Coordinates Microscopic Image Storage', 'SOP Class', '', '', 'VLSlideCoordinatesMicroscopicImageStorage'),
    '1.2.840.10008.*******.********': ('VL Photographic Image Storage', 'SOP Class', '', '', 'VLPhotographicImageStorage'),
    '1.2.840.10008.*******.********.1': ('Video Photographic Image Storage', 'SOP Class', '', '', 'VideoPhotographicImageStorage'),
    '1.2.840.10008.*******.********.1': ('Ophthalmic Photography 8 Bit Image Storage', 'SOP Class', '', '', 'OphthalmicPhotography8BitImageStorage'),
    '1.2.840.10008.*******.********.2': ('Ophthalmic Photography 16 Bit Image Storage', 'SOP Class', '', '', 'OphthalmicPhotography16BitImageStorage'),
    '1.2.840.10008.*******.********.3': ('Stereometric Relationship Storage', 'SOP Class', '', '', 'StereometricRelationshipStorage'),
    '1.2.840.10008.*******.********.4': ('Ophthalmic Tomography Image Storage', 'SOP Class', '', '', 'OphthalmicTomographyImageStorage'),
    '1.2.840.10008.*******.********.5': ('Wide Field Ophthalmic Photography Stereographic Projection Image Storage', 'SOP Class', '', '', 'WideFieldOphthalmicPhotographyStereographicProjectionImageStorage'),
    '1.2.840.10008.*******.********.6': ('Wide Field Ophthalmic Photography 3D Coordinates Image Storage', 'SOP Class', '', '', 'WideFieldOphthalmicPhotography3DCoordinatesImageStorage'),
    '1.2.840.10008.*******.********.7': ('Ophthalmic Optical Coherence Tomography En Face Image Storage', 'SOP Class', '', '', 'OphthalmicOpticalCoherenceTomographyEnFaceImageStorage'),
    '1.2.840.10008.*******.********.8': ('Ophthalmic Optical Coherence Tomography B-scan Volume Analysis Storage', 'SOP Class', '', '', 'OphthalmicOpticalCoherenceTomographyBscanVolumeAnalysisStorage'),
    '1.2.840.10008.*******.********': ('VL Whole Slide Microscopy Image Storage', 'SOP Class', '', '', 'VLWholeSlideMicroscopyImageStorage'),
    '1.2.840.10008.*******.1.77.1.7': ('Dermoscopic Photography Image Storage', 'SOP Class', '', '', 'DermoscopicPhotographyImageStorage'),
    '1.2.840.10008.*******.1.77.1.8': ('Confocal Microscopy Image Storage', 'SOP Class', '', '', 'ConfocalMicroscopyImageStorage'),
    '1.2.840.10008.*******.1.77.1.9': ('Confocal Microscopy Tiled Pyramidal Image Storage', 'SOP Class', '', '', 'ConfocalMicroscopyTiledPyramidalImageStorage'),
    '1.2.840.10008.*******.1.78.1': ('Lensometry Measurements Storage', 'SOP Class', '', '', 'LensometryMeasurementsStorage'),
    '1.2.840.10008.*******.1.78.2': ('Autorefraction Measurements Storage', 'SOP Class', '', '', 'AutorefractionMeasurementsStorage'),
    '1.2.840.10008.*******.1.78.3': ('Keratometry Measurements Storage', 'SOP Class', '', '', 'KeratometryMeasurementsStorage'),
    '1.2.840.10008.*******.1.78.4': ('Subjective Refraction Measurements Storage', 'SOP Class', '', '', 'SubjectiveRefractionMeasurementsStorage'),
    '1.2.840.10008.*******.1.78.5': ('Visual Acuity Measurements Storage', 'SOP Class', '', '', 'VisualAcuityMeasurementsStorage'),
    '1.2.840.10008.*******.1.78.6': ('Spectacle Prescription Report Storage', 'SOP Class', '', '', 'SpectaclePrescriptionReportStorage'),
    '1.2.840.10008.*******.1.78.7': ('Ophthalmic Axial Measurements Storage', 'SOP Class', '', '', 'OphthalmicAxialMeasurementsStorage'),
    '1.2.840.10008.*******.1.78.8': ('Intraocular Lens Calculations Storage', 'SOP Class', '', '', 'IntraocularLensCalculationsStorage'),
    '1.2.840.10008.*******.1.79.1': ('Macular Grid Thickness and Volume Report Storage', 'SOP Class', '', '', 'MacularGridThicknessAndVolumeReportStorage'),
    '1.2.840.10008.*******.1.80.1': ('Ophthalmic Visual Field Static Perimetry Measurements Storage', 'SOP Class', '', '', 'OphthalmicVisualFieldStaticPerimetryMeasurementsStorage'),
    '1.2.840.10008.*******.1.81.1': ('Ophthalmic Thickness Map Storage', 'SOP Class', '', '', 'OphthalmicThicknessMapStorage'),
    '1.2.840.10008.*******.1.82.1': ('Corneal Topography Map Storage', 'SOP Class', '', '', 'CornealTopographyMapStorage'),
    '1.2.840.10008.*******.1.88.1': ('Text SR Storage - Trial', 'SOP Class', '', 'Retired', 'TextSRStorageTrial'),
    '1.2.840.10008.*******.1.88.2': ('Audio SR Storage - Trial', 'SOP Class', '', 'Retired', 'AudioSRStorageTrial'),
    '1.2.840.10008.*******.1.88.3': ('Detail SR Storage - Trial', 'SOP Class', '', 'Retired', 'DetailSRStorageTrial'),
    '1.2.840.10008.*******.1.88.4': ('Comprehensive SR Storage - Trial', 'SOP Class', '', 'Retired', 'ComprehensiveSRStorageTrial'),
    '1.2.840.10008.*******.1.88.11': ('Basic Text SR Storage', 'SOP Class', '', '', 'BasicTextSRStorage'),
    '1.2.840.10008.*******.1.88.22': ('Enhanced SR Storage', 'SOP Class', '', '', 'EnhancedSRStorage'),
    '1.2.840.10008.*******.1.88.33': ('Comprehensive SR Storage', 'SOP Class', '', '', 'ComprehensiveSRStorage'),
    '1.2.840.10008.*******.1.88.34': ('Comprehensive 3D SR Storage', 'SOP Class', '', '', 'Comprehensive3DSRStorage'),
    '1.2.840.10008.*******.1.88.35': ('Extensible SR Storage', 'SOP Class', '', '', 'ExtensibleSRStorage'),
    '1.2.840.10008.*******.1.88.40': ('Procedure Log Storage', 'SOP Class', '', '', 'ProcedureLogStorage'),
    '1.2.840.10008.*******.1.88.50': ('Mammography CAD SR Storage', 'SOP Class', '', '', 'MammographyCADSRStorage'),
    '1.2.840.10008.*******.1.88.59': ('Key Object Selection Document Storage', 'SOP Class', '', '', 'KeyObjectSelectionDocumentStorage'),
    '1.2.840.10008.*******.1.88.65': ('Chest CAD SR Storage', 'SOP Class', '', '', 'ChestCADSRStorage'),
    '1.2.840.10008.*******.1.88.67': ('X-Ray Radiation Dose SR Storage', 'SOP Class', '', '', 'XRayRadiationDoseSRStorage'),
    '1.2.840.10008.*******.1.88.68': ('Radiopharmaceutical Radiation Dose SR Storage', 'SOP Class', '', '', 'RadiopharmaceuticalRadiationDoseSRStorage'),
    '1.2.840.10008.*******.1.88.69': ('Colon CAD SR Storage', 'SOP Class', '', '', 'ColonCADSRStorage'),
    '1.2.840.10008.*******.1.88.70': ('Implantation Plan SR Storage', 'SOP Class', '', '', 'ImplantationPlanSRStorage'),
    '1.2.840.10008.*******.1.88.71': ('Acquisition Context SR Storage', 'SOP Class', '', '', 'AcquisitionContextSRStorage'),
    '1.2.840.10008.*******.1.88.72': ('Simplified Adult Echo SR Storage', 'SOP Class', '', '', 'SimplifiedAdultEchoSRStorage'),
    '1.2.840.10008.*******.1.88.73': ('Patient Radiation Dose SR Storage', 'SOP Class', '', '', 'PatientRadiationDoseSRStorage'),
    '1.2.840.10008.*******.1.88.74': ('Planned Imaging Agent Administration SR Storage', 'SOP Class', '', '', 'PlannedImagingAgentAdministrationSRStorage'),
    '1.2.840.10008.*******.1.88.75': ('Performed Imaging Agent Administration SR Storage', 'SOP Class', '', '', 'PerformedImagingAgentAdministrationSRStorage'),
    '1.2.840.10008.*******.1.88.76': ('Enhanced X-Ray Radiation Dose SR Storage', 'SOP Class', '', '', 'EnhancedXRayRadiationDoseSRStorage'),
    '1.2.840.10008.*******.1.88.77': ('Waveform Annotation SR Storage', 'SOP Class', '', '', 'WaveformAnnotationSRStorage'),
    '1.2.840.10008.*******.1.90.1': ('Content Assessment Results Storage', 'SOP Class', '', '', 'ContentAssessmentResultsStorage'),
    '1.2.840.10008.*******.1.91.1': ('Microscopy Bulk Simple Annotations Storage', 'SOP Class', '', '', 'MicroscopyBulkSimpleAnnotationsStorage'),
    '1.2.840.10008.*******.1.104.1': ('Encapsulated PDF Storage', 'SOP Class', '', '', 'EncapsulatedPDFStorage'),
    '1.2.840.10008.*******.1.104.2': ('Encapsulated CDA Storage', 'SOP Class', '', '', 'EncapsulatedCDAStorage'),
    '1.2.840.10008.*******.1.104.3': ('Encapsulated STL Storage', 'SOP Class', '', '', 'EncapsulatedSTLStorage'),
    '1.2.840.10008.*******.1.104.4': ('Encapsulated OBJ Storage', 'SOP Class', '', '', 'EncapsulatedOBJStorage'),
    '1.2.840.10008.*******.1.104.5': ('Encapsulated MTL Storage', 'SOP Class', '', '', 'EncapsulatedMTLStorage'),
    '1.2.840.10008.*******.1.128': ('Positron Emission Tomography Image Storage', 'SOP Class', '', '', 'PositronEmissionTomographyImageStorage'),
    '1.2.840.10008.*******.1.128.1': ('Legacy Converted Enhanced PET Image Storage', 'SOP Class', '', '', 'LegacyConvertedEnhancedPETImageStorage'),
    '1.2.840.10008.*******.1.129': ('Standalone PET Curve Storage', 'SOP Class', '', 'Retired', 'StandalonePETCurveStorage'),
    '1.2.840.10008.*******.1.130': ('Enhanced PET Image Storage', 'SOP Class', '', '', 'EnhancedPETImageStorage'),
    '1.2.840.10008.*******.1.131': ('Basic Structured Display Storage', 'SOP Class', '', '', 'BasicStructuredDisplayStorage'),
    '1.2.840.10008.*******.1.200.1': ('CT Defined Procedure Protocol Storage', 'SOP Class', '', '', 'CTDefinedProcedureProtocolStorage'),
    '1.2.840.10008.*******.1.200.2': ('CT Performed Procedure Protocol Storage', 'SOP Class', '', '', 'CTPerformedProcedureProtocolStorage'),
    '1.2.840.10008.*******.1.200.3': ('Protocol Approval Storage', 'SOP Class', '', '', 'ProtocolApprovalStorage'),
    '1.2.840.10008.*******.1.200.4': ('Protocol Approval Information Model - FIND', 'SOP Class', '', '', 'ProtocolApprovalInformationModelFind'),
    '1.2.840.10008.*******.1.200.5': ('Protocol Approval Information Model - MOVE', 'SOP Class', '', '', 'ProtocolApprovalInformationModelMove'),
    '1.2.840.10008.*******.1.200.6': ('Protocol Approval Information Model - GET', 'SOP Class', '', '', 'ProtocolApprovalInformationModelGet'),
    '1.2.840.10008.*******.1.200.7': ('XA Defined Procedure Protocol Storage', 'SOP Class', '', '', 'XADefinedProcedureProtocolStorage'),
    '1.2.840.10008.*******.1.200.8': ('XA Performed Procedure Protocol Storage', 'SOP Class', '', '', 'XAPerformedProcedureProtocolStorage'),
    '1.2.840.10008.*******.1.201.1': ('Inventory Storage', 'SOP Class', '', '', 'InventoryStorage'),
    '1.2.840.10008.*******.1.201.2': ('Inventory - FIND', 'SOP Class', '', '', 'InventoryFind'),
    '1.2.840.10008.*******.1.201.3': ('Inventory - MOVE', 'SOP Class', '', '', 'InventoryMove'),
    '1.2.840.10008.*******.1.201.4': ('Inventory - GET', 'SOP Class', '', '', 'InventoryGet'),
    '1.2.840.10008.*******.1.201.5': ('Inventory Creation', 'SOP Class', '', '', 'InventoryCreation'),
    '1.2.840.10008.*******.1.201.6': ('Repository Query', 'SOP Class', '', '', 'RepositoryQuery'),
    '1.2.840.10008.*******.1.201.1.1': ('Storage Management SOP Instance', 'Well-known SOP Instance', '', '', 'StorageManagementInstance'),
    '1.2.840.10008.*******.1.481.1': ('RT Image Storage', 'SOP Class', '', '', 'RTImageStorage'),
    '1.2.840.10008.*******.1.481.2': ('RT Dose Storage', 'SOP Class', '', '', 'RTDoseStorage'),
    '1.2.840.10008.*******.1.481.3': ('RT Structure Set Storage', 'SOP Class', '', '', 'RTStructureSetStorage'),
    '1.2.840.10008.*******.1.481.4': ('RT Beams Treatment Record Storage', 'SOP Class', '', '', 'RTBeamsTreatmentRecordStorage'),
    '1.2.840.10008.*******.1.481.5': ('RT Plan Storage', 'SOP Class', '', '', 'RTPlanStorage'),
    '1.2.840.10008.*******.1.481.6': ('RT Brachy Treatment Record Storage', 'SOP Class', '', '', 'RTBrachyTreatmentRecordStorage'),
    '1.2.840.10008.*******.1.481.7': ('RT Treatment Summary Record Storage', 'SOP Class', '', '', 'RTTreatmentSummaryRecordStorage'),
    '1.2.840.10008.*******.1.481.8': ('RT Ion Plan Storage', 'SOP Class', '', '', 'RTIonPlanStorage'),
    '1.2.840.10008.*******.1.481.9': ('RT Ion Beams Treatment Record Storage', 'SOP Class', '', '', 'RTIonBeamsTreatmentRecordStorage'),
    '1.2.840.10008.*******.1.481.10': ('RT Physician Intent Storage', 'SOP Class', '', '', 'RTPhysicianIntentStorage'),
    '1.2.840.10008.*******.1.481.11': ('RT Segment Annotation Storage', 'SOP Class', '', '', 'RTSegmentAnnotationStorage'),
    '1.2.840.10008.*******.1.481.12': ('RT Radiation Set Storage', 'SOP Class', '', '', 'RTRadiationSetStorage'),
    '1.2.840.10008.*******.1.481.13': ('C-Arm Photon-Electron Radiation Storage', 'SOP Class', '', '', 'CArmPhotonElectronRadiationStorage'),
    '1.2.840.10008.*******.1.481.14': ('Tomotherapeutic Radiation Storage', 'SOP Class', '', '', 'TomotherapeuticRadiationStorage'),
    '1.2.840.10008.*******.1.481.15': ('Robotic-Arm Radiation Storage', 'SOP Class', '', '', 'RoboticArmRadiationStorage'),
    '1.2.840.10008.*******.1.481.16': ('RT Radiation Record Set Storage', 'SOP Class', '', '', 'RTRadiationRecordSetStorage'),
    '1.2.840.10008.*******.1.481.17': ('RT Radiation Salvage Record Storage', 'SOP Class', '', '', 'RTRadiationSalvageRecordStorage'),
    '1.2.840.10008.*******.1.481.18': ('Tomotherapeutic Radiation Record Storage', 'SOP Class', '', '', 'TomotherapeuticRadiationRecordStorage'),
    '1.2.840.10008.*******.1.481.19': ('C-Arm Photon-Electron Radiation Record Storage', 'SOP Class', '', '', 'CArmPhotonElectronRadiationRecordStorage'),
    '1.2.840.10008.*******.1.481.20': ('Robotic Radiation Record Storage', 'SOP Class', '', '', 'RoboticRadiationRecordStorage'),
    '1.2.840.10008.*******.1.481.21': ('RT Radiation Set Delivery Instruction Storage', 'SOP Class', '', '', 'RTRadiationSetDeliveryInstructionStorage'),
    '1.2.840.10008.*******.1.481.22': ('RT Treatment Preparation Storage', 'SOP Class', '', '', 'RTTreatmentPreparationStorage'),
    '1.2.840.10008.*******.1.481.23': ('Enhanced RT Image Storage', 'SOP Class', '', '', 'EnhancedRTImageStorage'),
    '1.2.840.10008.*******.1.481.24': ('Enhanced Continuous RT Image Storage', 'SOP Class', '', '', 'EnhancedContinuousRTImageStorage'),
    '1.2.840.10008.*******.1.481.25': ('RT Patient Position Acquisition Instruction Storage', 'SOP Class', '', '', 'RTPatientPositionAcquisitionInstructionStorage'),
    '1.2.840.10008.*******.1.501.1': ('DICOS CT Image Storage', 'SOP Class', 'DICOS', '', 'DICOSCTImageStorage'),
    '1.2.840.10008.*******.1.501.2.1': ('DICOS Digital X-Ray Image Storage - For Presentation', 'SOP Class', 'DICOS', '', 'DICOSDigitalXRayImageStorageForPresentation'),
    '1.2.840.10008.*******.1.501.2.2': ('DICOS Digital X-Ray Image Storage - For Processing', 'SOP Class', 'DICOS', '', 'DICOSDigitalXRayImageStorageForProcessing'),
    '1.2.840.10008.*******.1.501.3': ('DICOS Threat Detection Report Storage', 'SOP Class', 'DICOS', '', 'DICOSThreatDetectionReportStorage'),
    '1.2.840.10008.*******.1.501.4': ('DICOS 2D AIT Storage', 'SOP Class', 'DICOS', '', 'DICOS2DAITStorage'),
    '1.2.840.10008.*******.1.501.5': ('DICOS 3D AIT Storage', 'SOP Class', 'DICOS', '', 'DICOS3DAITStorage'),
    '1.2.840.10008.*******.1.501.6': ('DICOS Quadrupole Resonance (QR) Storage', 'SOP Class', 'DICOS', '', 'DICOSQuadrupoleResonanceStorage'),
    '1.2.840.10008.*******.1.601.1': ('Eddy Current Image Storage', 'SOP Class', 'DICONDE ASTM E2934', '', 'EddyCurrentImageStorage'),
    '1.2.840.10008.*******.1.601.2': ('Eddy Current Multi-frame Image Storage', 'SOP Class', 'DICONDE ASTM E2934', '', 'EddyCurrentMultiFrameImageStorage'),
    '1.2.840.10008.*******.1.601.3': ('Thermography Image Storage', 'SOP Class', 'DICONDE ASTM E3440', '', 'ThermographyImageStorage'),
    '1.2.840.10008.*******.1.601.4': ('Thermography Multi-frame Image Storage', 'SOP Class', 'DICONDE ASTM E3440', '', 'ThermographyMultiFrameImageStorage'),
    '1.2.840.10008.*******.2.1.1': ('Patient Root Query/Retrieve Information Model - FIND', 'SOP Class', '', '', 'PatientRootQueryRetrieveInformationModelFind'),
    '1.2.840.10008.*******.2.1.2': ('Patient Root Query/Retrieve Information Model - MOVE', 'SOP Class', '', '', 'PatientRootQueryRetrieveInformationModelMove'),
    '1.2.840.10008.*******.2.1.3': ('Patient Root Query/Retrieve Information Model - GET', 'SOP Class', '', '', 'PatientRootQueryRetrieveInformationModelGet'),
    '1.2.840.10008.*******.2.2.1': ('Study Root Query/Retrieve Information Model - FIND', 'SOP Class', '', '', 'StudyRootQueryRetrieveInformationModelFind'),
    '1.2.840.10008.*******.2.2.2': ('Study Root Query/Retrieve Information Model - MOVE', 'SOP Class', '', '', 'StudyRootQueryRetrieveInformationModelMove'),
    '1.2.840.10008.*******.2.2.3': ('Study Root Query/Retrieve Information Model - GET', 'SOP Class', '', '', 'StudyRootQueryRetrieveInformationModelGet'),
    '1.2.840.10008.*******.2.3.1': ('Patient/Study Only Query/Retrieve Information Model - FIND', 'SOP Class', '', 'Retired', 'PatientStudyOnlyQueryRetrieveInformationModelFind'),
    '1.2.840.10008.*******.2.3.2': ('Patient/Study Only Query/Retrieve Information Model - MOVE', 'SOP Class', '', 'Retired', 'PatientStudyOnlyQueryRetrieveInformationModelMove'),
    '1.2.840.10008.*******.2.3.3': ('Patient/Study Only Query/Retrieve Information Model - GET', 'SOP Class', '', 'Retired', 'PatientStudyOnlyQueryRetrieveInformationModelGet'),
    '1.2.840.10008.*******.2.4.2': ('Composite Instance Root Retrieve - MOVE', 'SOP Class', '', '', 'CompositeInstanceRootRetrieveMove'),
    '1.2.840.10008.*******.2.4.3': ('Composite Instance Root Retrieve - GET', 'SOP Class', '', '', 'CompositeInstanceRootRetrieveGet'),
    '1.2.840.10008.*******.2.5.3': ('Composite Instance Retrieve Without Bulk Data - GET', 'SOP Class', '', '', 'CompositeInstanceRetrieveWithoutBulkDataGet'),
    '1.2.840.10008.5.1.4.20.1': ('Defined Procedure Protocol Information Model - FIND', 'SOP Class', '', '', 'DefinedProcedureProtocolInformationModelFind'),
    '1.2.840.10008.5.1.4.20.2': ('Defined Procedure Protocol Information Model - MOVE', 'SOP Class', '', '', 'DefinedProcedureProtocolInformationModelMove'),
    '1.2.840.10008.5.1.4.20.3': ('Defined Procedure Protocol Information Model - GET', 'SOP Class', '', '', 'DefinedProcedureProtocolInformationModelGet'),
    '1.2.840.10008.5.1.4.31': ('Modality Worklist Information Model - FIND', 'SOP Class', '', '', 'ModalityWorklistInformationModelFind'),
    '1.2.840.10008.5.1.4.32': ('General Purpose Worklist Management Meta SOP Class', 'Meta SOP Class', '', 'Retired', 'GeneralPurposeWorklistManagementMeta'),
    '1.2.840.10008.5.1.4.32.1': ('General Purpose Worklist Information Model - FIND', 'SOP Class', '', 'Retired', 'GeneralPurposeWorklistInformationModelFind'),
    '1.2.840.10008.5.1.4.32.2': ('General Purpose Scheduled Procedure Step SOP Class', 'SOP Class', '', 'Retired', 'GeneralPurposeScheduledProcedureStep'),
    '1.2.840.10008.5.1.4.32.3': ('General Purpose Performed Procedure Step SOP Class', 'SOP Class', '', 'Retired', 'GeneralPurposePerformedProcedureStep'),
    '1.2.840.10008.5.1.4.33': ('Instance Availability Notification SOP Class', 'SOP Class', '', '', 'InstanceAvailabilityNotification'),
    '1.2.840.10008.********.1': ('RT Beams Delivery Instruction Storage - Trial', 'SOP Class', '', 'Retired', 'RTBeamsDeliveryInstructionStorageTrial'),
    '1.2.840.10008.********.2': ('RT Conventional Machine Verification - Trial', 'SOP Class', '', 'Retired', 'RTConventionalMachineVerificationTrial'),
    '1.2.840.10008.********.3': ('RT Ion Machine Verification - Trial', 'SOP Class', '', 'Retired', 'RTIonMachineVerificationTrial'),
    '1.2.840.10008.********.4': ('Unified Worklist and Procedure Step Service Class - Trial', 'Service Class', '', 'Retired', 'UnifiedWorklistAndProcedureStepTrial'),
    '1.2.840.10008.********.4.1': ('Unified Procedure Step - Push SOP Class - Trial', 'SOP Class', '', 'Retired', 'UnifiedProcedureStepPushTrial'),
    '1.2.840.10008.********.4.2': ('Unified Procedure Step - Watch SOP Class - Trial', 'SOP Class', '', 'Retired', 'UnifiedProcedureStepWatchTrial'),
    '1.2.840.10008.********.4.3': ('Unified Procedure Step - Pull SOP Class - Trial', 'SOP Class', '', 'Retired', 'UnifiedProcedureStepPullTrial'),
    '1.2.840.10008.********.4.4': ('Unified Procedure Step - Event SOP Class - Trial', 'SOP Class', '', 'Retired', 'UnifiedProcedureStepEventTrial'),
    '1.2.840.10008.********.5': ('UPS Global Subscription SOP Instance', 'Well-known SOP Instance', '', '', 'UPSGlobalSubscriptionInstance'),
    '1.2.840.10008.********.5.1': ('UPS Filtered Global Subscription SOP Instance', 'Well-known SOP Instance', '', '', 'UPSFilteredGlobalSubscriptionInstance'),
    '1.2.840.10008.********.6': ('Unified Worklist and Procedure Step Service Class', 'Service Class', '', '', 'UnifiedWorklistAndProcedureStep'),
    '1.2.840.10008.********.6.1': ('Unified Procedure Step - Push SOP Class', 'SOP Class', '', '', 'UnifiedProcedureStepPush'),
    '1.2.840.10008.********.6.2': ('Unified Procedure Step - Watch SOP Class', 'SOP Class', '', '', 'UnifiedProcedureStepWatch'),
    '1.2.840.10008.********.6.3': ('Unified Procedure Step - Pull SOP Class', 'SOP Class', '', '', 'UnifiedProcedureStepPull'),
    '1.2.840.10008.********.6.4': ('Unified Procedure Step - Event SOP Class', 'SOP Class', '', '', 'UnifiedProcedureStepEvent'),
    '1.2.840.10008.********.6.5': ('Unified Procedure Step - Query SOP Class', 'SOP Class', '', '', 'UnifiedProcedureStepQuery'),
    '1.2.840.10008.********.7': ('RT Beams Delivery Instruction Storage', 'SOP Class', '', '', 'RTBeamsDeliveryInstructionStorage'),
    '1.2.840.10008.********.8': ('RT Conventional Machine Verification', 'SOP Class', '', '', 'RTConventionalMachineVerification'),
    '1.2.840.10008.********.9': ('RT Ion Machine Verification', 'SOP Class', '', '', 'RTIonMachineVerification'),
    '1.2.840.10008.********.10': ('RT Brachy Application Setup Delivery Instruction Storage', 'SOP Class', '', '', 'RTBrachyApplicationSetupDeliveryInstructionStorage'),
    '1.2.840.10008.5.1.4.37.1': ('General Relevant Patient Information Query', 'SOP Class', '', '', 'GeneralRelevantPatientInformationQuery'),
    '1.2.840.10008.5.1.4.37.2': ('Breast Imaging Relevant Patient Information Query', 'SOP Class', '', '', 'BreastImagingRelevantPatientInformationQuery'),
    '1.2.840.10008.5.1.4.37.3': ('Cardiac Relevant Patient Information Query', 'SOP Class', '', '', 'CardiacRelevantPatientInformationQuery'),
    '1.2.840.10008.5.1.4.38.1': ('Hanging Protocol Storage', 'SOP Class', '', '', 'HangingProtocolStorage'),
    '1.2.840.10008.5.1.4.38.2': ('Hanging Protocol Information Model - FIND', 'SOP Class', '', '', 'HangingProtocolInformationModelFind'),
    '1.2.840.10008.5.1.4.38.3': ('Hanging Protocol Information Model - MOVE', 'SOP Class', '', '', 'HangingProtocolInformationModelMove'),
    '1.2.840.10008.5.1.4.38.4': ('Hanging Protocol Information Model - GET', 'SOP Class', '', '', 'HangingProtocolInformationModelGet'),
    '1.2.840.10008.5.1.4.39.1': ('Color Palette Storage', 'SOP Class', '', '', 'ColorPaletteStorage'),
    '1.2.840.10008.5.1.4.39.2': ('Color Palette Query/Retrieve Information Model - FIND', 'SOP Class', '', '', 'ColorPaletteQueryRetrieveInformationModelFind'),
    '1.2.840.10008.5.1.4.39.3': ('Color Palette Query/Retrieve Information Model - MOVE', 'SOP Class', '', '', 'ColorPaletteQueryRetrieveInformationModelMove'),
    '1.2.840.10008.5.1.4.39.4': ('Color Palette Query/Retrieve Information Model - GET', 'SOP Class', '', '', 'ColorPaletteQueryRetrieveInformationModelGet'),
    '1.2.840.10008.5.1.4.41': ('Product Characteristics Query SOP Class', 'SOP Class', '', '', 'ProductCharacteristicsQuery'),
    '1.2.840.10008.5.1.4.42': ('Substance Approval Query SOP Class', 'SOP Class', '', '', 'SubstanceApprovalQuery'),
    '1.2.840.10008.5.1.4.43.1': ('Generic Implant Template Storage', 'SOP Class', '', '', 'GenericImplantTemplateStorage'),
    '1.2.840.10008.5.1.4.43.2': ('Generic Implant Template Information Model - FIND', 'SOP Class', '', '', 'GenericImplantTemplateInformationModelFind'),
    '1.2.840.10008.5.1.4.43.3': ('Generic Implant Template Information Model - MOVE', 'SOP Class', '', '', 'GenericImplantTemplateInformationModelMove'),
    '1.2.840.10008.5.1.4.43.4': ('Generic Implant Template Information Model - GET', 'SOP Class', '', '', 'GenericImplantTemplateInformationModelGet'),
    '1.2.840.10008.5.1.4.44.1': ('Implant Assembly Template Storage', 'SOP Class', '', '', 'ImplantAssemblyTemplateStorage'),
    '1.2.840.10008.5.1.4.44.2': ('Implant Assembly Template Information Model - FIND', 'SOP Class', '', '', 'ImplantAssemblyTemplateInformationModelFind'),
    '1.2.840.10008.5.1.4.44.3': ('Implant Assembly Template Information Model - MOVE', 'SOP Class', '', '', 'ImplantAssemblyTemplateInformationModelMove'),
    '1.2.840.10008.5.1.4.44.4': ('Implant Assembly Template Information Model - GET', 'SOP Class', '', '', 'ImplantAssemblyTemplateInformationModelGet'),
    '1.2.840.10008.********.1': ('Implant Template Group Storage', 'SOP Class', '', '', 'ImplantTemplateGroupStorage'),
    '1.2.840.10008.********.2': ('Implant Template Group Information Model - FIND', 'SOP Class', '', '', 'ImplantTemplateGroupInformationModelFind'),
    '1.2.840.10008.********.3': ('Implant Template Group Information Model - MOVE', 'SOP Class', '', '', 'ImplantTemplateGroupInformationModelMove'),
    '1.2.840.10008.********.4': ('Implant Template Group Information Model - GET', 'SOP Class', '', '', 'ImplantTemplateGroupInformationModelGet'),
    '1.2.840.10008.7.1.1': ('Native DICOM Model', 'Application Hosting Model', '', '', 'NativeDICOMModel'),
    '1.2.840.10008.7.1.2': ('Abstract Multi-Dimensional Image Model', 'Application Hosting Model', '', '', 'AbstractMultiDimensionalImageModel'),
    '1.2.840.10008.8.1.1': ('DICOM Content Mapping Resource', 'Mapping Resource', '', '', 'DICOMContentMappingResource'),
    '1.2.840.10008.10.1': ('Video Endoscopic Image Real-Time Communication', 'SOP Class', '', '', 'VideoEndoscopicImageRealTimeCommunication'),
    '1.2.840.10008.10.2': ('Video Photographic Image Real-Time Communication', 'SOP Class', '', '', 'VideoPhotographicImageRealTimeCommunication'),
    '1.2.840.10008.10.3': ('Audio Waveform Real-Time Communication', 'SOP Class', '', '', 'AudioWaveformRealTimeCommunication'),
    '1.2.840.10008.10.4': ('Rendition Selection Document Real-Time Communication', 'SOP Class', '', '', 'RenditionSelectionDocumentRealTimeCommunication'),
    '1.2.840.10008.********': ('dicomDeviceName', 'LDAP OID', '', '', 'dicomDeviceName'),
    '1.2.840.10008.********': ('dicomDescription', 'LDAP OID', '', '', 'dicomDescription'),
    '1.2.840.10008.********': ('dicomManufacturer', 'LDAP OID', '', '', 'dicomManufacturer'),
    '1.2.840.10008.********': ('dicomManufacturerModelName', 'LDAP OID', '', '', 'dicomManufacturerModelName'),
    '1.2.840.10008.********': ('dicomSoftwareVersion', 'LDAP OID', '', '', 'dicomSoftwareVersion'),
    '1.2.840.10008.********': ('dicomVendorData', 'LDAP OID', '', '', 'dicomVendorData'),
    '1.2.840.10008.********': ('dicomAETitle', 'LDAP OID', '', '', 'dicomAETitle'),
    '1.2.840.10008.********': ('dicomNetworkConnectionReference', 'LDAP OID', '', '', 'dicomNetworkConnectionReference'),
    '1.2.840.10008.********': ('dicomApplicationCluster', 'LDAP OID', '', '', 'dicomApplicationCluster'),
    '1.2.840.10008.********0': ('dicomAssociationInitiator', 'LDAP OID', '', '', 'dicomAssociationInitiator'),
    '1.2.840.10008.********1': ('dicomAssociationAcceptor', 'LDAP OID', '', '', 'dicomAssociationAcceptor'),
    '1.2.840.10008.********2': ('dicomHostname', 'LDAP OID', '', '', 'dicomHostname'),
    '1.2.840.10008.********3': ('dicomPort', 'LDAP OID', '', '', 'dicomPort'),
    '1.2.840.10008.********4': ('dicomSOPClass', 'LDAP OID', '', '', 'dicomSOPClass'),
    '1.2.840.10008.********5': ('dicomTransferRole', 'LDAP OID', '', '', 'dicomTransferRole'),
    '1.2.840.10008.********6': ('dicomTransferSyntax', 'LDAP OID', '', '', 'dicomTransferSyntax'),
    '1.2.840.10008.********7': ('dicomPrimaryDeviceType', 'LDAP OID', '', '', 'dicomPrimaryDeviceType'),
    '1.2.840.10008.********8': ('dicomRelatedDeviceReference', 'LDAP OID', '', '', 'dicomRelatedDeviceReference'),
    '1.2.840.10008.********9': ('dicomPreferredCalledAETitle', 'LDAP OID', '', '', 'dicomPreferredCalledAETitle'),
    '1.2.840.10008.********0': ('dicomTLSCyphersuite', 'LDAP OID', '', '', 'dicomTLSCyphersuite'),
    '1.2.840.10008.********1': ('dicomAuthorizedNodeCertificateReference', 'LDAP OID', '', '', 'dicomAuthorizedNodeCertificateReference'),
    '1.2.840.10008.********2': ('dicomThisNodeCertificateReference', 'LDAP OID', '', '', 'dicomThisNodeCertificateReference'),
    '1.2.840.10008.********3': ('dicomInstalled', 'LDAP OID', '', '', 'dicomInstalled'),
    '1.2.840.10008.********4': ('dicomStationName', 'LDAP OID', '', '', 'dicomStationName'),
    '1.2.840.10008.********5': ('dicomDeviceSerialNumber', 'LDAP OID', '', '', 'dicomDeviceSerialNumber'),
    '1.2.840.10008.********6': ('dicomInstitutionName', 'LDAP OID', '', '', 'dicomInstitutionName'),
    '1.2.840.10008.********7': ('dicomInstitutionAddress', 'LDAP OID', '', '', 'dicomInstitutionAddress'),
    '1.2.840.10008.********8': ('dicomInstitutionDepartmentName', 'LDAP OID', '', '', 'dicomInstitutionDepartmentName'),
    '1.2.840.10008.********9': ('dicomIssuerOfPatientID', 'LDAP OID', '', '', 'dicomIssuerOfPatientID'),
    '1.2.840.10008.********0': ('dicomPreferredCallingAETitle', 'LDAP OID', '', '', 'dicomPreferredCallingAETitle'),
    '1.2.840.10008.********1': ('dicomSupportedCharacterSet', 'LDAP OID', '', '', 'dicomSupportedCharacterSet'),
    '1.2.840.10008.********': ('dicomConfigurationRoot', 'LDAP OID', '', '', 'dicomConfigurationRoot'),
    '1.2.840.10008.********': ('dicomDevicesRoot', 'LDAP OID', '', '', 'dicomDevicesRoot'),
    '1.2.840.10008.********': ('dicomUniqueAETitlesRegistryRoot', 'LDAP OID', '', '', 'dicomUniqueAETitlesRegistryRoot'),
    '1.2.840.10008.********': ('dicomDevice', 'LDAP OID', '', '', 'dicomDevice'),
    '1.2.840.10008.********': ('dicomNetworkAE', 'LDAP OID', '', '', 'dicomNetworkAE'),
    '1.2.840.10008.********': ('dicomNetworkConnection', 'LDAP OID', '', '', 'dicomNetworkConnection'),
    '1.2.840.10008.15.0.4.7': ('dicomUniqueAETitle', 'LDAP OID', '', '', 'dicomUniqueAETitle'),
    '1.2.840.10008.15.0.4.8': ('dicomTransferCapability', 'LDAP OID', '', '', 'dicomTransferCapability'),
    '1.2.840.10008.15.1.1': ('Universal Coordinated Time', 'Synchronization Frame of Reference', '', '', 'UTC'),
    '1.2.840.10008.1.4.1.1': ('Talairach Brain Atlas Frame of Reference', 'Well-known frame of reference', '', '', 'TalairachBrainAtlas'),
    '1.2.840.10008.1.4.1.2': ('SPM2 T1 Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2T1'),
    '1.2.840.10008.1.4.1.3': ('SPM2 T2 Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2T2'),
    '1.2.840.10008.1.4.1.4': ('SPM2 PD Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2PD'),
    '1.2.840.10008.1.4.1.5': ('SPM2 EPI Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2EPI'),
    '1.2.840.10008.1.4.1.6': ('SPM2 FIL T1 Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2FILT1'),
    '1.2.840.10008.1.4.1.7': ('SPM2 PET Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2PET'),
    '1.2.840.10008.1.4.1.8': ('SPM2 TRANSM Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2TRANSM'),
    '1.2.840.10008.1.4.1.9': ('SPM2 SPECT Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2SPECT'),
    '1.2.840.10008.1.4.1.10': ('SPM2 GRAY Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2GRAY'),
    '1.2.840.10008.1.4.1.11': ('SPM2 WHITE Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2WHITE'),
    '1.2.840.10008.1.4.1.12': ('SPM2 CSF Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2CSF'),
    '1.2.840.10008.1.4.1.13': ('SPM2 BRAINMASK Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2BRAINMASK'),
    '1.2.840.10008.1.4.1.14': ('SPM2 AVG305T1 Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2AVG305T1'),
    '1.2.840.10008.1.4.1.15': ('SPM2 AVG152T1 Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2AVG152T1'),
    '1.2.840.10008.1.4.1.16': ('SPM2 AVG152T2 Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2AVG152T2'),
    '1.2.840.10008.1.4.1.17': ('SPM2 AVG152PD Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2AVG152PD'),
    '1.2.840.10008.1.4.1.18': ('SPM2 SINGLESUBJT1 Frame of Reference', 'Well-known frame of reference', '', '', 'SPM2SINGLESUBJT1'),
    '1.2.840.10008.1.4.2.1': ('ICBM 452 T1 Frame of Reference', 'Well-known frame of reference', '', '', 'ICBM452T1'),
    '1.2.840.10008.1.4.2.2': ('ICBM Single Subject MRI Frame of Reference', 'Well-known frame of reference', '', '', 'ICBMSingleSubjectMRI'),
    '1.2.840.10008.1.4.3.1': ('IEC 61217 Fixed Coordinate System Frame of Reference', 'Well-known frame of reference', '', '', 'IEC61217FixedCoordinateSystem'),
    '1.2.840.10008.1.4.3.2': ('Standard Robotic-Arm Coordinate System Frame of Reference', 'Well-known frame of reference', '', '', 'StandardRoboticArmCoordinateSystem'),
    '1.2.840.10008.1.4.3.3': ('IEC 61217 Table Top Coordinate System Frame of Reference', 'Well-known frame of reference', '', '', 'IEC61217TableTopCoordinateSystem'),
    '1.2.840.10008.1.4.4.1': ('SRI24 Frame of Reference', 'Well-known frame of reference', '', '', 'SRI24'),
    '1.2.840.10008.1.4.5.1': ('Colin27 Frame of Reference', 'Well-known frame of reference', '', '', 'Colin27'),
    '1.2.840.10008.1.4.6.1': ('LPBA40/AIR Frame of Reference', 'Well-known frame of reference', '', '', 'LPBA40AIR'),
    '1.2.840.10008.1.4.6.2': ('LPBA40/FLIRT Frame of Reference', 'Well-known frame of reference', '', '', 'LPBA40FLIRT'),
    '1.2.840.10008.1.4.6.3': ('LPBA40/SPM5 Frame of Reference', 'Well-known frame of reference', '', '', 'LPBA40SPM5')
}
