# pyrt-dicom

![PyPI version](https://img.shields.io/pypi/v/pyrt_dicom.svg)
[![Documentation Status](https://readthedocs.org/projects/pyrt-dicom/badge/?version=latest)](https://pyrt-dicom.readthedocs.io/en/latest/?version=latest)

Complement existing tools (PyMedPhys for analysis, scikit-rt for research) by becoming the go-to library for DICOM RT file creation

* Free software: MIT License
* Documentation: https://pyrt-dicom.readthedocs.io.

## Features

* TODO

## Credits

This package was created with [<PERSON><PERSON>cutter](https://github.com/audreyfeldroy/cookiecutter) and the [audrey<PERSON>roy/cookiecutter-pypackage](https://github.com/audreyfeldroy/cookiecutter-pypackage) project template.

* This package builds upon the work of the [PyMedPhys](https://github.com/pymedphys/pymedphys) and [scikit-rt](https://github.com/scikit-rt/scikit-rt) projects, which provide a solid foundation in DICOM RT file analysis and manipulation.
