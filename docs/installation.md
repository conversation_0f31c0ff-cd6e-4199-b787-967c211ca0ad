# Installation

## Stable release

To install pyrt-dicom, run this command in your terminal:

```sh
pip install pyrt_dicom
```

This is the preferred method to install pyrt-dicom, as it will always install the most recent stable release.

If you don't have [pip](https://pip.pypa.io) installed, this [Python installation guide](http://docs.python-guide.org/en/latest/starting/installation/) can guide you through the process.

## From sources

The sources for pyrt-dicom can be downloaded from the [Github repo](https://github.com/srobertson86/pyrt_dicom).

You can either clone the public repository:

```sh
git clone git://github.com/srobertson86/pyrt_dicom
```

Or download the [tarball](https://github.com/srobertson86/pyrt_dicom/tarball/master):

```sh
curl -OJL https://github.com/srobertson86/pyrt_dicom/tarball/master
```

Once you have a copy of the source, you can install it with:

```sh
python setup.py install
```
