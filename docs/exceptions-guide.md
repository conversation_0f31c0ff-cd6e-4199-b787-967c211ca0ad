# Exception Handling in pyrt-dicom

This guide documents the exception handling patterns used throughout the pyrt-dicom project, including both standard and enhanced exception types.

## Table of Contents
- [Exception Hierarchy](#exception-hierarchy)
- [Standard Exceptions](#standard-exceptions)
- [Enhanced Exceptions](#enhanced-exceptions)
  - [Features](#enhanced-features)
  - [Available Enhanced Exception Types](#available-enhanced-exception-types)
- [Usage Examples](#usage-examples)
  - [Basic Usage](#basic-usage)
  - [Enhanced Usage](#enhanced-usage)
  - [Best Practices](#best-practices)
- [Migration Guide](#migration-guide)
- [Testing Exception Handling](#testing-exception-handling)

## Exception Hierarchy

```
Exception
└── PyrtDicomError (base class)
    ├── DicomCreationError
    ├── ValidationError
    ├── CoordinateSystemError
    ├── UIDGenerationError
    └── TemplateError
```

## Standard Exceptions

Standard exceptions provide basic error information with traditional Python exception patterns:

```python
raise DicomCreationError("Failed to create DICOM file: insufficient data")
```

## Enhanced Exceptions

Enhanced exceptions provide rich contextual information for better debugging and user guidance.

### Enhanced Features

1. **Actionable Suggestions** (`suggestions`): List of steps to resolve the issue
2. **Clinical Context** (`clinical_context`): Dictionary with relevant parameters
3. **DICOM References** (`dicom_reference`): References to DICOM standard sections
4. **Automatic Context Detection**: Falls back to simple behavior when enhanced features aren't used

### Available Enhanced Exception Types

1. **DicomCreationError**
   - For DICOM file creation issues
   - Handles missing elements, invalid data types, file I/O errors

2. **ValidationError**
   - For clinical/technical parameter validation
   - Supports range checking, unit validation, DICOM compliance

3. **CoordinateSystemError**
   - For spatial/geometric issues
   - Handles frame of reference mismatches, patient position conflicts

4. **UIDGenerationError**
   - For DICOM UID-related problems
   - Validates format, uniqueness, and relationships

5. **TemplateError**
   - For DICOM template/IOD structure issues
   - Validates required attributes and modality-specific constraints

## Usage Examples

### Basic Usage

```python
# Simple error (standard behavior)
try:
    validate_dose(600)  # cGy
except ValidationError as e:
    logger.error(f"Validation failed: {e}")
    raise
```

### Enhanced Usage

```python
# Enhanced error with clinical context
try:
    validate_dose(600)  # cGy
except ValidationError as e:
    raise ValidationError(
        "Dose value exceeds safe limits",
        parameter_name="DosePerFraction",
        current_value=600,
        valid_range=(100, 300),
        units="cGy",
        validation_type="clinical"
    ) from e
```

### Best Practices

1. **Always chain exceptions** using `from` to preserve the full error context:
   ```python
   try:
       # code that might fail
   except SomeError as e:
       raise EnhancedError("Context", ...) from e
   ```

2. **Use appropriate exception types** that best describe the error condition.

3. **Provide meaningful suggestions** that help resolve the issue.

4. **Include relevant DICOM references** when applicable.

5. **Keep clinical context** in a separate dictionary for better organization.

## Migration Guide

### From Standard to Enhanced Exceptions

1. **Identify error-prone areas** that would benefit from enhanced context.
2. **Add relevant parameters** to exception constructors.
3. **Update error handling** to use the enhanced features.
4. **Test thoroughly** to ensure backward compatibility.

### Example Migration

**Before:**
```python
if not is_valid_uid(uid):
    raise ValueError(f"Invalid UID format: {uid}")
```

**After:**
```python
if not is_valid_uid(uid):
    raise UIDGenerationError(
        f"Invalid UID format: {uid}",
        uid_value=uid,
        suggestions=[
            "UID must contain only numbers and periods",
            "Maximum length is 64 characters"
        ],
        clinical_context={
            'uid_format': "1.2.826.0.1.3680043.8.498.{random_numbers}",
            'max_length': 64
        },
        dicom_reference="PS 3.5, Chapter 9 (Unique Identifiers)"
    )
```

## Testing Exception Handling

When testing enhanced exceptions:

1. **Test both standard and enhanced** exception behavior.
2. **Verify context** is correctly preserved in exception chains.
3. **Check suggestions** are actionable and relevant.
4. **Validate DICOM references** are accurate and up-to-date.

Example test case:

```python
def test_validation_error():
    with pytest.raises(ValidationError) as exc_info:
        validate_dose(600)
    
    exc = exc_info.value
    assert 600 in str(exc)
    assert "DosePerFraction" in str(exc)
    assert exc.clinical_context['current_value'] == 600
    assert "suggestions" in exc.__dict__
```

## Conclusion

Enhanced exceptions in pyrt-dicom provide a powerful way to improve error reporting and debugging. By following the patterns and best practices outlined in this guide, you can create more maintainable and user-friendly error handling throughout the codebase.
