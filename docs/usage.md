# Usage

This document demonstrates the intended usage patterns for pyrt-dicom, focusing on creating radiotherapy DICOM files from common data sources.

## DICOM Coordinate System and Standards

### Understanding DICOM Patient Coordinate System

pyrt-dicom follows the DICOM standard for patient-based coordinates (LPS system):

```python
import pydicom
import numpy as np

# DICOM Patient Coordinate System (LPS):
# X-axis: increases from patient's Right (R) to Left (L)
# Y-axis: increases from patient's Anterior (A) to Posterior (P)  
# Z-axis: increases from patient's Inferior (I) to Superior (S)
#
# Key DICOM Tags for spatial information:
# - ImagePositionPatient (0020,0032): [x, y, z] of upper-left pixel in mm
# - ImageOrientationPatient (0020,0037): direction cosines of first row and column
# - PixelSpacing (0028,0030): [row spacing, column spacing] in mm
# - SliceThickness (0018,0050): spacing between slices in mm
# - FrameOfReferenceUID (0020,0052): unique identifier linking spatial coordinate systems

# Example: Reading spatial information from existing DICOM
ct_slice = pydicom.dcmread('/path/to/ct_slice.dcm')
print(f"Image Position (Patient): {ct_slice.ImagePositionPatient}")  # [x, y, z] in mm
print(f"Pixel Spacing: {ct_slice.PixelSpacing}")  # [row, col] spacing in mm
print(f"Slice Thickness: {ct_slice.SliceThickness}")  # mm
print(f"Frame of Reference UID: {ct_slice.FrameOfReferenceUID}")
print(f"Image Orientation (Patient): {ct_slice.ImageOrientationPatient}")  # Direction cosines
```

### Array Dimension Conventions

```python
# pyrt-dicom uses consistent array ordering: [slices, rows, columns] = [z, y, x]
# This matches the natural DICOM coordinate progression:
image_array = np.zeros((128, 256, 256))  # [z-slices, y-rows, x-columns]

# Pixel spacing corresponds to [row_spacing, column_spacing] = [y_spacing, x_spacing]
pixel_spacing = (1.0, 1.0)  # mm: (y-direction, x-direction)

# ImagePositionPatient is the real-world coordinate of the center of the 
# upper-left pixel (first row, first column) of the first slice
image_position_patient = (-128.0, -128.0, -100.0)  # (x, y, z) in mm LPS coordinates
```

## Quick Start

```python
import pyrt_dicom
from pyrt_dicom import CTSeries, RTStructureSet, RTDose, RTPlan
import pydicom  # Core DICOM library - pyrt-dicom builds on pydicom
import numpy as np
```

## Individual DICOM File Creation

### 1. Creating CT Series

Create a CT series from image data (e.g., from SimpleITK, nibabel, or numpy arrays):

```python
import numpy as np
import pydicom
from pyrt_dicom import CTSeries

# From numpy array (common from medical image processing)
# DICOM coordinate system: Patient-based coordinate system (LPS)
# X: increases from patient's right to left
# Y: increases from patient's anterior to posterior  
# Z: increases from patient's inferior to superior
ct_array = np.random.rand(128, 256, 256).astype(np.float32)  # [slices (z), rows (y), columns (x)]
pixel_spacing = (1.0, 1.0)  # mm (row spacing, column spacing) - DICOM (0028,0030)
slice_thickness = 2.5  # mm - DICOM (0018,0050)
# ImagePositionPatient (0020,0032): x, y, z coordinates of upper left pixel of first slice in mm
image_position_patient = (-128.0, -128.0, -100.0)  # mm (x, y, z) - DICOM LPS coordinates

ct_series = CTSeries.from_array(
    image_array=ct_array,
    pixel_spacing=pixel_spacing,
    slice_thickness=slice_thickness,
    image_position_patient=image_position_patient,
    patient_info={
        'PatientName': 'Doe^John',
        'PatientID': 'RT001',
        'PatientBirthDate': '19850315',
        'PatientSex': 'M'
    },
    study_description='RT Planning CT',
    series_description='Planning CT'
)

# Save CT series (creates multiple DICOM files)
ct_series.save('/path/to/output/ct_series/')

# Alternative: From SimpleITK image
import SimpleITK as sitk
ct_image = sitk.ReadImage('planning_ct.nii.gz')
ct_series = CTSeries.from_sitk_image(
    ct_image,
    patient_info={'PatientName': 'Doe^John', 'PatientID': 'RT001'}
)

# Alternative: From existing DICOM files using pydicom
# Load and modify existing CT series
ct_dicom = pydicom.dcmread('/path/to/existing/ct_slice_001.dcm')
print(f"Original Image Position: {ct_dicom.ImagePositionPatient}")
print(f"Pixel Spacing: {ct_dicom.PixelSpacing}")
print(f"Slice Thickness: {ct_dicom.SliceThickness}")

# Create new CT series from pydicom dataset
ct_series = CTSeries.from_pydicom_series(
    dicom_files=['/path/to/ct/slice_{:03d}.dcm'.format(i) for i in range(1, 129)],
    patient_info={'PatientName': 'Doe^John', 'PatientID': 'RT001'}
)
```

### 2. Creating RT Structure Set (RTSTRUCT)

Create structure sets from segmentation masks:

```python
import pydicom
from pyrt_dicom import RTStructureSet

# From binary masks (most common workflow)
# Mask dimensions must match CT array: [slices (z), rows (y), columns (x)]
masks = {
    'PTV_7000': np.random.rand(128, 256, 256) > 0.8,  # Planning Target Volume
    'CTV_7000': np.random.rand(128, 256, 256) > 0.9,  # Clinical Target Volume  
    'GTV': np.random.rand(128, 256, 256) > 0.95,      # Gross Target Volume
    'Heart': np.random.rand(128, 256, 256) > 0.85,    # Organ at Risk
    'Lung_L': np.random.rand(128, 256, 256) > 0.75,   # Left Lung
    'Lung_R': np.random.rand(128, 256, 256) > 0.75,   # Right Lung
}

# Create RTSTRUCT with reference to CT series
rt_struct = RTStructureSet.from_masks(
    masks=masks,
    reference_ct=ct_series,  # Links to CT for coordinate system
    colors={
        'PTV_7000': (255, 0, 0),     # Red
        'CTV_7000': (255, 128, 0),   # Orange
        'GTV': (255, 255, 0),        # Yellow
        'Heart': (255, 0, 255),      # Magenta
        'Lung_L': (0, 255, 255),     # Cyan
        'Lung_R': (0, 128, 255),     # Light Blue
    },
    roi_types={
        'PTV_7000': 'PTV',
        'CTV_7000': 'CTV', 
        'GTV': 'GTV',
        'Heart': 'ORGAN',
        'Lung_L': 'ORGAN',
        'Lung_R': 'ORGAN',
    }
)

rt_struct.save('/path/to/output/rtstruct.dcm')

# Alternative: Load existing RTSTRUCT and modify using pydicom
existing_rtstruct = pydicom.dcmread('/path/to/existing/rtstruct.dcm')
print(f"Existing ROIs: {[roi.ROIName for roi in existing_rtstruct.StructureSetROISequence]}")
print(f"Frame of Reference UID: {existing_rtstruct.ReferencedFrameOfReferenceSequence[0].FrameOfReferenceUID}")

# Create new RTSTRUCT referencing existing structure set
rt_struct = RTStructureSet.from_existing_rtstruct(
    existing_rtstruct,
    additional_masks={'NewROI': np.random.rand(128, 256, 256) > 0.9},
    reference_ct=ct_series
)

# Alternative: From contour points
contour_data = {
    'PTV_7000': {
        'slice_0': np.array([[10, 20], [15, 25], [20, 20], [15, 15]]),  # 2D points per slice
        'slice_1': np.array([[12, 22], [17, 27], [22, 22], [17, 17]]),
        # ... more slices
    }
}

rt_struct = RTStructureSet.from_contours(
    contour_data=contour_data,
    reference_ct=ct_series,
    slice_positions=[-100.0, -97.5, -95.0]  # Z positions in mm
)
```

### 3. Creating RT Dose (RTDOSE)

Create dose distributions from dose calculation engines:

```python
import pydicom
from pyrt_dicom import RTDose

# From 3D dose array (typical output from dose calculation)
# Dose array dimensions: [slices (z), rows (y), columns (x)] to match CT
dose_array = np.random.rand(64, 128, 128) * 80.0  # in Gy
# DICOM PixelSpacing (0028,0030): row spacing, column spacing
# DICOM SliceThickness (0018,0050): spacing between slices
dose_pixel_spacing = (2.0, 2.0)  # mm (row spacing, column spacing)
dose_slice_thickness = 2.5  # mm
# ImagePositionPatient (0020,0032): x, y, z coordinates of upper left pixel in mm
dose_grid_origin = (-64.0, -64.0, -100.0)  # mm (adjusted for dose grid resolution)

rt_dose = RTDose.from_array(
    dose_array=dose_array,
    reference_ct=ct_series,  # Links to CT for coordinate alignment
    dose_units='GY',
    dose_type='PHYSICAL',
    dose_grid_scaling=1e-4,  # Scaling factor for dose values
    grid_frame_offset_vector=(0.0, 0.0, 0.0),
    pixel_spacing=dose_pixel_spacing,
    slice_thickness=dose_slice_thickness,
    image_position_patient=dose_grid_origin,
    dose_summation_type='PLAN'
)

rt_dose.save('/path/to/output/rtdose.dcm')

# Alternative: Load and modify existing RTDOSE using pydicom
existing_dose = pydicom.dcmread('/path/to/existing/rtdose.dcm')
print(f"Dose Units: {existing_dose.DoseUnits}")
print(f"Dose Grid Scaling: {existing_dose.DoseGridScaling}")
print(f"Dose Array Shape: {existing_dose.pixel_array.shape}")
print(f"Referenced Plan UID: {existing_dose.ReferencedRTPlanSequence[0].ReferencedSOPInstanceUID}")

# Create new dose from existing with modifications
rt_dose = RTDose.from_pydicom_dose(
    existing_dose,
    scale_factor=1.05,  # Apply 5% dose scaling
    new_dose_comment='Scaled dose (+5%) for plan adaptation'
)

# Alternative: Create dose from multiple beam contributions
# Each beam dose array: [slices (z), rows (y), columns (x)]
beam_doses = {
    'beam_1': np.random.rand(64, 128, 128) * 40.0,  # Individual beam dose
    'beam_2': np.random.rand(64, 128, 128) * 40.0,
}

rt_dose = RTDose.from_beam_doses(
    beam_doses=beam_doses,
    reference_ct=ct_series,
    summation_type='BEAM'
)

# DVH-informed dose creation
rt_dose.add_dvh_constraints(
    structure_set=rt_struct,
    constraints={
        'PTV_7000': {'D95': 66.5, 'D02': 73.5},  # Gy
        'Heart': {'V30': 0.0, 'Mean': 5.0},      # % volume, Gy mean
    }
)
```

### 4. Creating RT Plan (RTPLAN)

Create treatment plans with beam configurations:

```python
import pydicom
from pyrt_dicom import RTPlan

# Define beam geometry and parameters
beam_config = {
    'beam_1': {
        'beam_name': 'RAO',
        'beam_number': 1,
        'beam_type': 'STATIC',
        'radiation_type': 'PHOTON',
        'beam_energy': 6.0,  # MV
        'gantry_angle': 45.0,  # degrees
        'collimator_angle': 0.0,
        'couch_angle': 0.0,
        'isocenter_position': (0.0, 0.0, -50.0),  # mm (x, y, z)
        'sad': 1000.0,  # mm (Source-to-Axis Distance)
        'mlc_positions': {
            'x1': [-50, -45, -40, -35],  # mm, leaf positions
            'x2': [50, 45, 40, 35],
        },
        'jaw_positions': {'x1': -60, 'x2': 60, 'y1': -80, 'y2': 80},  # mm
        'monitor_units': 150.0,
        'dose_rate': 600.0,  # cGy/min
    },
    'beam_2': {
        'beam_name': 'LAO', 
        'beam_number': 2,
        'beam_type': 'STATIC',
        'radiation_type': 'PHOTON',
        'beam_energy': 6.0,
        'gantry_angle': 315.0,
        'collimator_angle': 0.0,
        'couch_angle': 0.0,
        'isocenter_position': (0.0, 0.0, -50.0),
        'sad': 1000.0,
        'mlc_positions': {
            'x1': [-45, -40, -35, -30],
            'x2': [45, 40, 35, 30],
        },
        'jaw_positions': {'x1': -55, 'x2': 55, 'y1': -75, 'y2': 75},
        'monitor_units': 150.0,
        'dose_rate': 600.0,
    }
}

# Prescription information
prescription = {
    'prescribed_dose': 70.0,  # Gy
    'target_roi': 'PTV_7000',
    'fractions': 35,
    'dose_per_fraction': 2.0,  # Gy
}

rt_plan = RTPlan.from_beam_config(
    beam_config=beam_config,
    prescription=prescription,
    reference_ct=ct_series,
    structure_set=rt_struct,
    plan_name='7000cGy in 35fx',
    plan_description='IMRT treatment plan',
    treatment_machine='TrueBeam_STx',
    treatment_technique='IMRT'
)

rt_plan.save('/path/to/output/rtplan.dcm')

# Alternative: Load and analyze existing RTPLAN using pydicom
existing_plan = pydicom.dcmread('/path/to/existing/rtplan.dcm')
print(f"Plan Name: {existing_plan.RTPlanName}")
print(f"Treatment Machine: {existing_plan.BeamSequence[0].TreatmentMachineName}")
print(f"Number of Beams: {len(existing_plan.BeamSequence)}")
print(f"Prescribed Dose: {existing_plan.DoseReferenceSequence[0].TargetPrescriptionDose} cGy")

# Create new plan based on existing template
rt_plan = RTPlan.from_pydicom_template(
    template_plan=existing_plan,
    new_beam_config=beam_config,
    plan_modifications={
        'RTPlanName': 'Modified Plan v2.0',
        'RTPlanDescription': 'Adapted treatment plan'
    }
)

# Alternative: VMAT plan
vmat_beam = {
    'beam_1': {
        'beam_name': 'VMAT_CW',
        'beam_type': 'DYNAMIC',
        'radiation_type': 'PHOTON', 
        'beam_energy': 6.0,
        'start_gantry_angle': 179.0,
        'stop_gantry_angle': 181.0,
        'gantry_rotation_direction': 'CW',
        'collimator_angle': 30.0,
        'arc_length': 358.0,
        'control_points': [
            # Each control point defines MLC positions, dose rate, gantry position
            {'gantry_angle': 179.0, 'dose_rate': 600.0, 'cumulative_mu': 0.0},
            {'gantry_angle': 270.0, 'dose_rate': 600.0, 'cumulative_mu': 75.0},
            {'gantry_angle': 181.0, 'dose_rate': 200.0, 'cumulative_mu': 150.0},
        ]
    }
}
```

## Linking DICOM Files (DICOM Standard References)

### Complete Treatment Planning Workflow

```python
from pyrt_dicom import TreatmentPlan

# Create a complete, linked treatment plan
treatment_plan = TreatmentPlan.create_complete_plan(
    ct_data=ct_array,
    structures=masks,
    dose_data=dose_array,
    beam_config=beam_config,
    prescription=prescription,
    patient_info={
        'PatientName': 'Doe^John',
        'PatientID': 'RT001',
        'PatientBirthDate': '19850315',
        'PatientSex': 'M'
    },
    study_info={
        'StudyInstanceUID': '*******.*******.9.10.11',
        'StudyDescription': 'RT Planning Study',
        'StudyDate': '20241201',
        'StudyTime': '120000'
    }
)

# Save all linked files to directory
treatment_plan.save_all('/path/to/output/treatment_plan/')

# Individual access to linked components
ct_series = treatment_plan.ct_series
rt_struct = treatment_plan.rt_structure_set  
rt_dose = treatment_plan.rt_dose
rt_plan = treatment_plan.rt_plan

# Verify DICOM references and linkages
linkage_report = treatment_plan.validate_dicom_references()
print(linkage_report)
```

### Manual DICOM Reference Management

```python
# Explicit reference management following DICOM standard
from pyrt_dicom.references import DicomReferenceManager

# Create reference manager for UID consistency
ref_manager = DicomReferenceManager()

# Step 1: Create CT with managed UIDs
ct_series = CTSeries.from_array(
    image_array=ct_array,
    pixel_spacing=(1.0, 1.0),
    slice_thickness=2.5,
    reference_manager=ref_manager  # Ensures consistent UIDs
)

# Step 2: Create RTSTRUCT that references CT
rt_struct = RTStructureSet.from_masks(
    masks=masks,
    referenced_ct=ct_series,  # Establishes ReferencedFrameOfReferenceUID
    reference_manager=ref_manager
)

# Step 3: Create RTDOSE that references both CT and RTSTRUCT  
rt_dose = RTDose.from_array(
    dose_array=dose_array,
    referenced_ct=ct_series,      # ReferencedImageSequence
    referenced_rt_plan=None,      # Will be set later
    reference_manager=ref_manager
)

# Step 4: Create RTPLAN that references CT, RTSTRUCT, and RTDOSE
rt_plan = RTPlan.from_beam_config(
    beam_config=beam_config,
    prescription=prescription,
    referenced_ct=ct_series,           # ReferencedImageSequence
    referenced_structure_set=rt_struct, # ReferencedStructureSetSequence  
    referenced_dose=rt_dose,           # ReferencedDoseSequence
    reference_manager=ref_manager
)

# Update RTDOSE to reference the RTPLAN (circular reference)
rt_dose.add_referenced_rt_plan(rt_plan)

# Validate all DICOM references
validation_results = ref_manager.validate_references()
if not validation_results.is_valid:
    print("Reference validation errors:")
    for error in validation_results.errors:
        print(f"  - {error}")
```

### Working with Existing DICOM Files

```python
import pydicom
from pyrt_dicom import DicomLoader

# Load existing DICOM files using pydicom and DicomLoader
ct_dicom_files = [pydicom.dcmread(f'/path/to/existing/ct/slice_{i:03d}.dcm') 
                  for i in range(1, 129)]
rtstruct_dicom = pydicom.dcmread('/path/to/existing/rtstruct.dcm')
rtdose_dicom = pydicom.dcmread('/path/to/existing/rtdose.dcm')

# Verify DICOM tags and relationships
print(f"CT Frame of Reference UID: {ct_dicom_files[0].FrameOfReferenceUID}")
print(f"RTSTRUCT Frame of Reference UID: {rtstruct_dicom.ReferencedFrameOfReferenceSequence[0].FrameOfReferenceUID}")
print(f"RTDOSE Referenced Image UID: {rtdose_dicom.ReferencedImageSequence[0].ReferencedSOPInstanceUID}")

# Load using DicomLoader (pyrt-dicom wrapper)
existing_ct = DicomLoader.load_ct_series('/path/to/existing/ct/')
existing_struct = DicomLoader.load_rt_structure_set('/path/to/existing/rtstruct.dcm')
existing_dose = DicomLoader.load_rt_dose('/path/to/existing/rtdose.dcm')

# Create new RTDOSE that references existing files
# Ensure dose array matches coordinate system of reference CT
recalculated_dose = np.random.rand(64, 128, 128) * 75.0  # [slices, rows, columns]

new_dose = RTDose.from_array(
    dose_array=recalculated_dose,
    referenced_ct=existing_ct,  # Inherits coordinate system and Frame of Reference
    referenced_structure_set=existing_struct,
    dose_comment='Recalculated with new algorithm v2.1',
    # Automatically inherits ImagePositionPatient and coordinate system from CT
    pixel_spacing=existing_ct.pixel_spacing,  # Match CT resolution
    slice_thickness=existing_ct.slice_thickness
)

# Verify compatibility and save
compatibility_check = new_dose.check_reference_compatibility()
if compatibility_check.is_compatible:
    new_dose.save('/path/to/output/new_rtdose.dcm')
else:
    print("Compatibility issues found:")
    for issue in compatibility_check.issues:
        print(f"  - {issue}")
```

## DICOM Anonymization and Privacy Protection

### Basic Anonymization

```python
import pydicom
from pyrt_dicom.anonymization import DicomAnonymizer, AnonymizationProfiles

# Create anonymizer with default profile
anonymizer = DicomAnonymizer(profile='basic')

# Anonymize individual DICOM file
original_ct = pydicom.dcmread('/path/to/original/ct_slice.dcm')
print(f"Original Patient: {original_ct.PatientName}")
print(f"Original Patient ID: {original_ct.PatientID}")
print(f"Original Study Date: {original_ct.StudyDate}")

# Apply anonymization
anonymized_ct = anonymizer.anonymize_dataset(original_ct)
print(f"Anonymized Patient: {anonymized_ct.PatientName}")  # "ANONYMOUS^ANONYMOUS"
print(f"Anonymized Patient ID: {anonymized_ct.PatientID}")  # Generated ID
print(f"Anonymized Study Date: {anonymized_ct.StudyDate}")  # Removed or shifted

# Save anonymized version
anonymized_ct.save_as('/path/to/anonymized/ct_slice.dcm')
```

### Anonymization Profiles

```python
from pyrt_dicom.anonymization import DicomAnonymizer

# Different anonymization levels for various use cases

# Basic Profile: Remove all identifiers, preserve clinical data
basic_anonymizer = DicomAnonymizer(profile='basic')
basic_config = {
    'remove_patient_identifiers': True,
    'remove_institution_info': True,
    'preserve_dates': False,  # Remove all dates
    'preserve_geometric_data': True,  # Keep spatial information
    'preserve_dose_data': True,  # Keep dose values
    'preserve_physician_info': False
}

# Clinical Profile: Preserve relative dates for longitudinal studies
clinical_anonymizer = DicomAnonymizer(profile='clinical')
clinical_config = {
    'remove_patient_identifiers': True,
    'preserve_dates': True,  # Shift dates but preserve intervals
    'preserve_geometric_data': True,
    'preserve_dose_data': True,
    'preserve_physician_info': False,
    'date_shift_days': 365  # Shift all dates by 1 year
}

# Research Profile: Maximum data preservation for analysis
research_anonymizer = DicomAnonymizer(profile='research')
research_config = {
    'remove_patient_identifiers': True,
    'preserve_dates': True,
    'preserve_geometric_data': True,
    'preserve_dose_data': True,
    'preserve_institution_info': False,  # Remove for multi-site studies
    'anonymize_study_descriptions': True,  # Generalize study descriptions
    'preserve_age_at_study': True  # Keep age for demographic analysis
}

# Custom Profile: Define specific anonymization rules
custom_anonymizer = DicomAnonymizer(profile='custom')
custom_config = {
    'remove_tags': ['PatientName', 'PatientID', 'PatientBirthDate'],
    'keep_tags': ['PatientSex', 'PatientAge'],  # Keep for analysis
    'replace_tags': {
        'InstitutionName': 'RESEARCH_SITE_001',
        'StudyDescription': 'ANONYMIZED_RT_STUDY'
    },
    'shift_dates': True,
    'date_shift_days': 1000,  # Large shift for extra privacy
    'preserve_uid_relationships': True  # Critical for RT data integrity
}
```

### Complete Treatment Plan Anonymization

```python
from pyrt_dicom.anonymization import TreatmentPlanAnonymizer
import pydicom

# Load complete treatment plan (CT, RTSTRUCT, RTDOSE, RTPLAN)
original_files = {
    'ct_series': [f'/path/to/ct/slice_{i:03d}.dcm' for i in range(1, 129)],
    'rtstruct': '/path/to/original/rtstruct.dcm',
    'rtdose': '/path/to/original/rtdose.dcm',
    'rtplan': '/path/to/original/rtplan.dcm'
}

# Create treatment plan anonymizer
plan_anonymizer = TreatmentPlanAnonymizer(profile='clinical')

# Anonymize complete treatment plan while preserving relationships
anonymized_plan = plan_anonymizer.anonymize_treatment_plan(
    original_files,
    output_directory='/path/to/anonymized/plan/',
    patient_id_mapping='ANON_PT_001',  # Consistent ID across files
    preserve_structure_names=True,  # Keep ROI names for analysis
    preserve_beam_names=True  # Keep beam configuration names
)

# Verify anonymization while preserving DICOM references
print("Anonymization Summary:")
print(f"Patient ID: {anonymized_plan['patient_id']}")
print(f"Study UID: {anonymized_plan['study_uid']}")
print(f"Frame of Reference UID: {anonymized_plan['frame_of_reference_uid']}")
print(f"Files created: {len(anonymized_plan['output_files'])}")

# Verify DICOM reference integrity
verification = plan_anonymizer.verify_reference_integrity(anonymized_plan['output_files'])
if verification['is_valid']:
    print("✅ DICOM references preserved after anonymization")
else:
    print("❌ Reference integrity issues found:")
    for issue in verification['issues']:
        print(f"  - {issue}")
```

### Batch Anonymization for Research Datasets

```python
from pyrt_dicom.anonymization import BatchAnonymizer
import pandas as pd

# Batch anonymization for multiple patients
batch_anonymizer = BatchAnonymizer(profile='research')

# Define patient dataset
patient_dataset = [
    {
        'patient_id': 'PT001',
        'files': {
            'ct': '/data/patient_001/ct/',
            'struct': '/data/patient_001/rtstruct.dcm',
            'dose': '/data/patient_001/rtdose.dcm',
            'plan': '/data/patient_001/rtplan.dcm'
        },
        'output_dir': '/anonymized_data/anon_pt_001/'
    },
    {
        'patient_id': 'PT002', 
        'files': {
            'ct': '/data/patient_002/ct/',
            'struct': '/data/patient_002/rtstruct.dcm',
            'dose': '/data/patient_002/rtdose.dcm',
            'plan': '/data/patient_002/rtplan.dcm'
        },
        'output_dir': '/anonymized_data/anon_pt_002/'
    }
]

# Execute batch anonymization
results = batch_anonymizer.anonymize_batch(
    patient_dataset,
    anonymization_config={
        'profile': 'research',
        'preserve_structure_names': True,
        'date_shift_range': (200, 400),  # Random shift between 200-400 days
        'generate_mapping_file': True  # Create anonymization mapping file
    }
)

# Generate anonymization report
report = batch_anonymizer.generate_report(results)
print(f"Processed {report['total_patients']} patients")
print(f"Total files anonymized: {report['total_files']}")
print(f"Success rate: {report['success_rate']:.1%}")

# Save anonymization mapping for audit purposes
mapping_df = pd.DataFrame(report['patient_mapping'])
mapping_df.to_csv('/anonymized_data/anonymization_mapping.csv', index=False)
```

### HIPAA-Compliant Anonymization

```python
from pyrt_dicom.anonymization import HIPAAAnonymizer

# HIPAA-compliant anonymization following Safe Harbor method
hipaa_anonymizer = HIPAAAnonymizer()

# HIPAA Safe Harbor identifiers removal
hipaa_config = {
    'remove_identifiers': [
        'PatientName', 'PatientID', 'PatientBirthDate',
        'InstitutionName', 'InstitutionAddress', 'PhysicianName',
        'OperatorName', 'PerformingPhysicianName', 'ReferringPhysicianName'
    ],
    'geographic_subdivision': True,  # Remove ZIP codes beyond 3 digits
    'age_aggregation': True,  # Age > 89 becomes "90+"
    'date_handling': 'remove_all',  # Remove all dates except year
    'uid_anonymization': True,  # Replace all UIDs with anonymized versions
    'audit_logging': True  # Required for compliance
}

# Apply HIPAA anonymization
hipaa_result = hipaa_anonymizer.anonymize_dataset(
    original_dataset=original_ct,
    config=hipaa_config,
    audit_info={
        'operator': 'research_coordinator_001',
        'purpose': 'multi_site_research_study',
        'timestamp': '2024-01-15T10:30:00Z'
    }
)

# Verify HIPAA compliance
compliance_check = hipaa_anonymizer.verify_compliance(hipaa_result['anonymized_dataset'])
print(f"HIPAA Compliance: {compliance_check['is_compliant']}")
if not compliance_check['is_compliant']:
    print("Compliance issues found:")
    for issue in compliance_check['issues']:
        print(f"  - {issue}")

# Generate audit log entry
audit_entry = hipaa_anonymizer.create_audit_log(hipaa_result)
print(f"Audit Log: {audit_entry['log_id']}")
print(f"Operation: {audit_entry['operation']}")
print(f"Timestamp: {audit_entry['timestamp']}")
```

### Anonymization with pyrt-dicom Objects

```python
from pyrt_dicom import CTSeries, RTStructureSet, RTDose, RTPlan
from pyrt_dicom.anonymization import DicomAnonymizer

# Create original RT objects
ct_series = CTSeries.from_array(
    image_array=ct_array,
    patient_info={
        'PatientName': 'Doe^John',
        'PatientID': 'RT001',
        'PatientBirthDate': '19850315'
    }
)

rt_struct = RTStructureSet.from_masks(
    masks=masks,
    reference_ct=ct_series
)

# Anonymize during creation
anonymizer = DicomAnonymizer(profile='clinical')

# Method 1: Anonymize during save
ct_series.save('/path/to/ct_series/', anonymize=True, anonymization_profile='clinical')
rt_struct.save('/path/to/rtstruct.dcm', anonymize=True, anonymization_profile='clinical')

# Method 2: Anonymize existing objects
anonymized_ct = anonymizer.anonymize_pyrt_object(ct_series)
anonymized_struct = anonymizer.anonymize_pyrt_object(rt_struct)

# Method 3: Anonymize complete treatment plan
treatment_plan = TreatmentPlan.create_complete_plan(
    ct_data=ct_array,
    structures=masks,
    dose_data=dose_array,
    beam_config=beam_config,
    patient_info={
        'PatientName': 'Smith^Jane',
        'PatientID': 'RT002'
    }
)

# Anonymize complete plan
anonymized_plan = anonymizer.anonymize_treatment_plan(
    treatment_plan,
    profile='research',
    preserve_clinical_data=True
)

# Save anonymized plan
anonymized_plan.save_all('/path/to/anonymized_plan/')
```

### Audit Logging and Compliance

```python
from pyrt_dicom.anonymization import AnonymizationAuditor

# Set up audit logging for compliance requirements
auditor = AnonymizationAuditor(
    log_file='/secure/logs/dicom_anonymization.log',
    encryption_key='/secure/keys/audit.key'
)

# Anonymize with audit logging
anonymizer = DicomAnonymizer(
    profile='clinical',
    auditor=auditor
)

result = anonymizer.anonymize_dataset(
    dataset=original_dataset,
    audit_metadata={
        'operator_id': 'research_coord_001',
        'study_protocol': 'IRB_2024_001',
        'purpose': 'multi_site_outcomes_research',
        'data_destination': 'central_research_database'
    }
)

# Query audit logs
audit_reports = auditor.generate_compliance_report(
    date_range=('2024-01-01', '2024-12-31'),
    operator_filter='research_coord_001'
)

print(f"Total anonymization operations: {audit_reports['total_operations']}")
print(f"Files processed: {audit_reports['files_processed']}")
print(f"Compliance violations: {audit_reports['violations']}")

# Export audit report for compliance review
audit_reports.export_to_csv('/compliance/annual_anonymization_report_2024.csv')
```

## Advanced Usage Patterns

### Batch Processing Multiple Patients

```python
from pyrt_dicom.batch import BatchProcessor

# Process multiple patients with consistent settings
batch_config = {
    'ct_settings': {'pixel_spacing': (1.0, 1.0), 'slice_thickness': 2.5},
    'struct_settings': {'default_colors': True, 'roi_generation_algorithm': 'AUTOMATIC'},
    'dose_settings': {'dose_units': 'GY', 'dose_type': 'PHYSICAL'},
    'plan_settings': {'treatment_machine': 'TrueBeam_STx'}
}

processor = BatchProcessor(batch_config)

patient_data = [
    {
        'patient_id': 'RT001',
        'ct_path': '/data/patient_001/ct.nii.gz',
        'masks_path': '/data/patient_001/masks/',
        'dose_path': '/data/patient_001/dose.nii.gz',
        'output_path': '/output/patient_001/'
    },
    # ... more patients
]

results = processor.process_batch(patient_data)
```

### Integration with Medical Image Processing Libraries

```python
# Integration with PyMedPhys
import pymedphys
from pyrt_dicom.integrations import PyMedPhysAdapter

# Load dose from PyMedPhys and convert to pyrt-dicom
pymedphys_dose = pymedphys.Dose.from_file('existing_dose.dcm')
rt_dose = PyMedPhysAdapter.from_pymedphys_dose(pymedphys_dose)

# Integration with scikit-rt (future)
import skrt
from pyrt_dicom.integrations import SkrtAdapter

skrt_image = skrt.Image('planning_ct.nii.gz')
ct_series = SkrtAdapter.from_skrt_image(skrt_image)
```

### Quality Assurance and Validation

```python
from pyrt_dicom.validation import ClinicalValidator, DicomValidator

# Clinical validation (dose coverage, organ constraints)
clinical_validator = ClinicalValidator()
clinical_results = clinical_validator.validate_plan(
    rt_plan=rt_plan,
    rt_dose=rt_dose, 
    rt_struct=rt_struct,
    clinical_protocol='RTOG_0617'  # Predefined clinical protocols
)

# DICOM conformance validation
dicom_validator = DicomValidator()
dicom_results = dicom_validator.validate_conformance(
    [ct_series, rt_struct, rt_dose, rt_plan],
    iod_validation=True,  # Information Object Definition validation
    relationship_validation=True  # Cross-reference validation
)

# Generate comprehensive report
qa_report = {
    'clinical': clinical_results,
    'dicom': dicom_results,
    'timestamp': '2024-12-01T12:00:00Z'
}
```

## Error Handling and Debugging

```python
from pyrt_dicom.exceptions import (
    DicomCreationError,
    ReferenceValidationError,
    ClinicalValidationError
)

try:
    rt_struct = RTStructureSet.from_masks(
        masks=invalid_masks,  # Missing required mask data
        reference_ct=ct_series
    )
except DicomCreationError as e:
    print(f"DICOM creation failed: {e}")
    print(f"Suggested fixes: {e.suggestions}")
    
except ReferenceValidationError as e:
    print(f"Reference validation failed: {e}")
    print(f"Invalid references: {e.invalid_references}")

# Enable detailed debugging
import logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger('pyrt_dicom')

# This will show detailed DICOM creation process
rt_dose = RTDose.from_array(dose_array, reference_ct=ct_series)
```

## DICOM Standard Compliance

### Key DICOM Tags Used by pyrt-dicom

```python
import pydicom

# Essential DICOM tags for RT objects (automatically handled by pyrt-dicom):

# Spatial Coordinate Tags:
# (0020,0032) ImagePositionPatient: Real-world coordinates of pixel (1,1)
# (0020,0037) ImageOrientationPatient: Direction cosines for row and column directions  
# (0028,0030) PixelSpacing: Physical spacing between pixels [row, col] in mm
# (0018,0050) SliceThickness: Spacing between slices in mm
# (0020,0052) FrameOfReferenceUID: Spatial coordinate system identifier

# Reference Relationship Tags:
# (3006,0010) ReferencedFrameOfReferenceSequence: Links structures to image coordinate system
# (3006,0012) RTReferencedStudySequence: References to related studies
# (3006,0014) RTReferencedSeriesSequence: References to related image series
# (300A,0070) FractionGroupSequence: Treatment fraction information
# (300C,0002) ReferencedRTPlanSequence: Links dose to treatment plan

# Patient and Study Organization Tags:
# (0010,0010) PatientName: Patient identification
# (0010,0020) PatientID: Primary patient identifier  
# (0020,000D) StudyInstanceUID: Unique study identifier
# (0020,000E) SeriesInstanceUID: Unique series identifier
# (0008,0018) SOPInstanceUID: Unique instance identifier

# Example: Inspecting DICOM tag compliance
def validate_dicom_tags(dicom_file):
    ds = pydicom.dcmread(dicom_file)
    
    # Check spatial tags
    required_spatial = [
        'ImagePositionPatient', 'PixelSpacing', 'SliceThickness',
        'FrameOfReferenceUID', 'ImageOrientationPatient'
    ]
    
    for tag in required_spatial:
        if hasattr(ds, tag):
            print(f"✓ {tag}: {getattr(ds, tag)}")
        else:
            print(f"✗ Missing required tag: {tag}")
    
    return ds

# Validate created DICOM files
validate_dicom_tags('/path/to/created/rtdose.dcm')
```

This usage documentation demonstrates the intended API design for pyrt-dicom, focusing on clinical workflows and proper DICOM reference management. The actual implementation will follow these patterns to ensure intuitive usage for medical physicists and researchers while maintaining strict DICOM standard compliance.